repositories {
    mavenCentral()
}


def getWorkingBranch() {
    // Triple double-quotes for the break lines
    def workingBranch = """git rev-parse --abbrev-ref HEAD""".execute().text.trim()
    println "#Working branch: " + workingBranch
    return workingBranch
}

task jenkins {
    doLast {
        new URL('http://**************:8080/buildByToken/buildWithParameters?job=Job-Name&token=JENKINS_TOKEN&branch=' +
                getWorkingBranch()).text
        println '#<PERSON> started running \\,,/(^_^)\\,,/ check it out here: http://**************:8080/job/Job-Name/'
    }
}
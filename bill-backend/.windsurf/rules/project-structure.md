---
trigger: model_decision
description: when a new chat session is started
globs: 
---

### Project Setup

## Quick Start

This is a Spring Boot 2.4.5 application following [Clean Architecture](https://8thlight.com/blog/uncle-bob/2012/08/13/the-clean-architecture.html) principles. The application requires Java 11 and uses <PERSON>rad<PERSON> as the build tool.

**Prerequisites:**
- Java 11
- Docker (for database testing)
- PostgreSQL (for local development)

**Start the application:**
```bash
./gradlew
# OR
./gradlew bootRun
```

## Build & Run Commands

### Development Commands
```bash
# Run application with default profile
./gradlew bootRun

# Run with specific Spring profile
./gradlew bootRun -Dspring.profiles.active=secrets

# Run with specific environment build
./gradlew bootRun -Penvironment=local

# Clean and build
./gradlew clean build

# Build Docker image
./gradlew docker
```

### Version Management
The application version is managed in `build.gradle` and automatically updated in `application.yml`:
- Major version: `0`
- Minor version: `78` (increment for releases)
- Build type: `RELEASE` or `SNAPSHOT`

## Testing Strategy

### Test Organization by Module

**Domain Module Tests** (`./gradlew domain:test`)
- Pure business logic testing with 100% mock dependencies
- No external systems or databases
- Focus on entities and use cases

**Data Module Tests** (`./gradlew data:test`)
- Tests repository implementations with real PostgreSQL database
- Uses `docker-compose.test.yml` automatically via `dockerCompose` plugin
- Tests CRUD operations and complex queries
- Includes Flyway migration testing

**Network Module Tests** (`./gradlew network:test`)
- Tests external service integrations (HTTP, Kafka)
- Contract testing with real service calls where appropriate
- Uses Retrofit for HTTP clients

**API Module Tests** (`./gradlew api:test`)
- MockMVC tests focusing on payload validation
- Controller endpoint testing
- Swagger documentation testing

**Functional Tests** (`./gradlew test`)
- End-to-end testing of complete workflows
- Uses Rest Assured for HTTP endpoint testing
- Located in `src/test/java` at root level

### Running Tests
```bash
# All tests
./gradlew test

# Specific module tests
./gradlew domain:test
./gradlew data:test
./gradlew network:test
./gradlew api:test

# Run tests with Docker services
./gradlew test  # Docker services start automatically

# Skip quality checks during testing
./gradlew test -PignoreQualityFailures=true
```

## Clean Architecture Structure

### Module Dependencies (Inward Only)
```
┌─────────────────────────────────────────┐
│  API (Controllers, Payloads)            │
├─────────────────────────────────────────┤
│  NETWORK (HTTP, Kafka, External APIs)   │
├─────────────────────────────────────────┤
│  DATA (Repositories, Flyway, QueryDSL)  │
├─────────────────────────────────────────┤
│  DOMAIN (Entities, Use Cases)           │  <- Core Business Logic
└─────────────────────────────────────────┘
```

### Key Components

**Domain** (`domain/`)
- Business entities and use cases
- Repository interfaces (implemented by Data layer)
- ACL integration and properties configuration
- No external dependencies (only Spring context for DI)

**Data** (`data/`)
- PostgreSQL repository implementations
- Flyway database migrations
- JPA entities with Hibernate + QueryDSL
- Audit trail with Envers
- Connection pooling with HikariCP

**Network** (`network/`)
- Retrofit-based HTTP clients
- Kafka producers and consumers
- ACL Migrator integration
- External service interfaces (Judge, Communications, Taxi)

**API** (`api/`)
- REST controllers and payloads
- Input validation
- Swagger documentation
- JWT token processing

**Root** (`src/`)
- Main application class (`BillApplication.java`)
- Integration and functional tests

## Configuration & Secrets

### Application Configuration
- **Main config:** `src/main/resources/application.yml`
- **Test config:** `src/test/resources/application-test.yml`
- **Environment-specific:** `config/build-local.gradle`, `config/build-server.gradle`

### Secret Management
1. Copy `application-secrets.yml.template` to `application-secrets.yml`
2. Replace placeholder values with actual secrets
3. Run with secrets profile: `./gradlew bootRun -Dspring.profiles.active=secrets`

### Key Environment Variables
```bash
# Database
BILL_DATA_DB_DRIVER=org.postgresql.Driver
BILL_DATA_DB_MAX_POOL_SIZE=15

# Server
BILL_SERVER_PORT=8080

# Kafka
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_SECURITY_PROTOCOL=SASL_PLAINTEXT

# External Services
BILL_NETWORK_JUDGE_URL=https://judge-api-dev.jumia.services/
COMOD_NETWORK_COMMUNICATIONS_URL=https://communications-api-dev.jumia.services/

# ACL
BILL_ACL_SKIP=false
BILL_ACL_HOST=http://internal-api-acl-staging.jumia.services
```

## Code Quality & Tooling

### Quality Plugins
All modules include quality checks via `config/quality/quality.gradle`:

**Static Analysis:**
```bash
# Run all quality checks
./gradlew check

# Individual quality tools
./gradlew spotbugsMain      # Bug detection
./gradlew pmdMain           # Code analysis
./gradlew checkstyleMain    # Code style
./gradlew cpdCheck          # Copy-paste detection

# Test coverage
./gradlew jacocoTestReport
```

### Code Generation
**QueryDSL** entities are auto-generated during compilation:
- Generated sources: `build/generated-sources/java`
- Triggered by: `./gradlew compileJava`
- Used for type-safe database queries

### Quality Configuration
- **SpotBugs filter:** `config/quality/spotbugs-filter.xml`
- **PMD rules:** `config/quality/pmd-ruleset.xml`  
- **Checkstyle config:** `config/quality/checkstyle.xml`
- **Skip failures:** `-PignoreQualityFailures=true`

## Database & Kafka Integration

### Database Setup
- **Test database:** Automatically managed via Docker Compose
- **Migrations:** Flyway with `db/migration` scripts
- **Schemas:** `public` (application), `audit` (revision tracking)

### Kafka Topics
- **Bill Documents:** Multi-country topic for document events
- **Taxi Status Updates:** Document status synchronization
- **Configuration:** Environment-driven topic creation and auto-start

## Docker Integration

The project uses Docker Compose for testing dependencies:
- **Test services:** `../dockers/docker-compose.test.yml`
- **Database:** `bill-database-test` service on port 15432
- **Auto-startup:** Integrated with Gradle `test` task

## Troubleshooting

### Common Issues

**Tests failing with database connection:**
- Ensure Docker is running
- Check port 15432 is available
- Verify `docker-compose.test.yml` exists in `../dockers/`

**QueryDSL entities not found:**
```bash
./gradlew clean compileJava
```

**Quality checks failing:**
```bash
./gradlew check -PignoreQualityFailures=true
```

**Application won't start - missing secrets:**
- Copy `application-secrets.yml.template` to `application-secrets.yml`
- Fill in actual secret values
- Use `-Dspring.profiles.active=secrets`

### Useful Development URLs
- **Application:** http://localhost:8080
- **Swagger UI:** http://localhost:8080/swagger-ui.html (if enabled)
- **Health Check:** http://localhost:8080/health
- **Metrics:** http://localhost:8080/prometheus

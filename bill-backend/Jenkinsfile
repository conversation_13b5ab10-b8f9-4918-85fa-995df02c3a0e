#!/usr/bin/env groovy

def setGitBuildStatus(String context, String state, String message) {
    step([$class            : 'GitHubCommitStatusSetter',
          contextSource     : [$class: 'ManuallyEnteredCommitContextSource', context: context],
          reposSource: [$class: "ManuallyEnteredRepositorySource", url: "https://github.com/AIGEXP/JS-Bill"],
          statusResultSource: [$class: 'ConditionalStatusResultSource', results: [[$class: 'AnyBuildResult', state: state, message: message]]]])
}

def getNonCompliantCommitMessages(String baseBranch) {
    def allCommitMessages = sh(
            script: """git log --format=%s ${baseBranch}...HEAD | { grep -E -v -e '^Merge' -e '^Revert' -e 'Change version to' -e '^AFRFINIT-[0-9]+ .+' -e '^AFRFINIT-[0-9]+ .+' || true; }""",
            returnStdout: true)
            .trim()

    return allCommitMessages;
}

def getCompliantCommitMessages(String baseBranch) {
    def allCommitMessages = sh(
            script: """git log --format=%s ${baseBranch}...HEAD | { grep -E -e '^AFRFINIT-[0-9]+ .+' -e '^AFRFINIT-[0-9]+ .+' || true; }""",
            returnStdout: true)
            .trim()

    return allCommitMessages.split('\n');
}

def getJiraReleaseTickets(String projectVersion, String jiraUsername, String jiraPassword) {
    def version = projectVersion.replace(" ", "%20")
    return sh(
            script: "curl -s --request GET https://jira.jumia.com/rest/api/2/search?jql=fixVersion=\\\"${version}\\\" -u \"${jiraUsername}:${jiraPassword}\" -H 'Content-Type: application/json' | jq '.issues[].key' -r",
            returnStdout: true)
            .trim()
            .split('\n')
}

def allTicketsHaveTheCorrectFixVersion(commitMessages, releaseTickets, projectVersion) {
    boolean result = true

    for (commitMessage in commitMessages) {
        def matches = (commitMessage ==~ /^AFREXP-[0-9]+ .+/ || commitMessage ==~ /^AFRFINIT-[0-9]+ .+/ )
        if (matches) {
            String ticketId = commitMessage.split(" ")[0].trim()

            boolean isPresent = false
            for (ticket in releaseTickets) {
                jiraTicketId = ticket.split(" ")[0].trim()
                if (jiraTicketId == ticketId) {
                    isPresent = true
                    break
                }
            }

            if (!isPresent) {
                println("Ticket ${ticketId} is not present in version ${projectVersion}, but we found commits for it")
                result = false
            } else {
                println("Verified that ticket ${ticketId} is included in version ${projectVersion}")
            }
        }
    }
    return result
}

def allTicketsHaveAtLeastOneCommit(releaseTickets, commitMessages) {
    boolean  result = true
    for (ticket in releaseTickets) {
        jiraTicketId = ticket.split(" ")[0].trim()

        boolean hasCommitMessage = false
        for (commitMessage in commitMessages) {
            String ticketId = commitMessage.split(" ")[0].trim()
            if (jiraTicketId == ticketId) {
                println("At least one commit found for ${jiraTicketId}: ${commitMessage}")
                hasCommitMessage = true
                break
            }
        }

        if (!hasCommitMessage) {
            println("Ticket ${ticket} doesn't have any commit.")
            result = false
        }
    }

    return result
}

def runBuildStage(String githubStage, String buildType, String buildVersion) {

    setGitBuildStatus(githubStage, "PENDING", "Build")

    stage('Build') {

        dir('bill-backend') {
            withCredentials([usernamePassword(credentialsId: 'nexus-credentials', usernameVariable: 'NEXUS_USERNAME', passwordVariable: 'NEXUS_PASSWORD')]) {

                sh "./gradlew clean"

                sh "./gradlew assemble " +
                        "-PbuildType=${buildType} " +
                        "-PnexusUsername=${NEXUS_USERNAME} " +
                        "-PnexusPassword=${NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"

                // clear old jars to prevent having multiple versions on docker image
                sh "if [ -d ../dockers/bill-backend/app ]; then rm -Rf ../dockers/bill-backend/app; fi"
                sh "mkdir -p ../dockers/bill-backend/app"
                sh "cp ./build/libs/bill-backend-${buildVersion}-${buildType}.jar ../dockers/bill-backend/app"
            }
        }

    }

    setGitBuildStatus(githubStage, "SUCCESS", "Build")
}

def runTestsStage(String githubStage) {

    setGitBuildStatus(githubStage, "PENDING", "Tests")

    stage('Test Application') {
        dir('bill-backend') {

            withCredentials([usernamePassword(credentialsId: 'nexus-credentials', usernameVariable: 'NEXUS_USERNAME', passwordVariable: 'NEXUS_PASSWORD')]) {
                sh "./gradlew test " +
                        "-PnexusUsername=${env.NEXUS_USERNAME} " +
                        "-PnexusPassword=${env.NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"
            }
        }

        //gather all test reports
        junit allowEmptyResults: true,
                testResults: '**/build/test-results/test/*.xml'
    }

    setGitBuildStatus(githubStage, "SUCCESS", "Tests")

}

def runQualityCheckStage(String githubStage) {

    setGitBuildStatus(githubStage, "PENDING", "Code inspection")

    stage('Code inspection') {

        dir('bill-backend') {
            withCredentials([usernamePassword(credentialsId: 'nexus-credentials', usernameVariable: 'NEXUS_USERNAME', passwordVariable: 'NEXUS_PASSWORD')]) {
                sh "./gradlew pmdMain " +
                        "-PignoreQualityFailures=true " +
                        "-PnexusUsername=${env.NEXUS_USERNAME} " +
                        "-PnexusPassword=${env.NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"

                sh "./gradlew checkstyleMain " +
                        "-PignoreQualityFailures=true " +
                        "-PnexusUsername=${env.NEXUS_USERNAME} " +
                        "-PnexusPassword=${env.NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"

                sh "./gradlew cpdCheck " +
                        "-PignoreQualityFailures=true " +
                        "-PnexusUsername=${env.NEXUS_USERNAME} " +
                        "-PnexusPassword=${env.NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"

                sh "./gradlew spotbugsMain " +
                        "-PignoreQualityFailures=true " +
                        "-PnexusUsername=${env.NEXUS_USERNAME} " +
                        "-PnexusPassword=${env.NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"
            }
        }

        recordIssues enabledForFailure: true,
                tool: checkStyle(pattern: '**/checkstyle/main.xml'),
                qualityGates: [[threshold: 1, type: 'TOTAL', unstable: true]]

        recordIssues enabledForFailure: true,
                tool: spotBugs(pattern: '**/spotbugs/main.xml'),
                qualityGates: [[threshold: 1, type: 'TOTAL', unstable: true]]

        recordIssues enabledForFailure: true,
                tool: cpd(pattern: '**/cpdCheck.xml'),
                qualityGates: [[threshold: 90, type: 'TOTAL', unstable: true]]

        recordIssues enabledForFailure: true,
                tool: pmdParser(pattern: '**/pmd/main.xml')
        qualityGates: [[threshold: 1, type: 'TOTAL', unstable: true]]

        recordIssues enabledForFailure: true,
                tool: taskScanner(includePattern: '**/*.java, **/*.gradle, **/*.xml', excludePattern:'target/**/*, documentation/**/*', highTags:'FIXME', normalTags:'TODO'),
                sourceCodeEncoding: 'UTF-8'
        qualityGates: [[threshold: 1, type: 'TOTAL', unstable: true]]
    }

    stage('Code coverage') {


        withCredentials([usernamePassword(credentialsId: 'nexus-credentials', usernameVariable: 'NEXUS_USERNAME', passwordVariable: 'NEXUS_PASSWORD')]) {

            dir('bill-backend') {
                sh "./gradlew jacocoTestReport " +
                        "-PnexusUsername=${env.NEXUS_USERNAME} " +
                        "-PnexusPassword=${env.NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"
            }

            //code coverage reports
            jacoco changeBuildStatus: true,
                    classPattern: 'bill-backend/**/**/classes',
                    exclusionPattern: '**/*Test*.*',
                    maximumInstructionCoverage: '65',
                    minimumInstructionCoverage: '55',
                    maximumBranchCoverage: '40',
                    minimumBranchCoverage: '30'
            sourcePattern: 'bill-backend/**/src/main/java'
        }
    }

    //domain
    publishHTML([allowMissing         : false,
                 alwaysLinkToLastBuild: false,
                 keepAll              : true,
                 reportDir            : 'bill-backend/domain/build/reports/tests/test/',
                 reportFiles          : 'index.html',
                 reportName           : 'Domain Test Report',
                 reportTitles         : ''])
    //data
    publishHTML([allowMissing         : false,
                 alwaysLinkToLastBuild: false,
                 keepAll              : true,
                 reportDir            : 'bill-backend/data/build/reports/tests/test/',
                 reportFiles          : 'index.html',
                 reportName           : 'Data Test Report',
                 reportTitles         : ''])
    //network
    publishHTML([allowMissing         : false,
                 alwaysLinkToLastBuild: false,
                 keepAll              : true,
                 reportDir            : 'bill-backend/network/build/reports/tests/test/',
                 reportFiles          : 'index.html',
                 reportName           : 'Network Test Report',
                 reportTitles         : ''])
    //api
    publishHTML([allowMissing         : false,
                 alwaysLinkToLastBuild: false,
                 keepAll              : true,
                 reportDir            : 'bill-backend/api/build/reports/tests/test/',
                 reportFiles          : 'index.html',
                 reportName           : 'API Test Report',
                 reportTitles         : ''])

    //endpoints
    publishHTML([allowMissing         : false,
                 alwaysLinkToLastBuild: false,
                 keepAll              : true,
                 reportDir            : 'bill-backend/build/reports/tests/test/',
                 reportFiles          : 'index.html',
                 reportName           : 'Endpoints Test Report',
                 reportTitles         : ''])

    setGitBuildStatus(githubStage, "SUCCESS", "Code inspection")
}

def runArtifactsUploadStage(String githubStage, String buildType, String buildVersion, boolean publishDockerImage) {

    setGitBuildStatus(githubStage, "PENDING", "Artifacts upload")

    stage('Artifacts') {
        withCredentials([usernamePassword(credentialsId: 'nexus-credentials', usernameVariable: 'NEXUS_USERNAME', passwordVariable: 'NEXUS_PASSWORD')]) {
            archiveArtifacts 'bill-backend/build/libs/*.jar'

            dir('bill-backend') {
                sh "./gradlew dockerPush " +
                        "-PbuildType=${buildType} " +
                        "-PnexusUsername=${env.NEXUS_USERNAME} " +
                        "-PnexusPassword=${env.NEXUS_PASSWORD} " +
                        "-PnexusPath=${env.NEXUS_PATH} " +
                        "-PnexusUrl=${env.NEXUS_URL}"
            }
        }

    }

    stage('Push docker public registry') {
        if (publishDockerImage) {
            // push dockers to other registry
            sh "./build-docker-images.sh"

            def imageVersion = buildVersion
            sh "docker tag dockers_bill-be:latest ${env.DOCKER_PUBLIC_REGISTRY}/bill:${imageVersion}-${buildType}"
            sh "docker push ${env.DOCKER_PUBLIC_REGISTRY}/bill:${imageVersion}-${buildType}"
        }
    }

    setGitBuildStatus(githubStage, "SUCCESS", "Artifacts upload")
}

def checkGitJiraSyncStage(String githubStage, String buildVersion, String baseBranch, boolean buildingBranch, boolean releaseCandidateBranch) {

    setGitBuildStatus(githubStage, "PENDING", "Git-Jira sync")

    if (!buildingBranch && !releaseCandidateBranch) {
        stage("Check commit messages") {
            def allCommitMessages = getNonCompliantCommitMessages(baseBranch)

            // If we have any invalid commit message
            if (allCommitMessages) {
                def commitMessages =  allCommitMessages.split('\n')

                for (commitMessage in commitMessages) {
                    if (commitMessage) {
                        println("Commit message must have a reference to a JIRA ticket: ${commitMessage}")
                    }
                }
                throw new Exception("Some commit messages don't reference a JIRA ticket")
            }
        }
    }

    if (releaseCandidateBranch) {
        stage("Check release tickets") {

            def commitMessages = getCompliantCommitMessages(baseBranch)

            withCredentials([usernamePassword(credentialsId: 'swat-jira-user', usernameVariable: 'JIRA_USERNAME', passwordVariable: 'JIRA_PASSWORD')]) {
                String formattedProjectVersion = "Bill ${buildVersion}"
                def releaseTickets = getJiraReleaseTickets(formattedProjectVersion, JIRA_USERNAME, JIRA_PASSWORD)

                def areReleaseTicketsValid = allTicketsHaveAtLeastOneCommit(releaseTickets, commitMessages)
                if (!areReleaseTicketsValid) {
                    throw new Exception("Jira tickets without any commit.")
                }

                def isVersionConfigured = allTicketsHaveTheCorrectFixVersion(commitMessages, releaseTickets, formattedProjectVersion)

                if (!isVersionConfigured) {
                    throw new Exception("Wrong fix versions")
                }
            }
        }
    }
    
    setGitBuildStatus(githubStage, "SUCCESS", "Git-Jira")
}

timestamps {

    node('finance-slaves') {
        def githubStage

        try {
            checkout scm

            // github stage labels
            def githubBuildStage = "BE Build"
            def githubTestsStage = "BE Tests"
            def githubQualityCheckStage = "BE Code Inspection"
            def githubArtifactsUploadStage = "BE Upload artifacts"
            def gitJiraSyncCheckStage = "Git-Jira sync"

            def developmentBranch = env.BRANCH_NAME == "development"
            def masterBranch = env.BRANCH_NAME == "master"
            def releaseCandidateBranch = env.CHANGE_TARGET == "master"
            def buildingBranch = developmentBranch || masterBranch
            def buildType = "SNAPSHOT"
            if (masterBranch) {
                buildType = "RELEASE"
            }

            String BASE_BRANCH = env.CHANGE_TARGET
            if (!BASE_BRANCH) {
                BASE_BRANCH = "development"
            }
            BASE_BRANCH = sh(script: "git merge-base HEAD origin/${BASE_BRANCH} | tr -d \"[:space:]\"", returnStdout: true)

            def buildVersion;
            dir("bill-backend") {
                buildVersion = sh (script: "cat ./src/main/resources/application.yml  | grep version | cut -d ' ' -f2",  returnStdout: true).trim()
            }

            githubStage = gitJiraSyncCheckStage
            checkGitJiraSyncStage(githubStage, buildVersion, BASE_BRANCH, buildingBranch, releaseCandidateBranch)

            githubStage = githubBuildStage
            runBuildStage(githubStage, buildType, buildVersion)

            githubStage = githubTestsStage
            runTestsStage(githubStage)

            githubStage = githubQualityCheckStage
            runQualityCheckStage(githubStage)

            githubStage = githubArtifactsUploadStage
            runArtifactsUploadStage(githubStage, buildType, buildVersion, buildingBranch)

        } catch (error) {
            //test report
            junit allowEmptyResults: true, testResults: '**/build/test-results/test/*.xml'

            setGitBuildStatus(githubStage, "FAILURE", "Ups, something went wrong!");
            currentBuild.result = 'FAILURE';

            throw error
        } finally {
            println("Build result: ${currentBuild.result}")
            //notify github
            if (currentBuild.result == 'SUCCESS') {
                setGitBuildStatus(githubStage, 'SUCCESS', "Great success! All seems good.")
            } else if (currentBuild.result == 'UNSTABLE') {
                setGitBuildStatus(githubStage, 'UNSTABLE', "You messed up a bit. Tests are not failing, but code quality is!")
            } else {
                setGitBuildStatus(githubStage, 'FAILURE', "Not good!")
            }
        }
    }
}

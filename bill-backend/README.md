# Backend

This application is the backend of the bill project.

It is spring Boot application structured to follow the [clean architecture](https://8thlight.com/blog/uncle-bob/2012/08/13/the-clean-architecture.html). This means that it is divided in several modules:

#### Domain
Contains the business logic of the application, mainly entities and use cases.
It is also here that we create interfaces that will be implemented by other modules (data and network).

Should be very well tested (close to 100% code coverage) and all dependencies should be mocked. 
#### Network
Contains the code to communicate with other systems (http, rabbit, etc.).

In here we should test the contracts and test real calls to those systems.
#### Data
Represents the persistence layer, which main job is to implement the repository defined in the **domain**.

We are using flyway to run the migrations and we aim at using a database running in a docker for testing environments.

Tests should use the db in the docker and run all migrations and then test all the CRUD operations and more complex queries for all our managed entities.
#### Api
This is our current delivery mechanism (our interface with the outside world). It basically contains all our controllers and payload definitions.

In here tests should mainly focus on payload validations, by using mockMVC.
#### src
This basically only includes the Application class....

As for tests, it will include all the functional tests. This means tests end to end for all endpoints and then include some nice happy trails that simulate the main workflows of the app.


## Build

We use gradle as our build tool.
To run the app you only need to execute:
 
<code>./gradlew</code> or <code>./gradlew bootRun</code> 

 
 To run our tests:
 
 <code>./gradlew test</code>
 
 If you want to run tests only for a specific module:
 
 <code>./gradlew data:test</code>

## Run application

To run the application locally you will need some secrets (e.g acl migrator machine user, etc.).
You can duplicate the `application-secrets.yml.template` file, removing the template extension, replace the properties with the proper values and replace the properties with the secrets (ask someone from the team for the proper secret values).
Then we can just run the application in secrets profile.

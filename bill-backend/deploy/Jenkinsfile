import groovy.json.JsonOutput
timestamps {
  ansiColor('xterm') {

    node('finance-slaves') {
        def slackChannel = "services-live" == params.environment ? "#cs_falcons_cd_prod" : "#cs_falcons_cd_dev"

        try {

          checkout scm

          slackSend(channel: "${slackChannel}", color: '#FFFF00', message: "Deploy bill-backend on ${params.environment} started! ${env.BUILD_URL}")

          stage('Check for existing artifact') {
            withCredentials([usernamePassword(credentialsId: 'docker-public-registry', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
              def imageExists = sh (script: "curl --user ${DOCKER_USERNAME}:${DOCKER_PASSWORD} 'https://registry-internal.jumia.services/v2/bill/tags/list' | jq -r '.tags[]' | grep '^${params.version}-${params.qualifier}\$'", returnStatus: true) == 0
              if (!imageExists) {
                error("Image ${params.version}-${params.qualifier} doest exist... Skipping...")
              }
            }
          }

          if ("${params.environment}" == "services-live") {
            stage('Create CM') {
              dir('tools/scripts/project/release') {
                def jobUserId
                wrap([$class: 'BuildUser']) {
                  jobUserId = "${BUILD_USER_ID}"
                }

                withCredentials([usernamePassword(credentialsId: 'jenkins-machine-user-live', usernameVariable: 'JENKINS_USERNAME', passwordVariable: 'JENKINS_PASSWORD'),
                                 usernamePassword(credentialsId: 'swat-jira-user', usernameVariable: 'JIRA_USERNAME', passwordVariable: 'JIRA_PASSWORD'),
                                 string(credentialsId: 'sysadminsafrexp_token', variable: 'GITHUB_TOKEN')]) {
                  sh "python communicate-release.py --release \"${params.jiraRelease}\" --jira-project AFRFINIT --jira-user ${JIRA_USERNAME} --jira-password ${JIRA_PASSWORD} --github-token \"${GITHUB_TOKEN}\" --cm-user ${JENKINS_USERNAME} --cm-password ${JENKINS_PASSWORD} --cm-users-to-notify ${params.notify} --cm-hostname https://communications-api-services.jumia.com --project Bill --deploy-user ${jobUserId} --deploy-type ${params.deployType} --create-cm --tag \"${params.version}\""
                }
              }
            }
          }


          stage('Prepare Deploy') {
            dir('bill-backend/deploy/ansible') {
              script {
                def deployVars = [
                        version     : "${params.version}",
                        qualifier   : "${params.qualifier}"
                ]

                def json = JsonOutput.toJson(deployVars)
                writeFile file: "deploy.json", text: json

                println(json)
              }
            }
          }

          if ("${params.environment}" == "services-staging") { // deploy dev
              stage('Deploy Dev') {
                  dir('bill-backend/deploy/ansible') {
                      sh "./inventories/js_eu_west_1_dev/ec2.py --refresh-cache"
                      sh "ansible-playbook -i inventories/js_eu_west_1_dev plays/deploy.yml --extra-vars \"@deploy.json\""
                  }
              }
          } else if ("${params.environment}" == "services-live") { // deploy live
              stage('Deploy Prod') {
                  dir('bill-backend/deploy/ansible') {
                      sh "./inventories/js_eu_west_1_prod/ec2.py --refresh-cache"
                      sh "ansible-playbook -i inventories/js_eu_west_1_prod plays/deploy.yml --extra-vars \"@deploy.json\""
                  }
              }
          }

          if ("${params.close_release}" == "true") {
            stage('Close Jira release') {
              dir('tools/scripts/project/release') {
                withCredentials([usernamePassword(credentialsId: 'jenkins-machine-user-live', usernameVariable: 'JENKINS_USERNAME', passwordVariable: 'JENKINS_PASSWORD'),
                                 usernamePassword(credentialsId: 'swat-jira-user', usernameVariable: 'JIRA_USERNAME', passwordVariable: 'JIRA_PASSWORD')]) {
                  sh "python close-release.py --release \"${params.jiraRelease}\" --jira-project AFRFINIT --jira-user ${JIRA_USERNAME} --jira-password ${JIRA_PASSWORD}"
                }
              }
            }
          }

          slackSend(channel: "${slackChannel}", color: '#00FF00', message: "Deploy bill-backend on ${params.environment} completed successfully! ${env.BUILD_URL}")
        } catch (error) {
            slackSend(channel: "${slackChannel}", color: '#FF0000', message: "Deploy bill-backend Job failed on ${params.environment} ! ${env.BUILD_URL}")
            throw error
        }
    }
  }
}

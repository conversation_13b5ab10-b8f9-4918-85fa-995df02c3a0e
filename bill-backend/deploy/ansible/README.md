# Deploy Process for Bill Backend


## Getting Started

These instructions will get you a copy of the project up and running on your local machine to deploy the application.


### Prerequisites

Tested on :

- Ansible 2.7.2


### What It Does

Deploys search backend  application

### Quick start notes

1. Install [ansible](https://pypi.python.org/pypi/ansible/), [boto3](https://pypi.python.org/pypi/boto3) and [dnspython](http://www.dnspython.org/)
2. Make sure you have you AWS credentials (`~/.aws/[credentials|config]`) configured with the needed profiles
3. Now you should be ready to run your playbooks, you could check some quick tips on [Useful Commands][1] section.

[1]: #useful-commands

### Useful commands

#### Refresh EC2 cache (inventory provisioning)
Before going through you should have configured the default aws profile on `~/.aws/[credentials|config]`
``` sh
./inventories/<env>/ec2.py --refresh-cache
```

#### Running deploy playbook
``` sh
ansible-playbook \
  --inventory inventories/<env> \
  --extra-vars '\
    version="0.1.0," \
    qualifier="SNAPSHOT", \
    nexus_username="<nexus_username>", \
    nexus_password="<nexus_password>" \
  ' \
  plays/deploy.yml
```

- name: "pre flight check"
  hosts: localhost
  any_errors_fatal: true
  tags:
    - always
  tasks:
    - assert:
        that:
          - version is defined
          - qualifier is defined


- name: "deploy"
  hosts: "bill"
  any_errors_fatal: true
  serial:
    - 1
  tasks:

    - name: "stop consul agent"
      become: true
      docker_container:
        name: "consul-agent"
        state: stopped
      tags:
        - disable-consul-agent

    - name: "wait for node to become unavailable"
      shell: "dig +short bill-finance.service.consul.{{ internal_domain }}."
      register: dig_result
      until:  ansible_default_ipv4.address not in dig_result.stdout
      retries: 30
      delay: 5

    - pause:
        seconds: 10
      tags:
        - disable-consul-agent

    - set_fact:
        memtotal_kb: "{{ ansible_memtotal_mb * 1024 }}"
    - set_fact:
        java_heap: "{{ ((memtotal_kb | int) * 33 / 100 / 1024) | int }}"
    - set_fact:
        java_heap: "{{ ((memtotal_kb | int) * 50 / 100 / 1024) | int }}"
      when: (memtotal_kb | int) > 1100000

    - set_fact:
        java_options: "-Xms{{ java_heap }}m -Xmx{{ java_heap }}m"

    - set_fact:
        java_options: "{{ java_options }} -Dlogging.config=/opt/app/config/log4j2.xml -Dspring.config.additional-location=/opt/app/config/ -javaagent:/opt/newrelic/newrelic.jar"

    - name: "recreate container"
      become: true
      docker_container:
        name: "{{ finance['configs']['bill']['container']['name'] }}"
        image: "{{ finance['configs']['bill']['container']['image'] }}:{{ version }}-{{ qualifier }}"
        pull: true
        ports:
          - "8080:8080"
          - "9990:9990"
        volumes:
          - "{{ finance['configs']['bill']['directory']['logs'] }}:/opt/app/logs/"
          - "{{ finance['configs']['bill']['directory']['config'] }}:/opt/app/config"
          - "{{ finance['configs']['bill']['directory']['newrelic'] }}:/opt/newrelic"
          - "/tmp:/opt/app/dumps"
        detach: true
        env:
          SPRING_PROFILES_ACTIVE="{{ env }}"
          JAVA_OPTS="{{ java_options }}"
        recreate: true
        restart_policy: unless-stopped

    - name: "start consul agent"
      become: true
      docker_container:
        name: "consul-agent"
        state: started
      tags:
        - enable-consul-agent

    - name: "wait for node to become available"
      shell: "dig +short bill-finance.service.consul.{{ internal_domain }}."
      register: dig_result
      until:  ansible_default_ipv4.address in dig_result.stdout
      retries: 30
      delay: 5

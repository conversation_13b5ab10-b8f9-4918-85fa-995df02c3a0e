[ec2]

## general ##
regions = eu-west-1
regions_exclude = us-gov-west-1, cn-north-1

destination_variable = private_dns_name
hostname_variable = tag_Name
vpc_destination_variable = private_ip_address

replace_dash_in_groups = True
expand_csv_tags = False


## cache ##
cache_path = ~/.ansible/tmp
cache_max_age = 300


## include/exclude from inventory ##
all_instances = True

route53 = False
rds = False
elasticache = False
all_rds_instances = False
include_rds_clusters = False
all_elasticache_replication_groups = False
all_elasticache_clusters = False
all_elasticache_nodes = False


## group by ##
nested_groups = False

group_by_instance_id = False
group_by_region = False
group_by_availability_zone = True
group_by_aws_account = False
group_by_ami_id = False
group_by_instance_type = False
group_by_instance_state = False
group_by_platform = False
group_by_key_pair = False
group_by_vpc_id = False
group_by_security_group = False
group_by_tag_keys = True
group_by_tag_none = False
group_by_route53_names = False
group_by_rds_engine = False
group_by_rds_parameter_group = False
group_by_elasticache_engine = False
group_by_elasticache_cluster = False
group_by_elasticache_parameter_group = False
group_by_elasticache_replication_group = False

pattern_include = (dev.*|.*.dev.*)
stack_filters = False

## only look at instances with tag "role" defined ##
instance_filters = tag:role=*

[credentials]

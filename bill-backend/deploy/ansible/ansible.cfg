[defaults]
remote_user=finance-deployer
private_key_file=~/.ssh/finance-deployer

nocows=True

callback_plugins = callback_plugins/

jinja2_extensions = jinja2.ext.do,jinja2.ext.i18n

roles_path = roles/internal:roles/external

forks = 10
retry_files_enabled = False

gathering = smart
hash_behaviour=merge

host_key_checking = False
max_diff_size = 327680

[ssh_connection]
pipelining = True
ssh_args = -o ControlMaster=auto -o ControlPersist=30m

[diff]
always = True

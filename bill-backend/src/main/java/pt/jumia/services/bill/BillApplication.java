package pt.jumia.services.bill;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import pt.jumia.services.bill.domain.properties.ApiProperties;
import pt.jumia.services.bill.domain.properties.InfoProperties;
import pt.jumia.services.bill.domain.properties.ServerProperties;
import pt.jumia.services.bill.domain.properties.SpringProperties;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.MessageFormat;

@SpringBootApplication
@Slf4j
@SuppressWarnings("PMD.CloseResource")
public class BillApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext appContext = SpringApplication.run(BillApplication.class, args);

        SpringProperties springProperties = appContext.getBean(SpringProperties.class);
        ServerProperties serverProperties = appContext.getBean(ServerProperties.class);
        InfoProperties infoProperties = appContext.getBean(InfoProperties.class);
        ApiProperties apiProperties = appContext.getBean(ApiProperties.class);
        String protocol = "http";

        StringBuilder startupInfoBuilder = new StringBuilder();
        startupInfoBuilder.append("\n--------------------------------------------------------------------\n");

        startupInfoBuilder.append(MessageFormat.format(
                "\tApplication {0} v{1} is running! Access URLs and Info:\n",
                springProperties.getApplication().getName(),
                infoProperties.getBuild().getVersion()));

        startupInfoBuilder.append(MessageFormat.format(
                "\tLocal: \t\t\t{0}://localhost:{1}\n",
                protocol, String.valueOf(serverProperties.getPort())));

        startupInfoBuilder.append(MessageFormat.format(
                "\tExternal: \t{0}://{1}:{2}\n",
                protocol,
                InetAddress.getLocalHost().getHostAddress(),
                String.valueOf(serverProperties.getPort())));

        if (apiProperties.isSwaggerEnabled()) {
            startupInfoBuilder.append(MessageFormat.format(
                    "\tSwagger: \t\t{0}swagger-ui.html\n",
                    apiProperties.getSelfHost()));
        }

        String activeProfiles = String.join(", ", appContext.getEnvironment().getActiveProfiles());
        startupInfoBuilder.append(MessageFormat.format(
                "\tProfile(s): {0}\n",
                activeProfiles));

        startupInfoBuilder.append("--------------------------------------------------------------------");
        log.info(startupInfoBuilder.toString());
    }
}

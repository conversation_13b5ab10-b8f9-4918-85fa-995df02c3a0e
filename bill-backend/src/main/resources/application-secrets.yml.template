spring:
  datasource:
    url: *********************************************************************
  kafka:
    cluster-bill:
      properties:
        sasl:
          jaas:
            config: org.apache.kafka.common.security.scram.ScramLoginModule optional username=bill password="somepass";

data:
  db:
    url: *************************************
    username: someuser
    password: somepass

#Network
network:
  judge:
    username: bill
    password: somepass
  communications:
    username: bill
    password: somepass
  taxi:
    username: bill
    password: somepass
  afroms:
    shops:
      - country: eg
        shop: jumia
        url: https://console-staging.jumia.com.eg/
        apikey: someapikey
      - country: ug
        shop: jumia
        url: https://console-staging.jumia.ug/
        apikey: someapikey
      - country: ma
        shop: jumia
        url: https://console-staging.jumia.ma/
        apikey: someapikey

acl:
  migrator-user:
    username: someuser
    password: somepass

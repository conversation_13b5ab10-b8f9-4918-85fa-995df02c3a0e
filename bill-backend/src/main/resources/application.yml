# Uncomment to run the application with the fake implementations (repository, network, etc.)
#spring.profiles.active=fake

info.build.version: 0.78.0
server:
  port: ${BILL_SERVER_PORT:8080}
  error:
    include-message: always

spring:
  application.name: ${BILL_SPRING_APPLICATION_NAME:Bill}
  flyway:
    enabled: ${BILL_SPRING_FLYWAY_ENABLED:false}
    locations: ${BILL_SPRING_FLYWAY_LOCATIONS:db/migration}
    schemas: ${BILL_SPRING_FLYWAY_SCHEMAS:audit,public}

  kafka:
    cluster-bill:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
      properties:
        security:
          protocol: ${KAFKA_SECURITY_PROTOCOL:SASL_PLAINTEXT}
        sasl:
          mechanism: SCRAM-SHA-512
      consumer:
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
        max-poll-records: 1
      producer:
        value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

#Kafka
kafka:
  topics:
    bill-documents:
      create: ${KAFKA_CREATE_BILL:false}
      auto-start: ${KAFKA_AUTOSTART_BILL:false}
      name: >-
        bill.documents.jumia-eg
        bill.documents.jumia-ug
        bill.documents.jumia-ke
        bill.documents.jumia-gh
      group-id: "bill-documents-group"
      partitions: 3
    taxi-documents-status-updates:
      create: ${KAFKA_CREATE_TAXI:false}
      auto-start: ${KAFKA_AUTOSTART_TAXI:false}
      name: >-
        taxi.documents.status-updates.jumia-eg
        taxi.documents.status-updates.jumia-ug
        taxi.documents.status-updates.jumia-ke
        taxi.documents.status-updates.jumia-gh
      group-id: "taxi-documents-status-updates-group"
      partitions: 3

endpoints.default.web.enabled: false

#Spring actuator management configs
management.endpoints.web:
  base-path: / # Force base path to be root for instead of default "/actuator"
  exposure.include: health, prometheus   # Expose only health endpoint

#data
data:
  events:
    check-connection-timeout: ${BILL_DATA_EVENTS_CHECK_CONNECTION_TIMEOUT:15s}
  db:
    driver: ${BILL_DATA_DB_DRIVER:org.postgresql.Driver}
    application-schema: ${BILL_DATA_DB_APPLICATION_SCHEMA:public}
    audit-schema: ${BILL_DATA_DB_AUDIT_SCHAMA:audit}
    quartz-schema: ${BILL_DATA_DB_QUARTZ_SCHEMA:public}
    max-pool-size: ${BILL_DATA_DB_MAX_POOL_SIZE:15}
    flyway:
      repair: ${BILL_DATA_DB_MAX_POOL_SIZE:false}

#API
api:
  swagger-enabled: ${BILL_API_SWAGGER_ENABLED:true}
  self-host: ${BILL_API_SELF_HOST:http://localhost:8080/}
  allowed-domains: ${BILL_API_ALLOWED_DOMAINS:http://localhost:9000,http://localhost:8080,http://localhost:3000,http://localhost:4210,http://localhost:4200}

#Network
network:
  logging: ${BILL_NETWORK_LOGGING_ENABLED:false}
  read-timeout: 30
  judge:
    url: ${BILL_NETWORK_JUDGE_URL:https://judge-api-dev.jumia.services/}
  communications:
    url: ${COMOD_NETWORK_COMMUNICATIONS_URL:https://communications-api-dev.jumia.services/}
  taxi:
    url: ${BILL_NETWORK_TAXI_URL:http://localhost:8081/}

#ACL
acl:
  skip: ${BILL_ACL_SKIP:false}
  url: ${BILL_ACL_HOST:http://internal-api-acl-staging.jumia.services}
  app-name: ${BILL_ACL_APP_NAME:bill}
  cache:
    strategy: ${BILL_ACL_CACHE_STRATEGY:in-memory}
    in-memory:
      expiration-duration: ${BILL_ACL_CACHE_IN_MEMORY_EXPIRATION_DURATION:5m}
    redis:
      host: ${BILL_ACL_REDIS_HOST:dev-communications.2smgfr.0001.euw1.cache.amazonaws.com}
      port: ${BILL_ACL_REDIS_PORT:6379}
      username-key-prefix: ${BILL_ACL_REDIS_USERNAME_KEY_PREFIX:bill}
      password: ${BILL_ACL_REDIS_PASSWORD:dummy}
      expiration-duration: ${BILL_ACL_REDIS_EXPIRATION_DURATION:5m}
      timeout: ${BILL_ACL_REDIS_TIMEOUT:0s}
  migrator-user:
    username: ${BILL_ACL_MIGRATOR_USER_USERNAME:dummy}
    password: ${BILL_ACL_MIGRATOR_USER_PASSWORD:dummy}

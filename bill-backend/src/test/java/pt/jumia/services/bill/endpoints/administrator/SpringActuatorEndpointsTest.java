package pt.jumia.services.bill.endpoints.administrator;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.bill.domain.Profiles;
import pt.jumia.services.bill.network.afroms.AfromsNetworkRequester;
import pt.jumia.services.bill.robots.SpringActuatorRobot;


/**
 * SpringActuator integration tests
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles({Profiles.FAKE_CLIENTS, Profiles.TEST})
@AutoConfigureMetrics
@TestPropertySource(
        properties = {"spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration"}
)
public class SpringActuatorEndpointsTest {

    @LocalServerPort
    protected int port;

    private SpringActuatorRobot springActuatorRobot;

    @MockBean
    private AfromsNetworkRequester afromsNetworkRequester;

    @BeforeEach
    public void baseSetUp() {
        springActuatorRobot = new SpringActuatorRobot(port);
    }

    @Test
    public void healthTest() {
        springActuatorRobot.fetchHealth();
        springActuatorRobot.fetchHealthJson();
    }
}

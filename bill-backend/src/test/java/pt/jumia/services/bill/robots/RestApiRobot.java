package pt.jumia.services.bill.robots;

import io.restassured.RestAssured;
import io.restassured.specification.RequestSpecification;
import org.springframework.http.HttpHeaders;
import org.springframework.test.context.ActiveProfiles;
import pt.jumia.services.bill.domain.Profiles;

import static io.restassured.RestAssured.enableLoggingOfRequestAndResponseIfValidationFails;
import static io.restassured.RestAssured.given;
import static io.restassured.RestAssured.urlEncodingEnabled;

/**
 * Robot parent class that already deals with RestAssured configuration auth mocking if needed
 */
@ActiveProfiles({Profiles.FAKE_CLIENTS, Profiles.TEST})
public abstract class RestApiRobot {

    public static final String TOKEN = "lets_imagine_this_is_a_JWT_token";

    public RestApiRobot(int port) {
        RestAssured.port = port;
        enableLoggingOfRequestAndResponseIfValidationFails();
        urlEncodingEnabled = true;
    }

    protected RequestSpecification givenAuth() {
        return given()
            .header(HttpHeaders.CONTENT_TYPE, "application/json")
            .header(HttpHeaders.AUTHORIZATION, "Bearer " + TOKEN);
    }
}

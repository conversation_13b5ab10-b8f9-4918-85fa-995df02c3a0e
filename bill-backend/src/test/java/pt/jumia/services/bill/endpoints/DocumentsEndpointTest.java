package pt.jumia.services.bill.endpoints;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.bill.api.payloads.request.DocumentTransformationApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.TaxAuthoritiesDetailsApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.DocumentAggregateApiResponsePayload;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentPossibleOperations;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.ItemType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.network.taxi.TaxiNetworkRequester;
import pt.jumia.services.bill.robots.DocumentsRobot;
import pt.jumia.services.bill.test.utils.DecimalTestUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static pt.jumia.services.bill.test.utils.AssertionsHelper.WITHOUT_DB_FIELDS_AND_ISSUE_DATE_COMPARATOR;

/**
 * End to End test of the Document API, performing all CRUD operations and making sure that data is correctly updated
 */
public class DocumentsEndpointTest extends BaseEndpointTest {

    @MockBean
    private TaxiNetworkRequester taxiNetworkRequester;

    @BeforeEach
    public void setUp() {
        endpoint = DocumentsRobot.BASE_ROUTE;
        loginUser("<EMAIL>");

        doNothing().when(taxiNetworkRequester).pushInvoice(any());
    }

    @Test
    public void createDocument() {
        DocumentAggregate document = create(generateDocument("1234"));
        assertThat(document).isNotNull();
    }

    @Test
    public void createDuplicateDocument() {
        DocumentAggregate document = create(generateDocument("12345"));
        assertThat(document).isNotNull();

        documentsRobot.createFails(generateDocument("12345"))
                .statusCode(HttpStatus.BAD_REQUEST.value())
                .body("errors[0].name", equalTo("DUPLICATE_DOCUMENT"));
    }

    @Test
    public void getDocumentById() {
        DocumentAggregate createdDocument = create(generateDocument("12345"));
        createdDocument.setDocumentTransformations(Collections.emptyList());
        String documentId = String.valueOf(createdDocument.getDocument().getId());
        DocumentAggregate readDocument = documentsRobot.readByDocumentId(documentId);
        assertThat(readDocument).isEqualTo(createdDocument);
    }

    @Test
    public void getDocumentLogAndDocumentById() {
        String sid = "4321-test";
        DocumentAggregate createdDocument = create(generateDocument(sid));
        createdDocument.setDocumentTransformations(Collections.emptyList());
        String documentId = String.valueOf(createdDocument.getDocument().getId());
        DocumentAggregate readDocument = documentsRobot.readByDocumentId(documentId);
        DocumentApiLog documentApiLog = getDocumentApiLogByDocumentSid(sid);
        assertThat(readDocument).isEqualTo(createdDocument);
        assertThat(documentApiLog.getDocumentSid()).isEqualTo(createdDocument.getDocument().getSid());
        assertThat(documentApiLog.getIssuedDate()).isEqualTo(createdDocument.getDocument().getIssuedDate());
        assertThat(documentApiLog.getStatus()).isEqualTo(DocumentApiLog.DocumentApiLogStatus.SAVED);
        assertThat(documentApiLog.getResultingDocumentsIds()).contains(createdDocument.getDocument().getId());
    }

    @Test
    public void createReadAndUpdateTaxAuthoritiesDetails() {
        String documentSid = "test-document-sid3";
        DocumentAggregate createdDocument = create(generateDocument(documentSid));
        createdDocument.setDocumentTransformations(Collections.emptyList());
        String documentId = String.valueOf(createdDocument.getDocument().getId());
        DocumentAggregate readDocument = documentsRobot.readByDocumentId(documentId);
        assertThat(readDocument).isEqualTo(createdDocument);

        TaxAuthoritiesDetailsApiRequestPayload requestPayload = generateTaxAuthoritiesDetailsRequestPayload(createdDocument);
        TaxAuthoritiesDetails taxDetails = updateTaxDetails(createdDocument.getDocument().getId(), requestPayload);

        DocumentAggregate readDocumentAfterUpdate = documentsRobot.readSuccessfulDocumentByDocumentSid(documentSid);
        //check document is in success status
        assertThat(readDocumentAfterUpdate.getDocument().getStatus()).isEqualTo(DocumentStatus.TAX_SUCCESS);

        assertThat(readDocumentAfterUpdate.getTaxAuthoritiesDetails().withoutDbField())
                .usingRecursiveComparison(WITHOUT_DB_FIELDS_AND_ISSUE_DATE_COMPARATOR)
                //skip operations since tax details is an export of the db which has not possible operations appended
                .ignoringFieldsOfTypes(DocumentPossibleOperations.class)
                .isEqualTo(taxDetails.withoutDbField());
    }

    @Test
    public void getCountOfDocuments() {
        create(generateDocument("12345"));
        create(generateDocument("12346"));
        create(generateDocument("12347"));
        create(generateDocument("12348", "reference1"));
        create(generateDocument("12349", "reference2"));

        long documentsCountWithNoFilters = documentsRobot.getCount(null);
        assertThat(documentsCountWithNoFilters).isEqualTo(5);

        long documentsCountWithStatusFilters = documentsRobot.getCount(
                DocumentFilter
                        .builder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());
        assertThat(documentsCountWithStatusFilters).isEqualTo(5);

        long documentsCountWithStatusAndRefNumberFilters = documentsRobot.getCount(
                DocumentFilter
                        .builder()
                        .referenceNumber("reference1")
                        .status(DocumentStatus.TAX_PENDING)
                        .build());
        assertThat(documentsCountWithStatusAndRefNumberFilters).isEqualTo(1);
    }

    @Test
    public void getDocuments() {

        create(generateDocument("12347"));
        create(generateDocument("12348", "reference1", DocumentFlow.RETAIL));
        DocumentAggregate documentWithReferenceNum2 = create(generateDocument("12349", "reference2"));
        //call updateTaxDetails to add a document transformation for the document
        TaxAuthoritiesDetailsApiRequestPayload requestPayload = generateTaxAuthoritiesDetailsRequestPayload(documentWithReferenceNum2);
        updateTaxDetails(documentWithReferenceNum2.getDocument().getId(), requestPayload);

        //pagination
        List<DocumentAggregateApiResponsePayload> documentsPaginated = documentsRobot.getDocumentsPaginated(
                DocumentFilter.builder().include(List.of(Document.Details.values())).build(),
                null,
                PageFilters
                        .builder()
                        .size(2)
                        .page(1)
                        .build()
        );
        assertThat(documentsPaginated)
                .hasSize(2);

        //filters
        List<DocumentAggregateApiResponsePayload> documentsFilteredWithReference = documentsRobot.getDocumentsPaginated(
                DocumentFilter
                        .builder()
                        .referenceNumber("reference2")
                        .include(List.of(Document.Details.values()))
                        .build(),
                null,
                PageFilters
                        .builder()
                        .size(10)
                        .page(1)
                        .build()
        );
        assertThat(documentsFilteredWithReference)
                .hasSize(1);
        assertThat(documentsFilteredWithReference.get(0).getReferenceNumber())
                .isEqualTo(documentWithReferenceNum2.getDocument().getReferenceNumber());

        List<DocumentAggregateApiResponsePayload> documentsFilteredWithFlowPartialText = documentsRobot.getDocumentsPaginated(
                DocumentFilter
                        .builder()
                        .flow(DocumentFlow.RETAIL)
                        .include(List.of(Document.Details.values()))
                        .build(),
                null,
                PageFilters
                        .builder()
                        .size(10)
                        .page(1)
                        .build()
        );
        assertThat(documentsFilteredWithFlowPartialText)
                .hasSize(3);

        //filters
        List<DocumentAggregateApiResponsePayload> documentsFilteredDocumentTransformation = documentsRobot.getDocumentsPaginated(
                DocumentFilter
                        .builder()
                        .documentTransformationTypes(List.of(DocumentTransformation.EntityType.ITEM_NAME_CHANGE))
                        .include(List.of(Document.Details.values()))
                        .build(),
                null,
                PageFilters
                        .builder()
                        .size(10)
                        .page(1)
                        .build()
        );
        assertThat(documentsFilteredDocumentTransformation)
                .hasSize(1);
        assertThat(documentsFilteredDocumentTransformation.get(0).getId())
                .isEqualTo(documentWithReferenceNum2.getDocument().getId());

    }

    @Test
    public void acknowledgeDocument() {
        DocumentAggregate createdDocument = create(generateDocument("12347"));
        updateDocumentStatus(createdDocument.getDocument().getId(), DocumentStatus.TAX_SUBMITTED_INVALID);

        Document acknowledgeDocument = documentsRobot.acknowledgeDocument(createdDocument.getDocument().getId());

        assertThat(acknowledgeDocument.getStatus()).isEqualTo(DocumentStatus.TAX_ERROR_ACKED);
    }

    @Test
    public void acknowledgeDocument_failInvalidStatus() {
        // create a document with status 'NEW' (cannot be acknowledged)
        DocumentAggregate createdDocument = create(generateDocument("12347"));

        documentsRobot.acknowledgeDocumentConflict(createdDocument.getDocument().getId());
    }

    private DocumentAggregate generateDocument(String sid) {
        return generateDocument(sid, null, DocumentFlow.RETAIL);
    }

    private DocumentAggregate generateDocument(String sid, String referenceNumber) {
        return generateDocument(sid, referenceNumber, DocumentFlow.RETAIL);
    }

    private DocumentAggregate generateDocument(String sid, String referenceNumber, DocumentFlow flow) {
        Document document = Document.builder()
                .status(DocumentStatus.NEW)
                .judgeSid(null)
                .country(CountryCode.UG)
                .shop("jumia")
                .referenceNumber(referenceNumber)
                .type(DocumentType.SALES_INVOICE)
                .sid(sid)
                .flow(flow)
                .generatedBy("NAV")
                .issuedDate(LocalDateTime.now())
                .currency(Currency.getInstance("UGX"))
                .receiver(Receiver.builder()
                        .type(ReceiverType.CUSTOMER)
                        .legalName("Some receiver")
                        .address(Address.builder()
                                .country(CountryCode.UG)
                                .region("region")
                                .city("city")
                                .street("Testing street")
                                .buildingNumber("1")
                                .floor("2")
                                .build())
                        .generateId()
                        .build())
                .issuer(Issuer.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .businessRegistrationNumber("123")
                        .taxIdentificationNumber("456")
                        .email("<EMAIL>")
                        .branch("branch 1")
                        .address(Address.builder()
                                .country(CountryCode.UG)
                                .region("region")
                                .city("city")
                                .street("Testing street")
                                .buildingNumber("1")
                                .floor("3")
                                .build())
                        .generateId()
                        .build())
                .lineCount(1)
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                .discountAmount(DecimalTestUtils.decimalValue("0.00"))
                .originalDocument(null)
                .notes(null)
                .generateId()
                .build();
        return DocumentAggregate.builder()
                .document(document)
                .lines(List.of(DocumentLine.builder()
                        .document(document)
                        .position(0)
                        .quantity(DecimalTestUtils.decimalValue("1.0"))
                        .unitOfMeasure(UnitOfMeasure.NOT_APPLICABLE)
                        .itemCode("test-product")
                        .itemName("Test product")
                        .itemType(ItemType.PRODUCT)
                        .category(Category.builder()
                                .sid(UUID.randomUUID().toString())
                                .name("Test Category")
                                .taxAuthorityCode("C1")
                                .build())
                        .unitPrice(DecimalTestUtils.decimalValue("100.00"))
                        .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                        .netAmount(DecimalTestUtils.decimalValue("100.00"))
                        .discount(DocumentLine.Discount.builder()
                                .amount(DecimalTestUtils.decimalValue("0.00"))
                                .rate(DecimalTestUtils.decimalValue("0.00"))
                                .build())
                        .totalTaxAmount(DecimalTestUtils.decimalValue("20.00"))
                        .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .taxRate(DecimalTestUtils.decimalValue("0.20"))
                                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                                .build()))
                        .generateId()
                        .build()
                ))
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .document(document)
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(DecimalTestUtils.decimalValue("0.20"))
                        .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                        .netAmount(DecimalTestUtils.decimalValue("100.00"))
                        .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                        .generateId()
                        .build()
                ))
                .taxAuthoritiesDetails(null)
                .build();
    }

    private TaxAuthoritiesDetailsApiRequestPayload generateTaxAuthoritiesDetailsRequestPayload(DocumentAggregate documentAggregate) {
        List<DocumentTransformationApiRequestPayload> documentTransformationList = new ArrayList();
        documentTransformationList.add(new DocumentTransformationApiRequestPayload(generateDocumentTransformation()));
        return TaxAuthoritiesDetailsApiRequestPayload.builder()
                .submissionId("Fake submission id")
                .taxDocumentNumber("Take tax document number")
                .qrCode("YADA")
                .verificationCode("BLA")
                .deviceNumber("*********")
                .statusCode("200")
                .errorCode("45")
                .exception(null)
                .apiLogStatus(TaxAuthoritiesDetailsApiRequestPayload.ApiLogStatus.SUCCESS.name())
                .documentTransformations(documentTransformationList)
                .build();
    }

    private DocumentTransformation generateDocumentTransformation() {
        return DocumentTransformation.builder()
                .originalValue("oldValue")
                .newValue("newValue")
                .type(DocumentTransformation.EntityType.ITEM_NAME_CHANGE)
                .build();
    }
}

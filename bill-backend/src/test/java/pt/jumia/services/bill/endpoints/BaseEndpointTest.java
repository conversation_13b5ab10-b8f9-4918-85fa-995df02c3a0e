package pt.jumia.services.bill.endpoints;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.actuate.metrics.AutoConfigureMetrics;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.web.server.LocalServerPort;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.api.payloads.request.TaxAuthoritiesDetailsApiRequestPayload;
import pt.jumia.services.bill.data.entities.DocumentApiLogPsql;
import pt.jumia.services.bill.data.entities.DocumentTransformationPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentApiLogRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentLineRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentTransformationRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaIssuerRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaReceiverRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaTaxAuthoritiesDetailsRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaTaxCategoryTotalRepository;
import pt.jumia.services.bill.domain.Permissions;
import pt.jumia.services.bill.domain.Profiles;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;
import pt.jumia.services.bill.domain.repository.TaxAuthoritiesDetailsRepository;
import pt.jumia.services.bill.domain.utils.JsonUtils;
import pt.jumia.services.bill.network.acl.AclNetworkRequester;
import pt.jumia.services.bill.network.afroms.AfromsNetworkRequester;
import pt.jumia.services.bill.robots.DocumentsRobot;
import pt.jumia.services.bill.robots.KafkaRobot;
import pt.jumia.services.bill.robots.ReceivedDocumentsRobot;
import pt.jumia.services.bill.robots.RestApiRobot;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static io.restassured.RestAssured.given;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles({Profiles.FAKE_CLIENTS, Profiles.TEST})
@AutoConfigureMetrics
@TestPropertySource(
        properties = {"spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration"}
)
public abstract class BaseEndpointTest {

    @LocalServerPort
    protected int port;

    protected String endpoint;

    @MockBean
    private AclNetworkRequester aclNetworkRequester;

    @MockBean
    private AfromsNetworkRequester afromsNetworkRequester;

    private final Stack<UUID> documentLinesToDelete = new Stack<>();
    private final Stack<Long> documentTransformationToDelete = new Stack<>();
    private final Stack<UUID> documentsToDelete = new Stack<>();
    private final Stack<UUID> issuersToDelete = new Stack<>();
    private final Stack<UUID> receiversToDelete = new Stack<>();
    private final Stack<UUID> taxAuthoritiesDetailsToDelete = new Stack<>();
    private final Stack<UUID> taxCategoryTotalsToDelete = new Stack<>();
    private final Stack<Long> documentApiLogsToDelete = new Stack<>();
    private final Stack<UUID> receivedDocumentsToDelete = new Stack<>();

    @Autowired
    private JpaDocumentLineRepository jpaDocumentLineRepository;
    @Autowired
    private JpaDocumentRepository jpaDocumentRepository;
    @Autowired
    private JpaDocumentApiLogRepository jpaDocumentApiLogRepository;
    @Autowired
    private JpaIssuerRepository jpaIssuerRepository;
    @Autowired
    private JpaReceiverRepository jpaReceiverRepository;
    @Autowired
    private JpaTaxAuthoritiesDetailsRepository jpaTaxAuthoritiesDetailsRepository;
    @Autowired
    private JpaTaxCategoryTotalRepository jpaTaxCategoryTotalRepository;
    @Autowired
    private JpaDocumentTransformationRepository jpaDocumentTransformationRepository;
    @Autowired
    private DocumentRepository documentRepository;
    @Autowired
    private TaxAuthoritiesDetailsRepository taxAuthoritiesDetailsRepository;
    @Autowired
    private DocumentTransformationRepository documentTransformationRepository;
    @Autowired
    private JsonUtils jsonUtils;

    protected DocumentsRobot documentsRobot;
    protected ReceivedDocumentsRobot receivedDocumentsRobot;
    protected KafkaRobot kafkaRobot;

    @BeforeEach
    public void baseSetUp() {
        documentsRobot = new DocumentsRobot(port, jsonUtils);
        receivedDocumentsRobot = new ReceivedDocumentsRobot(port, jsonUtils);
        kafkaRobot = new KafkaRobot(port, jsonUtils);
    }

    @AfterEach
    public void baseTearDown() {
        clearStack(taxAuthoritiesDetailsToDelete, jpaTaxAuthoritiesDetailsRepository::deleteById);
        clearStack(documentTransformationToDelete,jpaDocumentTransformationRepository::deleteById);
        clearStack(taxCategoryTotalsToDelete, jpaTaxCategoryTotalRepository::deleteById);
        clearStack(documentLinesToDelete, jpaDocumentLineRepository::deleteById);
        clearStack(documentsToDelete, jpaDocumentRepository::deleteById);
        clearStack(documentApiLogsToDelete, jpaDocumentApiLogRepository::deleteById);
        clearStack(issuersToDelete, jpaIssuerRepository::deleteById);
        clearStack(receiversToDelete, jpaReceiverRepository::deleteById);
    }

    @Test
    public void testBearerAuth() {
        given()
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + RestApiRobot.TOKEN)
                .when().get(endpoint)
                .then().statusCode(HttpStatus.OK.value());
    }

    @Test
    public void testBasicAuth() {
        //sending request with Basic auth (username: <EMAIL>, pswd: password)
        given()
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .header(HttpHeaders.AUTHORIZATION, "Basic ****************************************")
                .when().get(endpoint)
                .then().statusCode(HttpStatus.OK.value());
    }

    @Test
    public void testInvalidAuth() {
        //sending invalid authorization header
        loginUser("<EMAIL>");
        given()
                .header(HttpHeaders.CONTENT_TYPE, "application/json")
                .header(HttpHeaders.AUTHORIZATION, "random stuff")
                .when().get(endpoint)
                .then().statusCode(HttpStatus.BAD_REQUEST.value());
    }

    private <T> void clearStack(Stack<T> stack, Consumer<T> delete) {
        while (!stack.empty()) {
            try {
                delete.accept(stack.pop());
            } catch (EmptyResultDataAccessException e) {
                // Can happen if duplicate IDs end up in the stack to delete. Ignore
            }
        }
    }

    protected DocumentAggregate create(DocumentAggregate aggregate) {
        DocumentAggregate saved = documentsRobot.create(aggregate);
        Document document = saved.getDocument();
        if (document != null) {
            documentsToDelete.push(document.getId());
            if (document.getReceiver() != null) {
                receiversToDelete.push(document.getReceiver().getId());
            }
            if (document.getIssuer() != null) {
                issuersToDelete.push(document.getIssuer().getId());
            }
            jpaDocumentApiLogRepository.findByDocumentSid(document.getSid())
                    .stream()
                    .map(documentLogPsql -> {
                        documentApiLogsToDelete.push(documentLogPsql.toEntity().getId());
                        return documentLogPsql.toEntity().getId();
                    })
                    .collect(Collectors.toList());
        }
        if (saved.getLines() != null) {
            documentLinesToDelete.addAll(saved.getLines().stream().map(DocumentLine::getId).collect(Collectors.toList()));
        }
        if (saved.getDocumentTransformations() != null) {
            documentTransformationToDelete.addAll(saved.getDocumentTransformations()
                    .stream().map(DocumentTransformation::getId).collect(Collectors.toList()));
        }
        if (saved.getTaxCategoryTotals() != null) {
            taxCategoryTotalsToDelete.addAll(saved.getTaxCategoryTotals().stream().map(TaxCategoryTotal::getId).collect(Collectors.toList()));
        }
        if (saved.getTaxAuthoritiesDetails() != null) {
            taxAuthoritiesDetailsToDelete.add(saved.getTaxAuthoritiesDetails().getId());
        }

        return saved;
    }

    protected DocumentApiLog getDocumentApiLogByDocumentSid(String sid) {
        List<DocumentApiLog> documentApiLogs = jpaDocumentApiLogRepository.findByDocumentSid(sid)
                .stream()
                .map(DocumentApiLogPsql::toEntity)
                .collect(Collectors.toList());
        if (documentApiLogs.size() == 0) {
            return null;
        }
        return documentApiLogs.get(0);
    }

    protected Document updateDocumentStatus(UUID documentId, DocumentStatus documentStatus) {
        return documentRepository.updateStatus(documentId, documentStatus);
    }

    protected TaxAuthoritiesDetails updateTaxDetails(UUID documentId, TaxAuthoritiesDetailsApiRequestPayload requestPayload) {
        documentsRobot.updateTaxDetails(documentId, requestPayload);

        TaxAuthoritiesDetails taxAuthoritiesDetails = taxAuthoritiesDetailsRepository.findByDocumentId(documentId).get();
        List<DocumentTransformation> documentTransformationList =
                documentTransformationRepository.findByDocumentId(documentId);
        if(documentTransformationList != null ){
        documentTransformationToDelete.addAll(documentTransformationList
                .stream().map(DocumentTransformation::getId).collect(Collectors.toList()));
        }
        taxAuthoritiesDetailsToDelete.add(taxAuthoritiesDetails.getId());
        documentsToDelete.add(taxAuthoritiesDetails.getDocument().getId());


        return taxAuthoritiesDetails;
    }

    protected void updateStatus(UUID receivedDocumentId, DocumentStatus newStatus) {
        receivedDocumentsRobot.updateStatus(receivedDocumentId, newStatus);
    }

    protected void publishBill(BillPayload payload) {
        kafkaRobot.publishBill(payload);
    }

    protected void loginUser(String username) {
        mockJWT(username);
    }

    protected void anonymousUser() {
        mockJWT(null);
    }

    private void mockJWT(String username) {
        if (username == null) {
            when(aclNetworkRequester.decodeToken(eq(RestApiRobot.TOKEN))).thenThrow(AclErrorException.build(401));
            return;
        }

        Date expirationTime = new Date(new Date().getTime() + TimeUnit.HOURS.toMillis(12));
        RequestUser requestUser = RequestUser.builder()
                .exp(expirationTime.getTime())
                .username(username)
                .build();

        when(aclNetworkRequester.hasPermission(any(), any())).thenReturn(true);
        when(aclNetworkRequester.decodeToken(eq(RestApiRobot.TOKEN))).thenAnswer(invocation -> requestUser);
        when(aclNetworkRequester.authorize(username, "password")).thenAnswer(invocation -> requestUser);

        when(aclNetworkRequester.getPermissions(requestUser)).thenReturn(
                Map.of(
                        "APPLICATION", Map.of(
                                "bill", List.of(
                                        Permissions.CAN_ACCESS
                                )
                        ),
                        "COUNTRY", Map.of(
                                "EG", List.of(
                                        Permissions.RESUBMIT_DOCUMENT,
                                        Permissions.CAN_VIEW_DOCUMENTS,
                                        Permissions.CAN_MANAGE_DOCUMENTS,
                                        Permissions.ACK_DOCUMENT,
                                        Permissions.CAN_CREATE_RECEIVED_DOCUMENTS,
                                        Permissions.CAN_LIST_RECEIVED_DOCUMENTS,
                                        Permissions.DECLINE_RECEIVED_DOCUMENTS_CANCELLATION
                                ),
                                "UG", List.of(
                                        Permissions.RESUBMIT_DOCUMENT,
                                        Permissions.CAN_VIEW_DOCUMENTS,
                                        Permissions.CAN_MANAGE_DOCUMENTS,
                                        Permissions.ACK_DOCUMENT,
                                        Permissions.REVIEW_RECEIVED_DOCUMENTS,
                                        Permissions.REJECT_RECEIVED_DOCUMENTS
                                )
                        )
                )
        );
    }
}

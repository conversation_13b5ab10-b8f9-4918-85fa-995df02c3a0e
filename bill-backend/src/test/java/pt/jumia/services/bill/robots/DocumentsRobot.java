package pt.jumia.services.bill.robots;

import io.restassured.response.ValidatableResponse;
import io.restassured.specification.RequestSpecification;
import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import pt.jumia.services.bill.api.payloads.request.DocumentApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.TaxAuthoritiesDetailsApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.DocumentAggregateApiResponsePayload;
import pt.jumia.services.bill.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.utils.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class DocumentsRobot extends RestApiRobot {

    public static final String BASE_ROUTE = "/api/documents";
    private final JsonUtils jsonUtils;

    public DocumentsRobot(int port, JsonUtils jsonUtils) {
        super(port);
        this.jsonUtils = jsonUtils;
    }

    public DocumentAggregate create(DocumentAggregate documentAggregate) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .body(jsonUtils.toJsonOrNull(new DocumentApiRequestPayload((documentAggregate))))
                .when().post(BASE_ROUTE)
                .then().statusCode(HttpStatus.CREATED.value())
                .extract().as(DocumentAggregateApiResponsePayload.class).toEntity();
    }

    public ValidatableResponse createFails(DocumentAggregate documentAggregate) {
        return givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .body(jsonUtils.toJsonOrNull(new DocumentApiRequestPayload((documentAggregate))))
                .when().post(BASE_ROUTE)
                .then();
    }

    public void updateTaxDetails(UUID documentId, TaxAuthoritiesDetailsApiRequestPayload requestPayload) {
        String route = BASE_ROUTE + "/" + documentId + "/tax-details";
        givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .body(jsonUtils.toJsonOrNull(requestPayload))
                .when().post(route)
                .then().statusCode(HttpStatus.OK.value());
    }

    public DocumentAggregate readByDocumentId(String documentId) {
        String route = String.format("%s/%s", BASE_ROUTE, documentId);

        return givenAuth()
                .when().get(route)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(DocumentAggregateApiResponsePayload.class).toEntity();
    }

    public DocumentAggregate readSuccessfulDocumentByDocumentSid(String documentSid) {
        String route = String.format("%s/sid/%s", BASE_ROUTE, documentSid);

        return givenAuth()
                .when().get(route)
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(DocumentAggregateApiResponsePayload.class).toEntity();
    }

    public long getCount(DocumentFilter filters) {
        RequestSpecification request = givenAuth();
        buildQueryParams(request, filters, null, null);
        return request
                .when().get(BASE_ROUTE + "/count")
                .then().statusCode(HttpStatus.OK.value())
                .extract().as(Long.class);
    }

    public List<DocumentAggregateApiResponsePayload> getDocumentsPaginated(
            DocumentFilter filters,
            DocumentSortFilters sortingFilters,
            PageFilters pageFilters
    ) {
        RequestSpecification request = givenAuth();
        buildQueryParams(request, filters, sortingFilters, pageFilters);
        return new ArrayList<>(request
                .when().get(BASE_ROUTE)
                .then().statusCode(HttpStatus.OK.value())
                .extract().body().jsonPath()
                .getList("results", DocumentAggregateApiResponsePayload.class));
    }

    public Document acknowledgeDocument(UUID documentId) {
        return acknowledgeDocument(documentId, HttpStatus.OK)
                .extract().as(DocumentApiResponsePayload.class).toEntity();
    }

    public void acknowledgeDocumentConflict(UUID documentId) {
        acknowledgeDocument(documentId, HttpStatus.CONFLICT);
    }

    private ValidatableResponse acknowledgeDocument(UUID documentId, HttpStatus expectedStatus) {
        return givenAuth()
                .when().post(String.format("%s/%s/acknowledge", BASE_ROUTE, documentId))
                .then().statusCode(expectedStatus.value());
    }

    private void buildQueryParams(
            RequestSpecification request,
            DocumentFilter filters,
            DocumentSortFilters sortingFilters,
            PageFilters pageFilters
    ) {

        if (filters != null) {

            if (filters.getInclude() != null) {
                request.param("include", filters.getInclude());
            }
            if (filters.getDocumentTransformationTypes() != null) {
                request.param("documentTransformationTypes", filters.getDocumentTransformationTypes());
            }
            if (filters.getReferenceNumber() != null) {
                request.param("referenceNumber", filters.getReferenceNumber());
            }
            if (filters.getIssuerTin() != null) {
                request.param("issuerTin", filters.getIssuerTin());
            }
            if (filters.getReceiverTin() != null) {
                request.param("receiverTin", filters.getReceiverTin());
            }
            if (filters.getCreatedAtTo() != null) {
                request.param("createdAtTo", filters.getCreatedAtTo());
            }
            if (filters.getCountryCode() != null) {
                request.param("countryCode", filters.getCountryCode().name());
            }
            if (filters.getStatus() != null) {
                request.param("status", filters.getStatus().name());
            }
            if (filters.getType() != null) {
                request.param("type", filters.getType().name());
            }
            if (filters.getFlow() != null) {
                request.param("flow", filters.getFlow());
            }
        }

        if (sortingFilters != null) {
            if (sortingFilters.getField() != null) {
                request.param("orderField", sortingFilters.getField());
            }
            if (sortingFilters.getDirection() != null) {
                request.param("orderDirection", sortingFilters.getDirection());
            }
        }

        if (pageFilters != null) {
            request.param("page", pageFilters.getPage());
            request.param("size", pageFilters.getSize());
        }
    }

}

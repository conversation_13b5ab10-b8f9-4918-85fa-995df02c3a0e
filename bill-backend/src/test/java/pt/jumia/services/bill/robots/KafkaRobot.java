package pt.jumia.services.bill.robots;

import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;
import pt.jumia.services.bill.domain.utils.JsonUtils;

public class KafkaRobot extends RestApiRobot {
    public static final String BASE_ENDPOINT = "/api/kafka";
    private final JsonUtils jsonUtils;

    public KafkaRobot(int port, JsonUtils jsonUtils) {
        super(port);
        this.jsonUtils = jsonUtils;
    }

    public void publishBill(BillPayload payload) {
        givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .body(jsonUtils.toJsonOrNull(payload))
                .when().post(BASE_ENDPOINT + "/publish/bill")
                .then().statusCode(HttpStatus.OK.value());
    }
}

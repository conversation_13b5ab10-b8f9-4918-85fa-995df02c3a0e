package pt.jumia.services.bill.endpoints.kafka;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.context.EmbeddedKafka;
import org.springframework.kafka.test.utils.KafkaTestUtils;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;
import pt.jumia.services.bill.endpoints.BaseEndpointTest;
import pt.jumia.services.bill.robots.KafkaRobot;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import static java.util.Collections.singleton;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@EnableKafka
@EmbeddedKafka
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
        properties = {
                "spring.kafka.cluster-bill.bootstrap-servers=${spring.embedded.kafka.brokers}",
                "spring.kafka.cluster-bill.properties.security.protocol=PLAINTEXT",
                "kafka.topics.generic.auto-start=true",
                "kafka.topics.generic.create=true"})
public class BillProducerTest extends BaseEndpointTest {

    private static final String TOPIC = "bill.documents.jumia-eg";

    @Autowired
    private EmbeddedKafkaBroker embeddedKafkaBroker;

    private Consumer<String, String> consumer;

    @BeforeEach
    public void setUp() {

        endpoint = KafkaRobot.BASE_ENDPOINT;
        loginUser("<EMAIL>");

        Map<String, Object> configs = new HashMap<>(KafkaTestUtils.consumerProps("myGroup", "false", embeddedKafkaBroker));
        consumer = new DefaultKafkaConsumerFactory<>(configs, new StringDeserializer(), new StringDeserializer()).createConsumer();
        consumer.subscribe(singleton(TOPIC));
        consumer.poll(Duration.ofSeconds(1));
    }

    @AfterEach
    public void tearDown() {
        consumer.close();
    }

    @Disabled("We dont have a GET endpoint on the base endpoint /api/kafka")
    @Test
    @Override
    public void testBasicAuth() {
    }

    @Disabled("We dont have a GET endpoint on the base endpoint /api/kafka")
    @Test
    @Override
    public void testBearerAuth() {
    }

    @Test
    public void kafkaSetup_withTopic_ensureSendMessage() throws InterruptedException, JsonProcessingException {
        //execute
        BillPayload fakePayload = BillPayload.builder()
                .sid("123")
                .build();

        publishBill(fakePayload);
        Thread.sleep(500); //needed to make sure the app consumes the event

        ConsumerRecord<String, String> singleRecord = KafkaTestUtils.getSingleRecord(consumer, TOPIC);
        assertNotNull(singleRecord);

        // Hamcrest Matchers to check the value
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(fakePayload);

        assertEquals(TOPIC, singleRecord.topic());
        assertEquals(json, singleRecord.value());
    }
}
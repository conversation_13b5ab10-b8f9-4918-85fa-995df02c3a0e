package pt.jumia.services.bill.robots;

import org.apache.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.utils.JsonUtils;

import java.util.UUID;

public class ReceivedDocumentsRobot extends RestApiRobot {
    public static final String BASE_ENDPOINT = "/api/received-documents";
    private final JsonUtils jsonUtils;

    public ReceivedDocumentsRobot(int port, JsonUtils jsonUtils) {
        super(port);
        this.jsonUtils = jsonUtils;
    }

    public void updateStatus(UUID receivedDocumentId, DocumentStatus newStatus) {
        givenAuth()
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .when().put(BASE_ENDPOINT + "/" + "receivedDocumentId" + "/update-status/" + newStatus)
                .then().statusCode(HttpStatus.OK.value());
    }
}

apply from: '../config/quality/quality.gradle'

dependencies {
    implementation project(':domain')

    implementation("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    api("org.springframework.boot:spring-boot-starter-web:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    implementation ("org.springframework.boot:spring-boot-starter-validation:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    // Decode auth token
    implementation "com.nimbusds:nimbus-jose-jwt:2.10.1"

    // Decoding b2b token
    implementation group: 'com.sun.xml.security', name: 'xml-security-impl', version: '1.0'

    //swagger, for api documentation
    implementation group: 'io.springfox', name: 'springfox-swagger2', version: '2.7.0'
    implementation group: 'io.springfox', name: 'springfox-swagger-ui', version: '2.7.0'

    // https://mvnrepository.com/artifact/com.opencsv/opencsv
    compile group: 'com.opencsv', name: 'opencsv', version: '4.6'

    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    // https://mvnrepository.com/artifact/com.squareup.okhttp/okhttp
    implementation group: 'com.squareup.okhttp', name: 'okhttp', version: '2.7.5'

    //kafka
    compile group: 'org.springframework.kafka', name: 'spring-kafka', version: "${kafkaVersion}"
}

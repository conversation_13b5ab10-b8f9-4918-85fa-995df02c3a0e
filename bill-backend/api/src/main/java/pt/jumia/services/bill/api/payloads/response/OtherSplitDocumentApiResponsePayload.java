package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.OtherSplitDocument;

import java.util.UUID;

@Data
@NoArgsConstructor
public class OtherSplitDocumentApiResponsePayload {

    private UUID id;
    private DocumentStatus status;

    private DocumentType type;
    private String sid;

    public OtherSplitDocumentApiResponsePayload(OtherSplitDocument document) {
        this.id = document.getId();
        this.status = document.getStatus();
        this.type = document.getType();
        this.sid = document.getSid();
      }

    public OtherSplitDocument toEntity() {

        return OtherSplitDocument
                .builder()
                .id(this.id)
                .status(this.status)
                .type(this.type)
                .sid(this.sid)
                .build();
    }
}

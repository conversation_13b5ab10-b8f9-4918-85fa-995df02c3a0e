package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Category;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CategoryApiRequestPayload {

    private String sid;
    private String name;
    private String taxAuthorityCode;

    public CategoryApiRequestPayload(Category entity) {
        this.sid = entity.getSid();
        this.name = entity.getName();
        this.taxAuthorityCode = entity.getTaxAuthorityCode();
    }

    public Category toEntity(){
        return Category
                .builder()
                .sid(this.sid)
                .name(this.name)
                .taxAuthorityCode(this.taxAuthorityCode)
                .build();
    }
}

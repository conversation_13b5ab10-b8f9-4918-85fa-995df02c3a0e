package pt.jumia.services.bill.api.payloads.response;

import com.neovisionaries.i18n.CountryCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DeliveryDetails;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class DeliveryDetailsApiResponsePayload {

    private UUID id;
    private String approach;
    private String packaging;
    private LocalDateTime dateValidity;
    private String exportPort;
    private BigDecimal grossWeight;
    private BigDecimal netWeight;
    private String countryOfOrigin;
    private String terms;
    private LocalDateTime createdAt;
    private String createdBy;

    public DeliveryDetailsApiResponsePayload(DeliveryDetails entity){
        this.id = entity.getId();
        this.approach = entity.getApproach();
        this.packaging = entity.getPackaging();
        this.dateValidity = entity.getDateValidity();
        this.exportPort = entity.getExportPort();
        this.grossWeight = entity.getGrossWeight();
        this.netWeight = entity.getNetWeight();
        this.countryOfOrigin = entity.getCountryOfOrigin() != null ?
                entity.getCountryOfOrigin().getAlpha2() : null;
        this.terms = entity.getTerms();
        this.createdAt = entity.getCreatedAt();
        this.createdBy = entity.getCreatedBy();
    }

    public DeliveryDetails toEntity() {
        return DeliveryDetails
                .builder()
                .id(this.id)
                .approach(this.approach)
                .packaging(this.packaging)
                .dateValidity(this.dateValidity)
                .exportPort(this.exportPort)
                .grossWeight(this.grossWeight)
                .netWeight(this.netWeight)
                .countryOfOrigin(this.countryOfOrigin == null ? null :
                        CountryCode.valueOf(this.countryOfOrigin))
                .terms(this.terms)
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .build();
    }
}

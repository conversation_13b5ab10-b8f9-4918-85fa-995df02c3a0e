package pt.jumia.services.bill.api.filters;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.AccessController;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.properties.AclProperties;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

@RequiredArgsConstructor
public class AuthFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(AuthFilter.class);

    private static final String OPTIONS_REQUEST_METHOD = "OPTIONS";

    private static final String BEARER = "Bearer";

    private static final String BASIC = "Basic";

    private static final String HEADER_AUTHENTICATION = "Authorization";

    private static final String INVALID_AUTH_METHOD_MSG = "Invalid authorization method: ";

    private static final String AUTH_ERROR_MSG = "Please make sure your request has an Authorization header";

    private static final String BASIC_TOKEN_WRONG_ENCODING_MSG = "Basic token in wrong encoding";

    private static final String BASIC_TOKEN_WRONG_FORMAT_MSG = "Basic token in wrong format";

    private static final String ACL_SKIPPED_USER = "acl.skipped.user";

    private static final int AUTHENTICATION_LENGTH = 2;

    private final AccessController accessController;
    private final AclProperties aclProperties;

    @Override
    public void init(FilterConfig filterConfig) {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
            ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        if (OPTIONS_REQUEST_METHOD.equals(httpRequest.getMethod())) {
            return;
        }

        if (aclProperties.isSkip()) {
            RequestUser requestUser = RequestUser.builder()
                    .username(ACL_SKIPPED_USER)
                    .build();
            acceptRequest(request, response, chain, requestUser);
            return;
        }

        HttpServletResponse httpResponse = (HttpServletResponse) response;
        String authHeader = httpRequest.getHeader(HEADER_AUTHENTICATION);

        if (StringUtils.isBlank(authHeader) || authHeader.split(" ").length != 2) {
            httpResponse.sendError(HttpServletResponse.SC_UNAUTHORIZED, AUTH_ERROR_MSG);
            return;
        }

        String[] auth = authHeader.split(" ");
        String authType = auth[0];
        String token = auth[1];
        switch (authType) {
            case BEARER:
                handleBearerAuthorization(request, response, chain, httpResponse, token);
                break;
            case BASIC:
                handleBasicAuthorization(request, response, chain, httpResponse, token);
                break;
            default:
                httpResponse.sendError(HttpServletResponse.SC_BAD_REQUEST, INVALID_AUTH_METHOD_MSG + authType);
                break;
        }
    }

    private void handleBearerAuthorization(ServletRequest request, ServletResponse response,
                                           FilterChain chain, HttpServletResponse httpResponse,
                                           String token) throws IOException, ServletException {
        try {
            RequestUser requestUser = accessController.decodeToken(token);
            // accept request
            validatePermissions(requestUser);
            acceptRequest(request, response, chain, requestUser);

        } catch (AclErrorException e) {
            LOGGER.info("Unable to decode token: {}", ExceptionUtils.getStackTrace(e));
            int statusCode = propagateAclError(e.getCode()) ? e.getCode() : HttpServletResponse.SC_UNAUTHORIZED;
            httpResponse.sendError(statusCode, e.getMessage());
        }
    }

    private void handleBasicAuthorization(ServletRequest request, ServletResponse response,
                                          FilterChain chain, HttpServletResponse httpResponse,
                                          String token) throws IOException, ServletException {
        try {
            String decryptedToken;
            byte[] bytes = Base64.getDecoder().decode(token);
            if (Objects.isNull(bytes)) {
                LOGGER.error("Failed to decode basic auth {}", bytes);
                httpResponse.sendError(HttpServletResponse.SC_UNAUTHORIZED, BASIC_TOKEN_WRONG_ENCODING_MSG);
                return;
            }

            decryptedToken = new String(bytes, StandardCharsets.UTF_8);
            String[] authentication = decryptedToken.split(":");
            if (authentication.length != AUTHENTICATION_LENGTH) {
                httpResponse.sendError(HttpServletResponse.SC_UNAUTHORIZED, BASIC_TOKEN_WRONG_FORMAT_MSG);
                return;
            }
            String username = authentication[0];
            String password = authentication[1];
            RequestUser requestUser = accessController.authorize(username, password);
            // accept request
            validatePermissions(requestUser);
            acceptRequest(request, response, chain, requestUser);

        } catch (AclErrorException e) {
            LOGGER.warn("Unauthorized access from token {}", token);
            int statusCode = propagateAclError(e.getCode()) ? e.getCode() : HttpServletResponse.SC_UNAUTHORIZED;
            httpResponse.sendError(statusCode, e.getMessage());
        }
    }

    private boolean propagateAclError(int errorCode) {
        return HttpServletResponse.SC_FORBIDDEN == errorCode ||
                HttpServletResponse.SC_UNAUTHORIZED == errorCode;
    }

    private void acceptRequest(ServletRequest request, ServletResponse response, FilterChain chain,
                               RequestUser requestUser) throws IOException, ServletException {
        try {
            // Set request user
            RequestContext.setUser(requestUser);
            chain.doFilter(request, response);
        } finally {
            // Clear context
            RequestContext.clear();
        }
    }

    private void validatePermissions(RequestUser requestUser) {
        boolean canAccess = !this.accessController.getPermissions(requestUser).isEmpty();
        if (!canAccess) {
            throw AclErrorException.build(403);
        }
    }

    @Override
    public void destroy() {
    }
}

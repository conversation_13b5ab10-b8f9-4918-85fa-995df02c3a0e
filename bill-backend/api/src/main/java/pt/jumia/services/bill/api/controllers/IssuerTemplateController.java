package pt.jumia.services.bill.api.controllers;


import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.bill.api.payloads.request.IssuerTemplateApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.IssuerTemplateApiResponsePayload;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.documents.CreateIssuerTemplateUseCase;
import pt.jumia.services.bill.domain.utils.JsonUtils;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/api/issuer-template")
@Slf4j
@RequiredArgsConstructor
public class IssuerTemplateController {

    private final JsonUtils jsonUtils;
    private final UserPermissionValidationUseCase userPermissionValidationUseCase;
    private final CreateIssuerTemplateUseCase createIssuerTemplateUseCase;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public IssuerTemplateApiResponsePayload create(@RequestBody @Valid IssuerTemplateApiRequestPayload requestPayload) {
        log.debug("'{}' creating a new issuer template: {}",
                RequestContext.getUsername(), jsonUtils.toJsonOrNull(requestPayload));

        checkCanManageByCountryOrThrow(CountryCode.valueOf(requestPayload.getAddress().getCountry()));

        IssuerTemplate issuerTemplate = createIssuerTemplateUseCase.execute(requestPayload.toEntity());

        return new IssuerTemplateApiResponsePayload(issuerTemplate);
    }

    private void checkCanManageByCountryOrThrow(CountryCode countryCode) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanManageDocumentsByCountryCodeOrThrow(RequestContext.getUser(), countryCode);
    }

}

package pt.jumia.services.bill.api.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.report.DailyIssuedDocumentsReportUseCase;
import pt.jumia.services.bill.domain.usecases.report.DailyReceivedDocumentsReportUseCase;

import java.time.LocalDateTime;

@RestController
@RequestMapping(value = "/api/report")
@RequiredArgsConstructor
@Slf4j
public class ReportController {

    private final DailyIssuedDocumentsReportUseCase dailyIssuedDocumentsReportUseCase;
    private final DailyReceivedDocumentsReportUseCase dailyReceivedDocumentsReportUseCase;
    private final UserPermissionValidationUseCase userPermissionValidationUseCase;

    @PostMapping(value = "/daily")
    public void generateDailyReport() {
        /* make sure that this user has access to view at least one country. */
        getCountriesCanViewDocumentsOrThrow();
        dailyIssuedDocumentsReportUseCase.generateDailyReport(LocalDateTime.now());
    }

    @PostMapping(value = "/review-received-documents")
    public void generateReviewReceivedDocumentsReport() {
        /* make sure that this user has access to view at least one country. */
        getCountriesCanViewReceivedDocumentsOrThrow();
        dailyReceivedDocumentsReportUseCase.generateReport(LocalDateTime.now());
    }

    private void getCountriesCanViewDocumentsOrThrow() throws UserForbiddenException {
        userPermissionValidationUseCase.getCountriesCanViewDocumentsOrThrow(RequestContext.getUser());
    }

    private void getCountriesCanViewReceivedDocumentsOrThrow() throws UserForbiddenException {
        userPermissionValidationUseCase.getCountriesCanViewReceivedDocumentsOrThrow(RequestContext.getUser());
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.IssuerType;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class IssuerTemplateApiResponsePayload {

    Long id;
    IssuerType type;
    String shop;

    //
    // Identification
    //

    String legalName;
    String name;
    String taxIdentificationNumber;
    String businessRegistrationNumber;
    String branch;

    //
    // Contact
    //

    AddressApiResponsePayload address;
    String email;
    String mobilePhone;
    String linePhone;

    //
    // Audit information
    //

    LocalDateTime createdAt;
    String createdBy;
    LocalDateTime updatedAt;
    String updatedBy;

    public IssuerTemplateApiResponsePayload(IssuerTemplate issuerTemplate){
        this.id = issuerTemplate.getId();
        this.type = issuerTemplate.getType();
        this.shop = issuerTemplate.getShop();
        this.legalName = issuerTemplate.getLegalName();
        this.name = issuerTemplate.getName();
        this.taxIdentificationNumber = issuerTemplate.getTaxIdentificationNumber();
        this.businessRegistrationNumber = issuerTemplate.getBusinessRegistrationNumber();
        this.branch = issuerTemplate.getBranch();
        this.email = issuerTemplate.getEmail();
        this.mobilePhone = issuerTemplate.getMobilePhone();
        this.linePhone = issuerTemplate.getLinePhone();
        this.address = issuerTemplate.getAddress() != null ? new AddressApiResponsePayload(issuerTemplate.getAddress()) : null;
        this.createdAt = issuerTemplate.getCreatedAt();
        this.createdBy = issuerTemplate.getCreatedBy();
        this.updatedAt = issuerTemplate.getUpdatedAt();
        this.updatedBy = issuerTemplate.getUpdatedBy();
    }

    public IssuerTemplate toEntity() {
        return IssuerTemplate.builder()
                .id(id)
                .type(type)
                .shop(shop)
                .legalName(legalName)
                .name(name)
                .taxIdentificationNumber(taxIdentificationNumber)
                .businessRegistrationNumber(businessRegistrationNumber)
                .branch(branch)
                .address(address != null ? address.toEntity() : null)
                .email(email)
                .mobilePhone(mobilePhone)
                .linePhone(linePhone)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.ConvertibleToDomain;
import pt.jumia.services.bill.domain.entities.Setting;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class SettingResponsePayload implements ConvertibleToDomain<Setting> {

    private Long id;
    private String property;
    private String type;
    private String overrideKey;
    private String description;
    private String value;
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    public SettingResponsePayload(Setting setting) {

        this.id = setting.getId();
        this.property = setting.getProperty();
        this.type = setting.getType().name();
        this.overrideKey = setting.getOverrideKey();
        this.description = setting.getDescription();
        this.value = setting.getValue();
        this.createdAt = setting.getCreatedAt();
        this.createdBy = setting.getCreatedBy();
        this.updatedAt = setting.getUpdatedAt();
        this.updatedBy = setting.getUpdatedBy();
    }

    @Override
    public Setting toEntity() {
        return Setting
                .builder()
                .id(id)
                .property(property)
                .type(Setting.Type.valueOf(type))
                .overrideKey(overrideKey)
                .description(description)
                .value(value)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}

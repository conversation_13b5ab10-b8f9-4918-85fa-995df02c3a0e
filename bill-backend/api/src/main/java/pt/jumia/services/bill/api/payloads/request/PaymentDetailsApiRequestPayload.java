package pt.jumia.services.bill.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.PaymentDetails;

@Data
@NoArgsConstructor
public class PaymentDetailsApiRequestPayload {

    private String bankName;
    private String bankAddress;
    private String bankAccountNo;
    private String bankAccountIBAN;
    private String swiftCode;
    private String terms;

    public PaymentDetailsApiRequestPayload(PaymentDetails entity) {
        this.bankName = entity.getBankName();
        this.bankAddress = entity.getBankAddress();
        this.bankAccountNo = entity.getBankAccountNo();
        this.bankAccountIBAN = entity.getBankAccountIBAN();
        this.swiftCode = entity.getSwiftCode();
        this.terms = entity.getTerms();
    }

    public PaymentDetails toEntity() {
        return PaymentDetails
                .builder()
                .bankName(this.bankName)
                .bankAddress(this.bankAddress)
                .bankAccountNo(this.bankAccountNo)
                .bankAccountIBAN(this.bankAccountIBAN)
                .swiftCode(this.swiftCode)
                .terms(this.terms)
                .generateId()
                .build();
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@NoArgsConstructor
public class TaxCategoryTotalApiResponsePayload {

    //
    // Internal fields
    //

    private UUID id;

    //
    // Tax information
    //

    private TaxCategory taxCategory;
    private BigDecimal taxRate;
    private BigDecimal taxFixedAmount;

    //
    // Summary
    //

    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;

    public TaxCategoryTotalApiResponsePayload(TaxCategoryTotal taxCategoryTotal) {
        this.id = taxCategoryTotal.getId();
        this.taxCategory = taxCategoryTotal.getTaxCategory();
        this.taxRate = taxCategoryTotal.getTaxRate();
        this.taxFixedAmount = taxCategoryTotal.getTaxFixedAmount();
        this.totalAmount = taxCategoryTotal.getTotalAmount();
        this.netAmount = taxCategoryTotal.getNetAmount();
        this.taxAmount = taxCategoryTotal.getTaxAmount();
    }

    public TaxCategoryTotal toEntity(Document document) {
        return TaxCategoryTotal.builder()
                .id(id)
                .document(document)
                .taxCategory(taxCategory)
                .taxRate(taxRate)
                .taxFixedAmount(taxFixedAmount)
                .totalAmount(totalAmount)
                .netAmount(netAmount)
                .taxAmount(taxAmount)
                .build();
    }
}

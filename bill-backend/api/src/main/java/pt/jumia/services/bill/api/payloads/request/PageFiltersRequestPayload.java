package pt.jumia.services.bill.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
@NoArgsConstructor
public class PageFiltersRequestPayload {
    @Min(1)
    @Max(10000)
    private Integer page = 1;
    @Min(1)
    @Max(1000)
    private Integer size = 20;

    public PageFiltersRequestPayload(PageFilters pageFilters) {
        this.page = pageFilters.getPage();
        this.size = pageFilters.getSize();
    }

    public PageFilters toEntity() {
        return PageFilters.builder().page(page).size(size).build();
    }
}

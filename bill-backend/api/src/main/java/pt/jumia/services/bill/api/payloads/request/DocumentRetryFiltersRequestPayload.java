package pt.jumia.services.bill.api.payloads.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.neovisionaries.i18n.CountryCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.filters.DocumentRetryFilters;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DocumentRetryFiltersRequestPayload {

    private String id;
    private String relatedEntityId;
    private String relatedEntityCode;
    private String type;
    private String shop;
    @NotNull
    @ValidEnumValue(enumClass = CountryCode.class)
    private String country;
    private List<String> statuses;
    private Integer statusCode;
    private String errorCode;
    private Integer nDays;
    private LocalDateTime createdAtFrom;
    private LocalDateTime createdAtTo;
    private LocalDateTime issuedDateFrom;
    private LocalDateTime issuedDateTo;

    public DocumentRetryFilters toEntity() {
        return DocumentRetryFilters.builder()
                .id(id)
                .relatedEntityId(UUID.fromString(relatedEntityId))
                .relatedEntityCode(relatedEntityCode)
                .type(type == null ? null : DocumentType.valueOf(type))
                .shop(shop)
                .country(country == null ? null : CountryCode.valueOf(country))
                .statuses(statuses == null ? null : statuses.stream().map(DocumentStatus::valueOf).collect(Collectors.toList()))
                .statusCode(statusCode)
                .errorCode(errorCode)
                .createdAtFrom(createdAtFrom)
                .createdAtTo(createdAtTo)
                .issuedDateFrom(issuedDateFrom)
                .issuedDateTo(issuedDateTo)
                .build();
    }
}

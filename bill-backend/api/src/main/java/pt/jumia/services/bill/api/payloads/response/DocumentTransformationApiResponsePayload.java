package pt.jumia.services.bill.api.payloads.response;


import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;

import java.time.LocalDateTime;
@Data
@NoArgsConstructor
public class DocumentTransformationApiResponsePayload {

    private Long id;

    private String oldValue;

    private String newValue;

    private String type;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;


    public DocumentTransformationApiResponsePayload(DocumentTransformation documentTransformation) {
        this.id = documentTransformation.getId();
        this.type = documentTransformation.getType() != null ? documentTransformation.getType().name() : null;
        this.newValue = documentTransformation.getNewValue();
        this.oldValue = documentTransformation.getOriginalValue();
        this.createdAt = documentTransformation.getCreatedAt();
        this.updatedAt = documentTransformation.getUpdatedAt();
    }

    public DocumentTransformation toEntity(Document document) {
        return DocumentTransformation.builder()
                .id(id)
                .document(document)
                .type(type == null ? null : DocumentTransformation.EntityType.valueOf(type))
                .originalValue(oldValue)
                .newValue(newValue)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();
    }
}

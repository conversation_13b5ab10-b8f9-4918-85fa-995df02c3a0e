package pt.jumia.services.bill.api.validations.annotations;

import pt.jumia.services.bill.api.validations.validators.PercentRateValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;

@Documented
@Target({FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {PercentRateValidator.class})
public @interface ValidPercentRate {

    String message() default "Not a valid Percent rate, it should be between 0.0 and 1.0!";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    boolean required() default false;
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class ReceiverApiResponsePayload {

    UUID id;
    ReceiverType type;

    //
    // Identification
    //

    String legalName;
    String name;
    String nationalIdentificationNumber;
    String taxIdentificationNumber;
    String businessRegistrationNumber;

    //
    // Contact
    //

    AddressApiResponsePayload address;
    String email;
    String mobilePhone;
    String linePhone;

    //
    // Audit information
    //

    LocalDateTime createdAt;
    String createdBy;

    public ReceiverApiResponsePayload(Receiver receiver){
        this.id = receiver.getId();
        this.type = receiver.getType();
        this.legalName = receiver.getLegalName();
        this.name = receiver.getName();
        this.nationalIdentificationNumber = receiver.getNationalIdentificationNumber();
        this.taxIdentificationNumber = receiver.getTaxIdentificationNumber();
        this.businessRegistrationNumber = receiver.getBusinessRegistrationNumber();
        this.email = receiver.getEmail();
        this.mobilePhone = receiver.getMobilePhone();
        this.linePhone = receiver.getLinePhone();
        this.address = receiver.getAddress() != null ? new AddressApiResponsePayload(receiver.getAddress()) : null;
        this.createdAt = receiver.getCreatedAt();
        this.createdBy = receiver.getCreatedBy();
    }

    public Receiver toEntity() {
        return Receiver.builder()
                .id(id)
                .type(type)
                .legalName(legalName)
                .name(name)
                .nationalIdentificationNumber(nationalIdentificationNumber)
                .taxIdentificationNumber(taxIdentificationNumber)
                .businessRegistrationNumber(businessRegistrationNumber)
                .address(address != null ? address.toEntity() : null)
                .email(email)
                .mobilePhone(mobilePhone)
                .linePhone(linePhone)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .build();
    }
}

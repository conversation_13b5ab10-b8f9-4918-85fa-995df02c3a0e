package pt.jumia.services.bill.api.export.csv;

import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import com.opencsv.exceptions.CsvDataTypeMismatchException;
import com.opencsv.exceptions.CsvRequiredFieldEmptyException;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletResponse;
import java.io.Writer;
import java.util.List;

public class CsvUtils {

    public static <T> void printCsv(String filename, List<T> entitiesList, HttpServletResponse response,
                                    Class<T> mappingClassName) throws Exception {
        response.setContentType("text/csv");
        response.setStatus(HttpServletResponse.SC_OK);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=" + filename);
        //create a csv writer
        CsvUtils.convertToCsv(mappingClassName, entitiesList, response.getWriter());
    }

    private static <T> void convertToCsv(Class<T> mappingClassName, List<T> entitiesList, Writer writer)
            throws CsvDataTypeMismatchException, CsvRequiredFieldEmptyException {
        HeaderColumnNameAndOrderMappingStrategy<T> mappingStrategy = new HeaderColumnNameAndOrderMappingStrategy<>();
        mappingStrategy.setType(mappingClassName);

        StatefulBeanToCsv<T> csv = new StatefulBeanToCsvBuilder<T>(writer)
                .withMappingStrategy(mappingStrategy)
                .withOrderedResults(true)
                .build();
        //write to csv file
        csv.write(entitiesList);
    }
}

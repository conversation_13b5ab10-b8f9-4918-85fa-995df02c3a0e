package pt.jumia.services.bill.api.filters;

import org.springframework.stereotype.Component;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * As spring read the HttpServletRequest's input stream, we cannot read the raw request body again.
 * This filter uses a {@link ContentCachingRequestWrapper} to allow us to access the raw request
 * body again.
 */
@Component
public class RequestCacheFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws IOException, ServletException {
        if (request instanceof HttpServletRequest) {
            HttpServletRequest requestToCache = new ContentCachingRequestWrapper((HttpServletRequest) request);
            chain.doFilter(requestToCache, response);
        } else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}

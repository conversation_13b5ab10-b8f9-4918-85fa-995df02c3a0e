package pt.jumia.services.bill.api.payloads.request;

import com.neovisionaries.i18n.CountryCode;
import com.neovisionaries.i18n.CurrencyCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Currency;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DocumentFiltersRequestPayload {

    @ValidEnumValue(enumClass = Document.Details.class)
    private List<String> include;
    private UUID id;
    private String sid;
    private String taxDocumentNumber;
    @ValidEnumValue(enumClass = DocumentFlow.class)
    private String flow;
    private String referenceNumber;
    private String issuerTin;

    private String issuerLegalName;
    private String receiverTin;

    private LocalDateTime createdAtFrom;
    private LocalDateTime createdAtTo;

    private LocalDateTime issuedDateFrom;
    private LocalDateTime issuedDateTo;

    private LocalDateTime receivedDateFrom;

    private LocalDateTime receivedDateTo;

    private String shop;
    @ValidEnumValue(enumClass = DocumentTransformation.EntityType.class)
    private List<String> documentTransformationTypes;
    @ValidEnumValue(enumClass = CountryCode.class)
    private String countryCode;
    @ValidEnumValue(enumClass = CountryCode.class)
    private String receiverCountryCode;
    @ValidEnumValue(enumClass = ReceiverType.class)
    private String receiverType;
    private String receiverName;
    @ValidEnumValue(enumClass = DocumentStatus.class)
    private String status;
    @ValidEnumValue(enumClass = DocumentType.class)
    private String type;
    @ValidEnumValue(enumClass = CurrencyCode.class)
    private String currency;
    private Boolean reviewed;
    private LocalDateTime updatedAtFrom;
    private LocalDateTime updatedAtTo;

    public DocumentFiltersRequestPayload(DocumentFilter documentAggregatesFilters) {
        this.id = documentAggregatesFilters.getId();
        this.include = Objects.isNull(documentAggregatesFilters.getInclude()) ?
                List.of(Arrays.toString(Document.Details.values())) :
                documentAggregatesFilters.getInclude().stream()
                        .map(Document.Details::name).collect(Collectors.toList());
        this.sid = documentAggregatesFilters.getSid();
        this.taxDocumentNumber = documentAggregatesFilters.getTaxDocumentNumber();
        this.flow = documentAggregatesFilters.getFlow().name();
        this.referenceNumber = documentAggregatesFilters.getReferenceNumber();
        this.issuerTin = documentAggregatesFilters.getIssuerTin();
        this.issuerLegalName = documentAggregatesFilters.getIssuerLegalName();
        this.receiverTin = documentAggregatesFilters.getReceiverTin();
        this.createdAtFrom = documentAggregatesFilters.getCreatedAtFrom();
        this.createdAtTo = documentAggregatesFilters.getCreatedAtTo();
        this.issuedDateFrom = documentAggregatesFilters.getIssuedDateFrom();
        this.issuedDateTo = documentAggregatesFilters.getIssuedDateTo();
        this.documentTransformationTypes = Objects.isNull(documentAggregatesFilters.getDocumentTransformationTypes()) ?
                List.of() :
                documentAggregatesFilters.getDocumentTransformationTypes().stream()
                        .map(DocumentTransformation.EntityType::name).collect(Collectors.toList());
        this.countryCode = documentAggregatesFilters.getCountryCode().name();
        this.receiverCountryCode = documentAggregatesFilters.getReceiverCountry().name();
        this.receiverName = documentAggregatesFilters.getReceiverName();
        this.receiverType = documentAggregatesFilters.getReceiverType().name();
        this.currency = documentAggregatesFilters.getCurrency().getCurrencyCode();
        this.status = documentAggregatesFilters.getStatus().name();
        this.type = documentAggregatesFilters.getType().name();
        this.receivedDateFrom = documentAggregatesFilters.getReceivedDateFrom();
        this.receivedDateTo = documentAggregatesFilters.getReceivedDateTo();
        this.reviewed = documentAggregatesFilters.getReviewed();
        this.updatedAtFrom = documentAggregatesFilters.getUpdatedAtFrom();
        this.updatedAtTo = documentAggregatesFilters.getUpdatedAtTo();
        this.shop = documentAggregatesFilters.getShop();
    }

    public DocumentFilter toEntity() {
        return DocumentFilter.builder()
                .id(this.id)
                .include(CollectionUtils.isEmpty(include)
                        ? List.of(Document.Details.values())
                        : include.stream().map(Document.Details::valueOf).collect(Collectors.toList()))
                .flow(flow == null ? null : DocumentFlow.valueOf(flow))
                .sid(sid)
                .taxDocumentNumber(taxDocumentNumber)
                .referenceNumber(referenceNumber)
                .issuerTin(issuerTin)
                .issuerLegalName(this.issuerLegalName)
                .receiverTin(receiverTin)
                .createdAtFrom(createdAtFrom)
                .createdAtTo(createdAtTo)
                .issuedDateFrom(issuedDateFrom)
                .issuedDateTo(issuedDateTo)
                .documentTransformationTypes(CollectionUtils.isEmpty(documentTransformationTypes)
                        ? List.of()
                        : documentTransformationTypes.stream().map(DocumentTransformation.EntityType::valueOf)
                        .collect(Collectors.toList()))
                .countryCode(countryCode == null ? null : CountryCode.valueOf(countryCode))
                .receiverCountry(receiverCountryCode == null ? null : CountryCode.valueOf(receiverCountryCode))
                .receiverName(receiverName)
                .receiverType(receiverType == null ? null : ReceiverType.valueOf(receiverType))
                .currency(currency == null ? null : Currency.getInstance(this.currency))
                .status(status == null ? null : DocumentStatus.valueOf(status))
                .type(type == null ? null : DocumentType.valueOf(type))
                .receivedDateFrom(receivedDateFrom)
                .receivedDateTo(receivedDateTo)
                .reviewed(this.reviewed)
                .updatedAtFrom(this.updatedAtFrom)
                .updatedAtTo(this.updatedAtTo)
                .shop(this.shop)
                .build();
    }
}

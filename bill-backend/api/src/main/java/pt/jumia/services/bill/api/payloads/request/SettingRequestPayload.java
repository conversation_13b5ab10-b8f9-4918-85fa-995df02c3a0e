package pt.jumia.services.bill.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.ConvertibleToDomain;
import pt.jumia.services.bill.domain.entities.Setting;

import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
public class SettingRequestPayload implements ConvertibleToDomain<Setting> {

    @NotEmpty
    private String property;
    @ValidEnumValue(required = true, enumClass = Setting.Type.class)
    private String type;
    private String overrideKey;
    private String description;
    @NotEmpty
    private String value;

    public SettingRequestPayload(Setting setting) {
        this.property = setting.getProperty();
        this.type = setting.getType().name();
        this.overrideKey = setting.getOverrideKey();
        this.description = setting.getDescription();
        this.value = setting.getValue();
    }

    @Override
    public Setting toEntity() {
        return Setting
                .builder()
                .property(property)
                .type(Setting.Type.valueOf(type))
                .overrideKey(overrideKey)
                .description(description)
                .value(value)
                .build();
    }
}

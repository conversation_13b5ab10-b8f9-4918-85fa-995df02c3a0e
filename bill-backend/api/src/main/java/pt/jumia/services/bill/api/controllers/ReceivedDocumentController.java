package pt.jumia.services.bill.api.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.bill.api.payloads.request.RejectionReasonRequestPayload;
import pt.jumia.services.bill.api.payloads.request.SendEmailRequestPayload;
import pt.jumia.services.bill.api.payloads.request.TaxAuthoritiesDetailsApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentAllowedOperationsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DownloadDocumentFromTaxAuthoritiesUseCase;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.domain.usecases.documents.SendEmailDocumentToReceiverUseCase;
import pt.jumia.services.bill.domain.usecases.documents.UpdateDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.receiveddocuments.ApproveReceivedDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.receiveddocuments.DeclineReceivedDocumentCancellationUseCase;
import pt.jumia.services.bill.domain.usecases.receiveddocuments.RejectReceivedDocumentUseCase;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@RestController
@RequestMapping(value = "/api/received-documents")
@Slf4j
@RequiredArgsConstructor
public class ReceivedDocumentController {
    private final UserPermissionValidationUseCase userPermissionValidationUseCase;
    private final UpdateDocumentUseCase updateDocumentUseCase;
    private final RejectReceivedDocumentUseCase rejectReceivedDocumentUseCase;
    private final ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    private final DeclineReceivedDocumentCancellationUseCase declineCancellationReceivedDocumentUseCase;
    private final ApproveReceivedDocumentUseCase approveReceivedDocumentUseCase;
    private final DownloadDocumentFromTaxAuthoritiesUseCase downloadDocumentFromTaxAuthoritiesUseCase;
    private final DocumentAllowedOperationsUseCase documentAllowedOperationsUseCase;
    private final SendEmailDocumentToReceiverUseCase sendEmailDocumentToReceiverUseCase;

    @GetMapping("/{receivedDocumentId}/tax-authorities-pdf")
    public ResponseEntity<InputStreamResource> fetchTaxAuthoritiesPdf(
            @PathVariable(value = "receivedDocumentId") @NotEmpty UUID receivedDocumentId) throws UserForbiddenException {

        log.debug("'{}' getting received document from Tax Authorities by ID {}",
                RequestContext.getUsername(), receivedDocumentId);

        DocumentAggregate receivedDocument = readDocumentAggregateUseCase.execute(receivedDocumentId);
        Document document = receivedDocument.getDocument();
        checkCanViewByCountryOrThrow(document.getCountry());

        DocumentFile documentFromTaxAuthorities = downloadDocumentFromTaxAuthoritiesUseCase.execute(document
                .getId().toString());

        return convertToInputStream(documentFromTaxAuthorities);
    }

    @PostMapping("/{receivedDocumentId}/review/{reviewFlag}")
    public DocumentApiResponsePayload review(@PathVariable(value = "receivedDocumentId") UUID receivedDocumentId,
                                             @PathVariable(value = "reviewFlag") Boolean isReviewed) {
        log.debug("'{}' changed review flag for received document by ID: {}", RequestContext.getUsername(), receivedDocumentId);
        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(receivedDocumentId);
        Document document = documentAggregate.getDocument();

        userPermissionValidationUseCase.checkCanReviewReceivedDocumentsByCountryCodeOrThrow(
                RequestContext.getUser(),
                document.getCountry()
        );

        Document documentFlag = updateDocumentUseCase.execute(receivedDocumentId,
                document.toBuilder()
                        .reviewed(isReviewed)
                        .reviewedAt(LocalDateTime.now(ZoneOffset.UTC))
                        .reviewedBy(RequestContext.getUsername()).build(),
                false
        );
        return documentWithPossibleOperations(documentFlag);
    }

    @PutMapping("/{receivedDocumentId}/reject")
    public DocumentApiResponsePayload reject(
            @PathVariable(value = "receivedDocumentId") UUID receivedDocumentId,
            @RequestBody @Valid RejectionReasonRequestPayload rejectionReasonPayload
    ) {
        log.debug("'{}' is rejecting received document by ID: {}", RequestContext.getUsername(), receivedDocumentId);
        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(receivedDocumentId);
        Document document = documentAggregate.getDocument();

        userPermissionValidationUseCase.checkCanRejectReceivedDocumentsByCountryCodeOrThrow(
                RequestContext.getUser(),
                document.getCountry()
        );

        Document documentRejection = rejectReceivedDocumentUseCase.execute(documentAggregate, rejectionReasonPayload.getRejectionReason());
        return documentWithPossibleOperations(documentRejection);
    }

    @PutMapping("/{receivedDocumentId}/decline-cancellation")
    public DocumentApiResponsePayload declineCancellation(
            @PathVariable(value = "receivedDocumentId") UUID receivedDocumentId
    ) {
        log.debug("'{}' is declining the cancellation of the received document by ID: {}",
                RequestContext.getUsername(), receivedDocumentId);

        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(receivedDocumentId);
        Document document = documentAggregate.getDocument();

        userPermissionValidationUseCase.checkCanDeclineReceivedDocumentsCancellationByCountryCodeOrThrow(
                RequestContext.getUser(),
                document.getCountry()
        );

        Document documentDeclineCancellation = declineCancellationReceivedDocumentUseCase.execute(documentAggregate);

        return documentWithPossibleOperations(documentDeclineCancellation);
    }

    @PutMapping("/{receivedDocumentId}/approve")
    public DocumentApiResponsePayload approved(
            @PathVariable(value = "receivedDocumentId") UUID receivedDocumentId
    ) {
        log.debug("'{}' is approving received document by ID: {}", RequestContext.getUsername(), receivedDocumentId);
        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(receivedDocumentId);
        Document document = documentAggregate.getDocument();

        userPermissionValidationUseCase.checkCanApprovedReceivedDocumentsByCountryCodeOrThrow(
                RequestContext.getUser(),
                document.getCountry()
        );

        Document approvedDocument = approveReceivedDocumentUseCase.execute(documentAggregate);
        return documentWithPossibleOperations(approvedDocument);
    }

    /**
     * @description this api is used for the call back from taxi to update the status of the received document
     */
    @PutMapping("/{receivedDocumentId}/update-status/{newStatus}")
    @ResponseStatus(HttpStatus.OK)
    public void updateStatus(
            @PathVariable(value = "receivedDocumentId") UUID receivedDocumentId,
            @PathVariable(value = "newStatus") TaxAuthoritiesDetailsApiRequestPayload.ApiLogStatus
                    newStatus
    ) {
        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(receivedDocumentId);
        Document document = documentAggregate.getDocument();

        userPermissionValidationUseCase.checkCanCreateReceivedDocumentsByCountryCodeOrThrow(
                RequestContext.getUser(),
                document.getCountry()
        );

        updateDocumentUseCase.execute(document.getId(), document.toBuilder()
                .status(newStatus.getDocumentStatus())
                .build(), true);
    }

    @PostMapping("/{documentId}/send-email")
    public void sendReceivedDocumentsEmail(@PathVariable(value = "documentId") UUID documentId,
                                           @Valid @RequestBody SendEmailRequestPayload sendEmailRequestPayload)
            throws JsonProcessingException {

        log.debug("'{}' sending email for document with ID: {}", RequestContext.getUsername(), documentId);

        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(documentId);
        checkCanSendDocumentsThroughEmail(documentAggregate.getDocument().getCountry());
        sendEmailDocumentToReceiverUseCase.execute(documentAggregate, sendEmailRequestPayload.getEmails());
    }

    private DocumentApiResponsePayload documentWithPossibleOperations(Document document) {
        return new DocumentApiResponsePayload(
                documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document)
        );
    }

    private ResponseEntity<InputStreamResource> convertToInputStream(DocumentFile documentFile) {
        InputStreamResource inputStreamResource = new InputStreamResource(new ByteArrayInputStream(documentFile.getContent()));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentLength(documentFile.getContent().length);
        httpHeaders.setContentType(MediaType.parseMediaType(documentFile.getMediaType()));
        return new ResponseEntity<>(inputStreamResource, httpHeaders, HttpStatus.OK);
    }

    private void checkCanViewByCountryOrThrow(CountryCode country) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanListReceivedDocumentsByCountryCodeOrThrow(RequestContext.getUser(), country);
    }

    private void checkCanSendDocumentsThroughEmail(CountryCode countryCode) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanSendDocumentsThroughEmailByCountryCodeOrThrow(RequestContext.getUser(),
                countryCode);
    }
}

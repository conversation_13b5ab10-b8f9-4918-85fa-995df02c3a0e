package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentId;

import java.util.UUID;

@Data
@NoArgsConstructor
public class DocumentIdApiResponsePayload {

    private UUID id;
    private String sid;

    public DocumentIdApiResponsePayload(DocumentId documentId) {
        id = documentId.getId();
        sid = documentId.getSid();
    }

    public DocumentId toEntity() {
        return DocumentId.builder()
                .id(id)
                .sid(sid)
                .build();
    }
}

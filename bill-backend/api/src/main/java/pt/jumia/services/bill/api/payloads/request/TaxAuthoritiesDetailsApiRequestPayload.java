package pt.jumia.services.bill.api.payloads.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.GhDocPdfRequestPayloadInfo;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.dtos.OverrideFields;
import pt.jumia.services.bill.domain.entities.dtos.TaxAuthoritiesDetailsUpdateDto;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@JsonIgnoreProperties
public class TaxAuthoritiesDetailsApiRequestPayload {

    private String submissionId;
    private String taxDocumentNumber;
    private String qrCode;
    private String verificationCode;
    private String deviceNumber;
    private String internalData;
    private String exception;
    private String statusCode;
    private String errorCode;
    @ValidEnumValue(enumClass = ApiLogStatus.class, required = true)
    private String apiLogStatus;
    private OverrideFieldsRequestPayload overrideFields;
    private GhDocPdfRequestPayloadInfo docPdfFields;
    private List<DocumentTransformationApiRequestPayload> documentTransformations;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class OverrideFieldsRequestPayload {
        private String buyerLegalName;
        private String invoiceNumberInsideTaxAuthorities;
        private Map<String, String> taxCategoriesMap;

        public OverrideFields toEntity() {
            return OverrideFields
                    .builder()
                    .invoiceNumberInsideTaxAuthorities(invoiceNumberInsideTaxAuthorities)
                    .buyerLegalName(buyerLegalName)
                    .taxCategoriesMap(taxCategoriesMap)
                    .build();
        }
    }

    public enum ApiLogStatus {
        ACCEPTED(DocumentStatus.TAX_PENDING),
        PENDING(DocumentStatus.TAX_PENDING),
        SKIPPED(DocumentStatus.TAX_SKIPPED),
        SUCCESS(DocumentStatus.TAX_SUCCESS),
        FAILED_MAPPING(DocumentStatus.TAXI_FAILED_MAPPING),
        FAILED_FETCH_ITEM(DocumentStatus.TAXI_FAILED_FETCH_ITEM),
        FAILED_REQUEST(DocumentStatus.TAX_FAILED_REQUEST),
        FAILED_RESPONSE(DocumentStatus.TAX_FAILED_RESPONSE),
        SUBMITTED_INVALID(DocumentStatus.TAX_SUBMITTED_INVALID),
        REJECTED(DocumentStatus.TAX_REJECTED),
        CANCELLED(DocumentStatus.TAX_CANCELLED),
        RETRIED(DocumentStatus.TAX_ERROR_RETRIED),
        ERROR_ACKED(DocumentStatus.TAX_ERROR_ACKED);

        private final DocumentStatus documentStatus;

        ApiLogStatus(DocumentStatus documentStatus) {
            this.documentStatus = documentStatus;
        }

        public DocumentStatus getDocumentStatus() {
            return this.documentStatus;
        }
    }

    public TaxAuthoritiesDetails toEntity(Document document) {
        return TaxAuthoritiesDetails.builder()
                .document(document)
                .submissionId(submissionId)
                .taxDocumentNumber(taxDocumentNumber)
                .qrCode(qrCode)
                .verificationCode(verificationCode)
                .internalData(internalData)
                .deviceNumber(deviceNumber)
                .documentTransformations(CollectionUtils.isEmpty(documentTransformations) ? null : documentTransformations.stream()
                        .map(DocumentTransformationApiRequestPayload::toEntity).collect(Collectors.toList()))
                .statusCode(statusCode)
                .errorCode(errorCode)
                .exception(exception)
                .generateId()
                .docPdfFields(docPdfFields == null ? null : docPdfFields.toEntity())
                .build();
    }

    public TaxAuthoritiesDetailsUpdateDto toEntityUpdateDto(UUID documentId) {
        return TaxAuthoritiesDetailsUpdateDto.builder()
                .documentId(documentId)
                .documentStatus(ApiLogStatus.valueOf(apiLogStatus).getDocumentStatus())
                .taxAuthoritiesDetails(TaxAuthoritiesDetails
                        .builder()
                        .submissionId(submissionId)
                        .taxDocumentNumber(taxDocumentNumber)
                        .qrCode(qrCode)
                        .verificationCode(verificationCode)
                        .internalData(internalData)
                        .deviceNumber(deviceNumber)
                        .statusCode(statusCode)
                        .errorCode(errorCode)
                        .exception(exception)
                        .generateId()
                        .documentTransformations(CollectionUtils.isEmpty(documentTransformations) ? null
                                : documentTransformations.stream()
                                .map(DocumentTransformationApiRequestPayload::toEntity).collect(Collectors.toList()))
                        .docPdfFields(docPdfFields == null ? null : docPdfFields.toEntity())
                        .build())
                .overrideFields(overrideFields == null ? null : overrideFields.toEntity())
                .build();
    }
}

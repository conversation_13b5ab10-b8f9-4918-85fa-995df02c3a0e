package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidPercentRate;
import pt.jumia.services.bill.domain.entities.DocumentLine;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DiscountApiRequestPayload {

    private BigDecimal amount;
    @ValidPercentRate
    private BigDecimal rate;

    public DiscountApiRequestPayload(DocumentLine.Discount discount) {
        this.amount = discount.getAmount();
        this.rate = discount.getRate();
    }

    public DocumentLine.Discount toEntity() {
        return DocumentLine.Discount.builder()
                .amount(amount)
                .rate(rate)
                .build();
    }

}

package pt.jumia.services.bill.api.utils;

import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import pt.jumia.services.bill.api.payloads.response.PageResponsePayload;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Service
public class PaginationService {

    public <E> PageResponsePayload<E> buildPageResponsePayload(
            HttpServletRequest request, PageFilters pageFilters, List<E> results) {

        // work around: the query is returning one more entry
        // in order to verify if there is a next page
        boolean hasNextPage = false;
        if (results.size() > pageFilters.getSize()) {
            hasNextPage = true;
            results.remove(results.size() - 1);
        }

        String baseUrl = getBaseUrl(request);
        String endpoint = getEndpoint(request);

        String previousPage = buildPreviousPage(pageFilters, endpoint);
        String nextPage = buildNextPage(pageFilters, hasNextPage, endpoint);

        return new PageResponsePayload<>(
                new PageResponsePayload.Links(baseUrl, previousPage, nextPage),
                pageFilters.getPage(), pageFilters.getSize(), results);
    }

    private String getBaseUrl(HttpServletRequest request) {
        return request.getRequestURL().substring(
                0, request.getRequestURL().length() - request.getRequestURI().length()) + request.getContextPath();
    }

    private String getEndpoint(HttpServletRequest request) {
        return request.getQueryString() == null ? request.getRequestURI() :
                request.getRequestURI() + "?" + request.getQueryString();
    }

    @SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
    private String buildPreviousPage(PageFilters pageFilters, String endpoint) {
        if (pageFilters.getPage() <= 1) {
            return null;
        }

        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromUriString(endpoint);
        urlBuilder.replaceQueryParam("page", pageFilters.getPage() - 1);
        return urlBuilder.build().toUriString();
    }

    private String buildNextPage(PageFilters pageFilters, boolean hasNextPage, String endpoint) {
        if (!hasNextPage) {
            return null;
        }

        UriComponentsBuilder urlBuilder = UriComponentsBuilder.fromUriString(endpoint);
        urlBuilder.replaceQueryParam("page", pageFilters.getPage() + 1);
        return urlBuilder.build().toUriString();
    }
}

package pt.jumia.services.bill.api.validations.validators;

import pt.jumia.services.bill.api.validations.annotations.ValidCurrencyCode;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Currency;

public class CurrencyCodeValidator implements ConstraintValidator<ValidCurrencyCode, String> {

    private ValidCurrencyCode params;

    @Override
    public void initialize(ValidCurrencyCode stringEnumeration) {
        this.params = stringEnumeration;
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (this.params.required() && value == null) {
            return false;
        } else if (value == null) {
            return true;
        } else {
            try {
                Currency.getInstance(value);
                return true;
            } catch (IllegalArgumentException e) {
                return false;
            }
        }
    }
}

package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.api.validations.annotations.ValidPercentRate;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class TaxCategoryTotalApiRequestPayload {

    //
    // Tax information
    //

    @ValidEnumValue(required = true, enumClass = TaxCategory.class)
    private String taxCategory;
    @ValidPercentRate
    private BigDecimal taxRate;
    private BigDecimal taxFixedAmount;

    //
    // Summary
    //

    @NotNull
    private BigDecimal totalAmount;
    @NotNull
    private BigDecimal netAmount;
    @NotNull
    private BigDecimal taxAmount;

    public TaxCategoryTotalApiRequestPayload(TaxCategoryTotal entity) {
        this.taxCategory = entity.getTaxCategory().name();
        this.taxRate = entity.getTaxRate();
        this.taxFixedAmount = entity.getTaxFixedAmount();
        this.totalAmount = entity.getTotalAmount();
        this.netAmount = entity.getNetAmount();
        this.taxAmount = entity.getTaxAmount();
    }

    public TaxCategoryTotal toEntity(Document document) {
        return TaxCategoryTotal
                .builder()
                .document(document)
                .taxCategory(TaxCategory.valueOf(this.taxCategory))
                .taxRate(this.taxRate)
                .taxFixedAmount(this.taxFixedAmount)
                .totalAmount(this.totalAmount)
                .netAmount(this.netAmount)
                .taxAmount(this.taxAmount)
                .generateId()
                .build();
    }
}

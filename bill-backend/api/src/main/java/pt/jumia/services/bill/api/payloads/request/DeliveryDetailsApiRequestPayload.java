package pt.jumia.services.bill.api.payloads.request;

import com.neovisionaries.i18n.CountryCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.DeliveryDetails;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class DeliveryDetailsApiRequestPayload {

    private String approach;
    private String packaging;
    private LocalDateTime dateValidity;
    private String exportPort;
    private BigDecimal grossWeight;
    private BigDecimal netWeight;
    @ValidEnumValue(enumClass = CountryCode.class)
    private String countryOfOrigin;
    private String terms;

    public DeliveryDetailsApiRequestPayload(DeliveryDetails entity) {
        this.approach = entity.getApproach();
        this.packaging = entity.getPackaging();
        this.dateValidity = entity.getDateValidity();
        this.exportPort = entity.getExportPort();
        this.grossWeight = entity.getGrossWeight();
        this.netWeight = entity.getNetWeight();
        this.countryOfOrigin = entity.getCountryOfOrigin() != null ?
                entity.getCountryOfOrigin().getAlpha2() : null;
        this.terms = entity.getTerms();
    }

    public DeliveryDetails toEntity() {
        return DeliveryDetails
                .builder()
                .approach(this.approach)
                .packaging(this.packaging)
                .dateValidity(this.dateValidity)
                .exportPort(this.exportPort)
                .grossWeight(this.grossWeight)
                .netWeight(this.netWeight)
                .countryOfOrigin(this.countryOfOrigin == null ? null :
                        CountryCode.valueOf(this.countryOfOrigin))
                .terms(this.terms)
                .generateId()
                .build();
    }

}

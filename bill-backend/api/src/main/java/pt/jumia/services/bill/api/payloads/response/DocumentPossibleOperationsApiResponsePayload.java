package pt.jumia.services.bill.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.Permissions;
import pt.jumia.services.bill.domain.entities.DocumentPossibleOperations;

@Data
@NoArgsConstructor
public class DocumentPossibleOperationsApiResponsePayload {
    @JsonProperty("can_approve")
    private boolean canApprove;
    @JsonProperty("can_ack")
    private boolean canAcknowledge;
    @JsonProperty("can_cancel")
    private boolean canCancel;
    @JsonProperty("can_decline_rejection")
    private boolean canDeclineDocumentRejection;
    @JsonProperty("can_retry_documents")
    private boolean canRetryDocuments;

    public DocumentPossibleOperationsApiResponsePayload
            (DocumentPossibleOperations documentPossibleOperations) {
        this.canApprove = documentPossibleOperations.isCanApprove();
        this.canAcknowledge = documentPossibleOperations.isCanAcknowledge();
        this.canCancel = documentPossibleOperations.isCanCancel();
        this.canDeclineDocumentRejection = documentPossibleOperations.isCanDeclineDocumentRejection();
        this.canRetryDocuments = documentPossibleOperations.isCanRetryDocuments();
    }
}

package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class IssuerApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = IssuerType.class)
    private String type;

    //
    // Identification
    //

    @NotBlank
    private String legalName;
    private String name;
    @NotBlank
    private String taxIdentificationNumber;
    @NotBlank
    private String businessRegistrationNumber;
    private String branch;

    //
    // Contact
    //

    @NotNull
    @Valid
    private AddressApiRequestPayload address;
    @NotBlank
    private String email;
    private String mobilePhone;
    private String linePhone;

    public IssuerApiRequestPayload(Issuer entity) {
        this.type = entity.getType().name();
        this.legalName = entity.getLegalName();
        this.name = entity.getName();
        this.taxIdentificationNumber = entity.getTaxIdentificationNumber();
        this.businessRegistrationNumber = entity.getBusinessRegistrationNumber();
        this.branch = entity.getBranch();
        this.address = entity.getAddress() != null ? new AddressApiRequestPayload(entity.getAddress()) : null;
        this.email = entity.getEmail();
        this.mobilePhone = entity.getMobilePhone();
        this.linePhone = entity.getLinePhone();
    }

    public Issuer toEntity() {
        return Issuer
                .builder()
                .type(IssuerType.valueOf(this.type))
                .legalName(this.legalName)
                .name(this.name)
                .taxIdentificationNumber(this.taxIdentificationNumber)
                .businessRegistrationNumber(this.businessRegistrationNumber)
                .branch(this.branch)
                .address(this.address.toEntity())
                .email(this.email)
                .mobilePhone(this.mobilePhone)
                .linePhone(this.linePhone)
                .generateId()
                .build();
    }
}

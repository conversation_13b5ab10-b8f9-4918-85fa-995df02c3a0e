package pt.jumia.services.bill.api.controllers;


import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.bill.domain.KafkaProducer;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * at 05/17/2022
 */
@RestController
@RequestMapping(value = "/api/kafka")
@AllArgsConstructor
public class KafkaController {
    private final KafkaProducer billProducer;

    @PostMapping(value = "/publish/bill")
    public void publishBill(@RequestBody @Valid BillPayload payload) {
        this.billProducer.sendMessage(payload);
    }
}

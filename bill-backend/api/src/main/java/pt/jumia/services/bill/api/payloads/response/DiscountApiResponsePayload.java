package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentLine;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class DiscountApiResponsePayload {

    private BigDecimal amount;
    private BigDecimal rate;

    public DiscountApiResponsePayload(DocumentLine.Discount discount) {
        this.amount = discount.getAmount();
        this.rate = discount.getRate();
    }

    public DocumentLine.Discount toEntity() {
        return DocumentLine.Discount.builder()
                .amount(amount)
                .rate(rate)
                .build();
    }
}

package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentId;

import javax.validation.constraints.NotBlank;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DocumentIdApiRequestPayload {

    private UUID id;
    @NotBlank
    private String sid;

    public DocumentIdApiRequestPayload(DocumentId entity) {
        this.id = entity.getId();
        this.sid = entity.getSid();
    }

    public DocumentId toEntity() {
        return DocumentId.builder()
                .id(id)
                .sid(sid)
                .build();
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.ItemType;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DocumentLineApiResponsePayload {

    //
    // Internal fields
    //

    private UUID id;

    //
    // General information
    //

    private int position;
    private BigDecimal quantity;
    private UnitOfMeasure unitOfMeasure;

    //
    // Product information
    //

    private String itemCode;
    private String skuVariant;
    private String itemName;
    private ItemType itemType;
    private CategoryApiResponsePayload category;

    //
    // Price information
    //

    private BigDecimal unitPrice;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal totalTaxAmount;
    private List<AppliedTaxApiResponsePayload> appliedTaxes;
    private DiscountApiResponsePayload discount;

    public DocumentLineApiResponsePayload(DocumentLine documentLine) {
        this.id = documentLine.getId();
        this.position = documentLine.getPosition();
        this.quantity = documentLine.getQuantity();
        this.unitOfMeasure = documentLine.getUnitOfMeasure();
        this.itemCode = documentLine.getItemCode();
        this.skuVariant = documentLine.getSkuVariant();
        this.itemName = documentLine.getItemName();
        this.itemType = documentLine.getItemType();
        this.category = new CategoryApiResponsePayload(documentLine.getCategory());
        this.unitPrice = documentLine.getUnitPrice();
        this.totalAmount = documentLine.getTotalAmount();
        this.netAmount = documentLine.getNetAmount();
        this.totalTaxAmount = documentLine.getTotalTaxAmount();
        this.appliedTaxes = documentLine.getAppliedTaxes().stream().map(AppliedTaxApiResponsePayload::new).collect(Collectors.toList());
        this.discount = documentLine.getDiscount() == null ? null : new DiscountApiResponsePayload(documentLine.getDiscount());
    }

    public DocumentLine toEntity(Document document) {
        return DocumentLine.builder()
                .id(id)
                .document(document)
                .position(position)
                .quantity(quantity)
                .unitOfMeasure(unitOfMeasure)
                .itemCode(itemCode)
                .skuVariant(skuVariant)
                .itemName(itemName)
                .itemType(itemType)
                .category(category.toEntity())
                .unitPrice(unitPrice)
                .totalAmount(totalAmount)
                .netAmount(netAmount)
                .totalTaxAmount(totalTaxAmount)
                .appliedTaxes(appliedTaxes.stream().map(AppliedTaxApiResponsePayload::toEntity).collect(Collectors.toList()))
                .discount(discount == null ? null : discount.toEntity())
                .build();
    }
}

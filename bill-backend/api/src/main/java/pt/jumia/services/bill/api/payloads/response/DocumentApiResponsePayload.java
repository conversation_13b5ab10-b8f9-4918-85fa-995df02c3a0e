package pt.jumia.services.bill.api.payloads.response;

import com.neovisionaries.i18n.CountryCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentPossibleOperations;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

@Data
@NoArgsConstructor
public class DocumentApiResponsePayload {

    //
    // Internal fields
    //

    private UUID id;
    private DocumentStatus status;
    private String judgeSid;

    //
    // General information
    //

    private CountryCode country;
    private String shop;
    private DocumentType type;
    private String sid;
    private DocumentFlow flow;
    private String generatedBy;
    private String referenceNumber;
    private LocalDateTime issuedDate;
    private String currency;
    private ReceiverApiResponsePayload receiver;
    private IssuerApiResponsePayload issuer;
    private DocumentIdApiResponsePayload originalDocument;
    private OtherSplitDocumentApiResponsePayload otherSplitDocument;
    private boolean conflictStatusWithOtherSplitDocument;
    private String notes;

    //
    // Summary
    //

    private int lineCount;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;

    //
    // Audit information
    //

    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;
    private DocumentPossibleOperationsApiResponsePayload operations;

    private LocalDateTime receivedDate;
    private LocalDateTime reviewedAt;
    private String reviewedBy;
    private Boolean reviewed;

    public DocumentApiResponsePayload(Document document) {
        this.id = document.getId();
        this.status = document.getStatus();
        this.judgeSid = document.getJudgeSid();
        this.country = document.getCountry();
        this.shop = document.getShop();
        this.type = document.getType();
        this.sid = document.getSid();
        this.flow = document.getFlow();
        this.generatedBy = document.getGeneratedBy();
        this.referenceNumber = document.getReferenceNumber();
        this.issuedDate = document.getIssuedDate();
        this.currency = document.getCurrency().getCurrencyCode();
        this.receiver = new ReceiverApiResponsePayload(document.getReceiver());
        this.issuer = new IssuerApiResponsePayload(document.getIssuer());
        this.originalDocument = document.getOriginalDocument() != null
                ? new DocumentIdApiResponsePayload(document.getOriginalDocument())
                : null;
        this.notes = document.getNotes();
        this.lineCount = document.getLineCount();
        this.totalAmount = document.getTotalAmount();
        this.netAmount = document.getNetAmount();
        this.taxAmount = document.getTaxAmount();
        this.discountAmount = document.getDiscountAmount();
        this.createdAt = document.getCreatedAt();
        this.createdBy = document.getCreatedBy();
        this.updatedAt = document.getUpdatedAt();
        this.updatedBy = document.getUpdatedBy();
        this.operations = new DocumentPossibleOperationsApiResponsePayload(document.getDocumentPossibleOperations());
        this.otherSplitDocument = document.getOtherSplitDocument() == null ? null :
                new OtherSplitDocumentApiResponsePayload(document.getOtherSplitDocument());
        this.conflictStatusWithOtherSplitDocument = document.getOtherSplitDocument() != null &&
                !document.getStatus().equals(document.getOtherSplitDocument().getStatus());
        this.receivedDate = document.getReceivedDate();
        this.reviewed = document.getReviewed();
        this.reviewedAt = document.getReviewedAt();
        this.reviewedBy = document.getReviewedBy();
    }

    public Document toEntity() {

        return Document
                .builder()
                .id(this.id)
                .judgeSid(this.judgeSid)
                .status(this.status)
                .country(this.country)
                .shop(this.shop)
                .type(this.type)
                .sid(this.sid)
                .flow(this.flow)
                .generatedBy(this.generatedBy)
                .referenceNumber(this.referenceNumber)
                .issuedDate(this.issuedDate)
                .receivedDate(this.receivedDate)
                .currency(Currency.getInstance(this.currency))
                .receiver(this.receiver.toEntity())
                .issuer(this.issuer.toEntity())
                .originalDocument(originalDocument != null ? originalDocument.toEntity() : null)
                .otherSplitDocument(otherSplitDocument != null ? otherSplitDocument.toEntity() :
                        null)
                .notes(this.notes)
                .lineCount(this.lineCount)
                .totalAmount(this.totalAmount)
                .netAmount(this.netAmount)
                .taxAmount(this.taxAmount)
                .discountAmount(this.discountAmount)
                .documentPossibleOperations(
                        DocumentPossibleOperations.builder()
                                .canApprove(this.operations.isCanApprove())
                                .canCancel(this.operations.isCanCancel())
                                .canAcknowledge(this.operations.isCanAcknowledge())
                                .canDeclineDocumentRejection(this.operations.isCanDeclineDocumentRejection())
                                .build()
                )
                .reviewed(this.reviewed)
                .reviewedAt(this.reviewedAt)
                .reviewedBy(this.reviewedBy)
                .build();
    }
}

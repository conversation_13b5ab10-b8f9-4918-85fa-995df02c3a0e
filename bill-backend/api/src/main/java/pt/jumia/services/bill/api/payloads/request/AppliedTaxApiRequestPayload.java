package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.api.validations.annotations.ValidPercentRate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.TaxCategory;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class AppliedTaxApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = TaxCategory.class)
    private String taxCategory;
    @ValidPercentRate
    private BigDecimal taxRate;
    private BigDecimal taxFixedAmount;
    @NotNull
    private BigDecimal taxAmount;

    public AppliedTaxApiRequestPayload(DocumentLine.AppliedTax entity) {
        this.taxCategory = entity.getTaxCategory().name();
        this.taxRate = entity.getTaxRate();
        this.taxFixedAmount = entity.getTaxFixedAmount();
        this.taxAmount = entity.getTaxAmount();
    }

    public DocumentLine.AppliedTax toEntity() {
        return DocumentLine.AppliedTax.builder()
                .taxCategory(TaxCategory.valueOf(this.taxCategory))
                .taxRate(this.taxRate)
                .taxFixedAmount(this.taxFixedAmount)
                .taxAmount(this.taxAmount)
                .build();
    }
}

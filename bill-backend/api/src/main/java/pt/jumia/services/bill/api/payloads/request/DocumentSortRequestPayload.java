package pt.jumia.services.bill.api.payloads.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.OrderDirection;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;

@Data
@NoArgsConstructor
public class DocumentSortRequestPayload {
    @ValidEnumValue(enumClass = Document.SortingFields.class)
    private String orderField = Document.SortingFields.CREATED_AT.name();
    @ValidEnumValue(enumClass = OrderDirection.class)
    private String orderDirection = OrderDirection.DESC.name();

    public DocumentSortRequestPayload(DocumentSortFilters documentSortFilters) {
        this.orderField = documentSortFilters.getField().name();
        this.orderDirection = documentSortFilters.getDirection().name();
    }

    public DocumentSortFilters toEntity() {
        return DocumentSortFilters.builder()
                .field(Document.SortingFields.valueOf(orderField))
                .direction(OrderDirection.valueOf(orderDirection))
                .build();
    }
}

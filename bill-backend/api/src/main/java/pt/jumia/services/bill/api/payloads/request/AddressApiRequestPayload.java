package pt.jumia.services.bill.api.payloads.request;

import com.neovisionaries.i18n.CountryCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Address;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class AddressApiRequestPayload {

    @ValidEnumValue(enumClass = CountryCode.class, required = true)
    private String country;
    private String region;
    private String city;
    private String street;
    private String buildingNumber;
    private String floor;
    private String postalCode;
    private String additionalInformation;

    public AddressApiRequestPayload(Address entity) {
        this.country = entity.getCountry() != null ? entity.getCountry().getAlpha2() : null;
        this.region = entity.getRegion();
        this.city = entity.getCity();
        this.street = entity.getStreet();
        this.buildingNumber = entity.getBuildingNumber();
        this.floor = entity.getFloor();
        this.postalCode = entity.getPostalCode();
        this.additionalInformation = entity.getAdditionalInformation();
    }

    public Address toEntity() {
        return Address.builder()
                .country(this.country == null ? null : CountryCode.valueOf(this.country))
                .region(this.region)
                .city(this.city)
                .street(this.street)
                .buildingNumber(this.buildingNumber)
                .floor(this.floor)
                .postalCode(this.postalCode)
                .additionalInformation(this.additionalInformation)
                .build();
    }
}

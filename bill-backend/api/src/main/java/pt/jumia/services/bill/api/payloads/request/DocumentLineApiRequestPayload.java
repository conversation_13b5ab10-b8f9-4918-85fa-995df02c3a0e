package pt.jumia.services.bill.api.payloads.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.ItemType;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DocumentLineApiRequestPayload {

    //
    // General information
    //
    @NotNull
    private int position;
    @NotNull
    private BigDecimal quantity;
    @ValidEnumValue(required = true, enumClass = UnitOfMeasure.class)
    private String unitOfMeasure;

    //
    // Product information
    //

    @NotBlank
    private String itemCode;
    @JsonProperty("SKU")
    private String skuVariant;
    @NotBlank
    private String itemName;
    @ValidEnumValue(required = true, enumClass = ItemType.class)
    private String itemType;

    private CategoryApiRequestPayload category;

    //
    // Price information
    //

    @NotNull
    private BigDecimal unitPrice;
    @NotNull
    private BigDecimal totalAmount;
    @NotNull
    private BigDecimal netAmount;
    @NotNull
    private BigDecimal totalTaxAmount;
    @NotEmpty
    @Valid
    private List<AppliedTaxApiRequestPayload> appliedTaxes;

    @Valid
    private DiscountApiRequestPayload discount;

    private String unitOfPackage;

    public DocumentLineApiRequestPayload(DocumentLine entity) {
        this.position = entity.getPosition();
        this.quantity = entity.getQuantity();
        this.unitOfMeasure = entity.getUnitOfMeasure().name();
        this.itemCode = entity.getItemCode();
        this.skuVariant = entity.getSkuVariant();
        this.itemName = entity.getItemName();
        this.itemType = entity.getItemType().name();
        this.category = entity.getCategory() != null ? new CategoryApiRequestPayload(entity.getCategory()) : null;
        this.unitPrice = entity.getUnitPrice();
        this.totalAmount = entity.getTotalAmount();
        this.netAmount = entity.getNetAmount();
        this.totalTaxAmount = entity.getTotalTaxAmount();
        this.appliedTaxes = entity.getAppliedTaxes() != null ?
                entity.getAppliedTaxes().stream().map(AppliedTaxApiRequestPayload::new).collect(Collectors.toList())
                : null;
        this.discount = entity.getDiscount() == null ? null : new DiscountApiRequestPayload(entity.getDiscount());
        this.unitOfPackage = entity.getUnitOfPackage();
    }

    public DocumentLine toEntity(Document document) {
        return DocumentLine
                .builder()
                .document(document)
                .position(this.position)
                .quantity(this.quantity)
                .unitOfMeasure(UnitOfMeasure.valueOf(this.unitOfMeasure))
                .itemCode(this.itemCode)
                .skuVariant(this.skuVariant)
                .itemName(this.itemName)
                .itemType(ItemType.valueOf(this.itemType))
                .category(this.category == null ? null : this.category.toEntity())
                .unitPrice(this.unitPrice)
                .totalAmount(this.totalAmount)
                .netAmount(this.netAmount)
                .totalTaxAmount(this.totalTaxAmount)
                .appliedTaxes(this.appliedTaxes.stream().map(AppliedTaxApiRequestPayload::toEntity).collect(Collectors.toList()))
                .discount(this.discount == null ? null : this.discount.toEntity())
                .unitOfPackage(this.unitOfPackage)
                .generateId()
                .build();
    }
}

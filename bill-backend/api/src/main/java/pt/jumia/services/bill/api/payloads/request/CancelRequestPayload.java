package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.CancelRequest;

import javax.validation.constraints.NotNull;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class CancelRequestPayload {
    @NotNull
    private String cancellationReason;

    public CancelRequest toEntity(UUID documentId) {
        return CancelRequest.builder()
                .documentId(documentId)
                .cancellationReason(this.cancellationReason)
                .build();
    }
}

package pt.jumia.services.bill.api.payloads.response.error;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.usecases.documents.validators.ValidationError;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodedErrorResponsePayload {

    private int code;
    private String name;
    private String details;
    private List<CodedErrorResponsePayload> errors;

    private CodedErrorResponsePayload(ErrorCode errorCode, String message, List<CodedErrorResponsePayload> errors) {
        this.code = errorCode.getCode();
        this.name = errorCode.name();
        this.details = message;
        this.errors = errors;
    }

    public CodedErrorResponsePayload(ValidationError error) {
        code = error.getCode().getCode();
        name = error.getCode().name();
        details = error.getErrorMessage();
    }

    public static CodedErrorResponsePayload forSingleError(ErrorCode errorCode, String message) {
        return new CodedErrorResponsePayload(errorCode, message, null);
    }

    public static CodedErrorResponsePayload forErrorWithCauses(ErrorCode errorCode, String message, List<CodedErrorResponsePayload> errors) {
        return new CodedErrorResponsePayload(errorCode, message, errors);
    }

    public static CodedErrorResponsePayload forValidationError(ValidationError error) {
        return new CodedErrorResponsePayload(error.getCode(), error.getErrorMessage(), null);
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Notification;

import java.util.List;

@Data
@NoArgsConstructor
public class NotificationResponsePayload {

    List<Notification> emails;

    public NotificationResponsePayload(List<Notification> notifications) {
        this.emails = notifications;
    }
}

package pt.jumia.services.bill.api.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.ResourceRegionHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;
import pt.jumia.services.bill.api.filters.AuthFilter;
import pt.jumia.services.bill.api.filters.CorsFilter;
import pt.jumia.services.bill.api.filters.RequestCacheFilter;
import pt.jumia.services.bill.domain.AccessController;
import pt.jumia.services.bill.domain.BigDecimalSerializer;
import pt.jumia.services.bill.domain.properties.AclProperties;
import pt.jumia.services.bill.domain.properties.ApiProperties;
import pt.jumia.services.bill.domain.utils.DateUtils;

import java.util.List;

/**
 * Configuration file loaded by Spring to configure filter
 */
@Configuration
@RequiredArgsConstructor
public class WebMvcConfiguration extends WebMvcConfigurationSupport {

    private final AccessController accessController;
    private final ApiProperties apiProperties;
    private final AclProperties aclProperties;

    @Override
    public void addResourceHandlers(final ResourceHandlerRegistry registry) {
        // Add mapping if swagger is enabled.
        if (apiProperties.isSwaggerEnabled()) {
            // Make Swagger meta-data available via <baseURL>/v2/api-docs/
            registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
            // Make Swagger UI available via <baseURL>/swagger-ui.html
            registry.addResourceHandler("/swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        }
    }

    /**
     * Configuration of the requestCache filter to allow to read the request body content multiple times.
     */
    @Bean
    public FilterRegistrationBean<RequestCacheFilter> registerRequestCacheFilter(RequestCacheFilter requestCacheFilter) {
        FilterRegistrationBean<RequestCacheFilter> filterRegistrationBean = new FilterRegistrationBean<>();

        // Register Spring internal RequestCachefilter
        filterRegistrationBean.setFilter(requestCacheFilter);

        // Apply the filter to all created servlets in the tomcat server
        filterRegistrationBean.addUrlPatterns("/api/*");
        return filterRegistrationBean;
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        registrar.setUseIsoFormat(true);
        registrar.registerFormatters(registry);
    }

    @Override
    protected void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        // Add some of the default converters
        converters.add(new ByteArrayHttpMessageConverter());
        converters.add(new StringHttpMessageConverter());
        converters.add(new ResourceHttpMessageConverter());
        converters.add(new ResourceRegionHttpMessageConverter());

        // But override the JSON converter to serialize dates in a sensible format
        Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();
        builder.serializationInclusion(JsonInclude.Include.NON_NULL);
        builder.serializers(
                new LocalDateSerializer(DateUtils.DATE_FORMATTER),
                new LocalDateTimeSerializer(DateUtils.DATE_TIME_FORMATTER),
                new BigDecimalSerializer()
        );

        converters.add(new MappingJackson2HttpMessageConverter(builder.build()));
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jsonCustomizer() {
        return builder -> builder.serializationInclusion(JsonInclude.Include.NON_NULL)
                .failOnUnknownProperties(false)
                .serializers(
                        new LocalDateSerializer(DateUtils.DATE_FORMATTER),
                        new LocalDateTimeSerializer(DateUtils.DATE_TIME_FORMATTER),
                        new BigDecimalSerializer()
                );
    }

    /**
     * Configuring CORS filters according to the allowed domains defined in the application.properties file
     */
    @Bean
    public FilterRegistrationBean registerCORSFilter() {
        FilterRegistrationBean<CorsFilter> filterRegistrationBean = new FilterRegistrationBean<>();

        // Register Spring internal CORS filter
        filterRegistrationBean.setFilter(corsFilter());

        // Apply the filter to all created servlets in the tomcat server
        filterRegistrationBean.addUrlPatterns("/api/*");
        filterRegistrationBean.addUrlPatterns("/auth/*");
        return filterRegistrationBean;
    }

    @Bean
    public FilterRegistrationBean registerAuthFilter() {
        FilterRegistrationBean<AuthFilter> filterRegistrationBean = new FilterRegistrationBean<>();

        // Register our custom AuthFilter
        filterRegistrationBean.setFilter(authFilter());
        filterRegistrationBean.setMatchAfter(true);

        // Apply the filter to all created servlets in the tomcat server
        filterRegistrationBean.addUrlPatterns("/api/*");
        return filterRegistrationBean;
    }

    @Bean
    public AuthFilter authFilter() {
        return new AuthFilter(accessController, aclProperties);
    }

    @Bean
    public CorsFilter corsFilter() {
        return new CorsFilter(apiProperties);
    }
}

package pt.jumia.services.bill.api.export;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.api.export.csv.CsvUtils;
import pt.jumia.services.bill.api.export.entities.CSVExportDocument;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.OrderDirection;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.exceptions.CreateCsvException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.settings.OverallSettings;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ExportDocument {

    private final DocumentAggregateRepository documentAggregateRepository;

    private final OverallSettings overallSettings;

    public void execute(DocumentFilter documentFilter, HttpServletResponse response) throws Exception {
        int fileRowsLimit = overallSettings.getExportationSettings().getExportationDocumentRowsLimit();
        int exceptionStackTraceLinesLimit = overallSettings.getExportationSettings()
                .getExportationDocumentExceptionTraceLinesLimit();

        try {
            DocumentSortFilters sort = DocumentSortFilters.builder()
                    .field(Document.SortingFields.CREATED_AT)
                    .direction(OrderDirection.DESC).build();
            PageFilters pageFilters = PageFilters.builder().page(1).size(fileRowsLimit).build();
            DocumentFilter documentFilterBuild = documentFilter.toBuilder().include(List.of(
                    Document.Details.ISSUER,
                    Document.Details.RECEIVER,
                    Document.Details.TAX_AUTHORITIES_DETAILS
            )).build();

            List<DocumentAggregate> documentAggregateList = documentAggregateRepository.findAll(
                    documentFilterBuild, sort, pageFilters);

            List<CSVExportDocument> exportDocumentList = documentAggregateList.stream()
                    .map(document -> CSVExportDocument.fromEntity(document, exceptionStackTraceLinesLimit))
                    .collect(Collectors.toList());

            CsvUtils.printCsv(String.format("%s-%s", CSVExportDocument.class.getSimpleName(), UUID.randomUUID()),
                    exportDocumentList, response, CSVExportDocument.class);
        } catch (Exception e) {
            throw new CreateCsvException(e.getMessage());
        }
    }
}

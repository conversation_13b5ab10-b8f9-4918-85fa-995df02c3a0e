package pt.jumia.services.bill.api.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.ContentCachingRequestWrapper;
import pt.jumia.services.bill.api.export.ExportDocument;
import pt.jumia.services.bill.api.payloads.request.CancelRequestPayload;
import pt.jumia.services.bill.api.payloads.request.DocumentApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.DocumentFiltersRequestPayload;
import pt.jumia.services.bill.api.payloads.request.DocumentSortRequestPayload;
import pt.jumia.services.bill.api.payloads.request.PageFiltersRequestPayload;
import pt.jumia.services.bill.api.payloads.request.StatementApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.TaxAuthoritiesDetailsApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.DocumentAggregateApiResponsePayload;
import pt.jumia.services.bill.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.bill.api.payloads.response.PageResponsePayload;
import pt.jumia.services.bill.api.utils.PaginationService;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.dtos.TaxAuthoritiesDetailsUpdateDto;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.documenettransformation.UpsertDocumentTransformationUseCase;
import pt.jumia.services.bill.domain.usecases.documentapilogs.UpsertDocumentApiLogUseCase;
import pt.jumia.services.bill.domain.usecases.documents.AckDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.documents.CancelDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.documents.CreateDocumentAggregateUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DeclineDocumentRejectionUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentAllowedOperationsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentRetryUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DownloadDocumentFromTaxAuthoritiesUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DownloadDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.UpdateTaxAuthoritiesDetailsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentResendUseCase;
import pt.jumia.services.bill.domain.usecases.migration.MigrateDecimalFieldsUseCase;
import pt.jumia.services.bill.domain.utils.JsonUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = "/api/documents")
@Slf4j
@RequiredArgsConstructor
public class DocumentController {

    private final JsonUtils jsonUtils;
    private final UserPermissionValidationUseCase userPermissionValidationUseCase;
    private final ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    private final CreateDocumentAggregateUseCase createDocumentAggregateUseCase;
    private final UpsertDocumentApiLogUseCase upsertDocumentApiLogUseCase;
    private final DownloadDocumentUseCase downloadDocumentUseCase;
    private final DownloadDocumentFromTaxAuthoritiesUseCase downloadDocumentFromTaxAuthoritiesUseCase;
    private final UpdateTaxAuthoritiesDetailsUseCase updateTaxAuthoritiesDetailsUseCase;
    private final ReadDocumentsUseCase readDocumentsUseCase;
    private final AckDocumentUseCase ackDocumentUseCase;
    private final PaginationService paginationService;
    private final ExportDocument exportDocument;
    private final CancelDocumentUseCase cancelDocumentUseCase;
    private final DeclineDocumentRejectionUseCase declineDocumentRejectionUseCase;
    private final DocumentAllowedOperationsUseCase documentAllowedOperationsUseCase;
    private final DocumentRetryUseCase documentRetryUseCase;
    private final UpsertDocumentTransformationUseCase upsertDocumentTransformationUseCase;
    private final DocumentResendUseCase documentResendUseCase;
    private final MigrateDecimalFieldsUseCase migrateDecimalFieldsUseCase;

    @GetMapping("{documentId}")
    public DocumentAggregateApiResponsePayload readById(@PathVariable(value = "documentId") UUID documentId) {
        log.debug("'{}' getting document details by ID '{}'",
                RequestContext.getUsername(), documentId);

        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(documentId);
        Document document = documentAggregate.getDocument();
        checkCanViewByCountryOrThrow(document.getCountry());
        documentAggregate.setDocument(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document));
        return new DocumentAggregateApiResponsePayload(documentAggregate);
    }

    @GetMapping("/sid/{documentSid}")
    public DocumentAggregateApiResponsePayload readBySid(@PathVariable(value = "documentSid") String documentSid) {
        log.debug("'{}' getting document details by SID '{}'",
                RequestContext.getUsername(), documentSid);

        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.executeBySidAndSuccessStatus(documentSid);
        Document document = documentAggregate.getDocument();
        checkCanViewByCountryOrThrow(document.getCountry());
        documentAggregate.setDocument(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document));
        return new DocumentAggregateApiResponsePayload(documentAggregate);
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public DocumentAggregateApiResponsePayload create(
            @RequestBody @Valid DocumentApiRequestPayload requestPayload,
            ContentCachingRequestWrapper request) {
        log.debug("'{}' creating a new document: {}",
                RequestContext.getUsername(), jsonUtils.toJsonOrNull(requestPayload));

        checkCanCreateAndResubmitDocumentsPermissionByCountryOrThrow(CountryCode.valueOf(requestPayload.getCountry()));

        DocumentApiLog documentApiLog = upsertDocumentApiLogUseCase.execute(DocumentApiLog.builder()
                .documentSid(requestPayload.getSid())
                .originalRequest(new String(request.getContentAsByteArray(), StandardCharsets.UTF_8))
                .issuedDate(requestPayload.getIssuedDate())
                .build());

        DocumentAggregate aggregate = createDocumentAggregateUseCase.execute(documentApiLog, requestPayload.toEntity());

        aggregate.setDocument(
                documentAllowedOperationsUseCase.appendDocumentPossibleOperations(aggregate.getDocument())
        );
        return new DocumentAggregateApiResponsePayload(aggregate);
    }

    @PostMapping("/create/statement")
    @ResponseStatus(HttpStatus.CREATED)
    public List<DocumentAggregateApiResponsePayload> createStatement(
            @RequestBody @Valid StatementApiRequestPayload requestPayload,
            ContentCachingRequestWrapper request) {
        log.debug("'{}' creating a new document as statement: {}",
                RequestContext.getUsername(), jsonUtils.toJsonOrNull(requestPayload));

        checkCanCreateAndResubmitDocumentsPermissionByCountryOrThrow(CountryCode.valueOf(requestPayload.getCountry()));

        requestPayload.setFlow(DocumentFlow.RETAIL.name());
        DocumentApiLog documentApiLog = upsertDocumentApiLogUseCase.execute(DocumentApiLog.builder()
                .documentSid(requestPayload.getSid())
                .originalRequest(new String(request.getContentAsByteArray(), StandardCharsets.UTF_8))
                .issuedDate(requestPayload.getIssuedDate())
                .build());
        List<DocumentAggregate> aggregates = createDocumentAggregateUseCase
                .executeAsStatement(documentApiLog, requestPayload.toEntity());
        aggregates.forEach(aggregate -> aggregate.setDocument(documentAllowedOperationsUseCase
                .appendDocumentPossibleOperations(aggregate.getDocument()))
        );

        return aggregates.stream().map(DocumentAggregateApiResponsePayload::new).collect(Collectors.toList());
    }

    @GetMapping("/sid/{document-sid}/pdf")
    public ResponseEntity<InputStreamResource> fetchAsPdf(
            @PathVariable("document-sid") @NotEmpty String documentSid) throws UserForbiddenException {

        log.debug("'{}' getting document from Judge by SID {}", RequestContext.getUsername(), documentSid);

        Document document = readDocumentsUseCase.readDocumentBySidAndSuccessStatus(documentSid);
        checkCanViewByCountryOrThrow(document.getCountry());

        DocumentFile documentFromJudge = downloadDocumentUseCase.execute(document);

        return convertToInputStream(documentFromJudge);
    }

    @GetMapping("/{documentId}/pdf")
    public ResponseEntity<InputStreamResource> fetchAsPdf(
            @PathVariable("documentId") @NotEmpty UUID documentId) throws UserForbiddenException,
            JsonProcessingException{

        log.debug("'{}' getting document from Judge by ID {}", RequestContext.getUsername(), documentId);

        Document document = readDocumentsUseCase.readDocumentById(documentId);

        checkCanViewByCountryOrThrow(document.getCountry());

        DocumentFile documentFromJudge = downloadDocumentUseCase.execute(document);

        return convertToInputStream(documentFromJudge);
    }

    @GetMapping("/{documentId}/tax-authorities-pdf")
    public ResponseEntity<InputStreamResource> fetchTaxAuthoritiesPdf(
            @PathVariable("documentId") @NotEmpty UUID documentId) throws UserForbiddenException,
            JsonProcessingException{

        log.debug("'{}' getting document from Tax Authorities by ID {}", RequestContext.getUsername(), documentId);

        Document document = readDocumentsUseCase.readDocumentById(documentId);
        checkCanViewByCountryOrThrow(document.getCountry());

        DocumentFile documentFromTaxAuthorities = downloadDocumentFromTaxAuthoritiesUseCase.execute(document
                .getId().toString());

        return convertToInputStream(documentFromTaxAuthorities);
    }

    @GetMapping("/export/csv")
    public void fetchAsCsv(
            @Valid DocumentFiltersRequestPayload documentFiltersRequestPayload,
            HttpServletResponse response) throws Exception {

        log.debug("'{}' getting documents as CSV file with filters {}.", RequestContext.getUsername(),
                jsonUtils.toJsonOrNull(documentFiltersRequestPayload)
        );


        DocumentFilter filters = mergeFiltersWithCountriesWithPermission(documentFiltersRequestPayload.toEntity());
        exportDocument.execute(filters, response);
    }

    @PostMapping("{documentId}/tax-details")
    public void updateTaxDetails(
            @PathVariable(value = "documentId") UUID documentId,
            @RequestBody @Valid TaxAuthoritiesDetailsApiRequestPayload taxAuthoritiesDetailsApiRequestPayload)
            throws JsonProcessingException{
        log.debug("'{}' updating tax authorities details for document ID '{}': {}",
                RequestContext.getUsername(), documentId, jsonUtils.toJsonOrNull(taxAuthoritiesDetailsApiRequestPayload));

        checkCanManageByCountryOrThrow(readDocumentsUseCase.readDocumentById(documentId).getCountry());
        TaxAuthoritiesDetailsUpdateDto taxAuthoritiesDetailsUpdateDto = taxAuthoritiesDetailsApiRequestPayload.toEntityUpdateDto(documentId);
        upsertDocumentTransformationInTaxAuthoritiesPayload(documentId,
                taxAuthoritiesDetailsUpdateDto.getTaxAuthoritiesDetails().getDocumentTransformations());
        updateTaxAuthoritiesDetailsUseCase.execute(taxAuthoritiesDetailsUpdateDto);
    }

    @PostMapping("{documentId}/acknowledge")
    public DocumentApiResponsePayload acknowledgeDocument(@PathVariable(value = "documentId") UUID documentId) {
        log.debug("'{}' acknowledged document by ID: {}", RequestContext.getUsername(), documentId);

        Document document = readDocumentsUseCase.readDocumentById(documentId);
        checkCanAcknowledgeOrThrow(document.getCountry());
        return documentWithPossibleOperations(ackDocumentUseCase.execute(document));
    }

    @PutMapping("/{documentId}/retry")
    public DocumentApiResponsePayload retry(
            @PathVariable("documentId") @NotEmpty UUID documentId) {

        Document document = readDocumentsUseCase.readDocumentById(documentId);
        checkCanRetryDocumentsByCountryOrThrow(document.getCountry());
        documentRetryUseCase.execute(document);

        return documentWithPossibleOperations(document);
    }

    @GetMapping
    public PageResponsePayload<DocumentApiResponsePayload> fetch(
            HttpServletRequest request,
            @Valid DocumentFiltersRequestPayload documentFiltersRequestPayload,
            @Valid DocumentSortRequestPayload documentSortRequestPayload,
            @Valid PageFiltersRequestPayload pageFiltersRequestPayload
    ) throws UserForbiddenException, EntityNotFoundException {
        log.debug("'{}' requesting List of Documents paginated with filters {}",
                RequestContext.getUsername(),
                jsonUtils.toJsonOrNull(documentFiltersRequestPayload));

        DocumentFilter filters = mergeFiltersWithCountriesWithPermission(documentFiltersRequestPayload.toEntity());
        filters = filters.toBuilder()
                .flow(DocumentFlow.RETAIL)
                .build();
        PageFilters pageFilters = pageFiltersRequestPayload.toEntity();
        DocumentSortFilters sortFilters = documentSortRequestPayload.toEntity();
        List<Document> documents = readDocumentsUseCase.execute(filters, sortFilters, pageFilters);
        List<DocumentApiResponsePayload> results = documents.stream()
                .map(this::documentWithPossibleOperations)
                .collect(Collectors.toList());

        return paginationService.buildPageResponsePayload(request, pageFilters, results);
    }

    @GetMapping("/received")
    public PageResponsePayload<DocumentAggregateApiResponsePayload> fetchReceivedDocument(
            HttpServletRequest request,
            @Valid DocumentFiltersRequestPayload documentFiltersRequestPayload,
            @Valid DocumentSortRequestPayload documentSortRequestPayload,
            @Valid PageFiltersRequestPayload pageFiltersRequestPayload
    ) throws UserForbiddenException, EntityNotFoundException {
        log.debug("'{}' requesting List of Documents paginated with filters {}",
                RequestContext.getUsername(),
                jsonUtils.toJsonOrNull(documentFiltersRequestPayload));

        DocumentFilter filters = mergeFiltersWithCountriesWithPermission(documentFiltersRequestPayload.toEntity());
        filters = filters.toBuilder()
                .flow(DocumentFlow.RECEIVED)
                .build();
        PageFilters pageFilters = pageFiltersRequestPayload.toEntity();
        DocumentSortFilters sortFilters = documentSortRequestPayload.toEntity();
        List<DocumentAggregate> documents = readDocumentAggregateUseCase.execute(filters, sortFilters, pageFilters);
        List<DocumentAggregateApiResponsePayload> results = documents.stream()
                .map(this::documentWithPossibleOperations)
                .collect(Collectors.toList());

        return paginationService.buildPageResponsePayload(request, pageFilters, results);
    }

    @GetMapping("/count")
    public Long count(
            @Valid DocumentFiltersRequestPayload documentFiltersRequestPayload
    ) throws UserForbiddenException, EntityNotFoundException {
        DocumentFilter filters = mergeFiltersWithCountriesWithPermission(documentFiltersRequestPayload.toEntity());
        return readDocumentsUseCase.executeCountAll(filters);
    }

    @PostMapping("{documentId}/cancel")
    public DocumentApiResponsePayload cancelDocument(@PathVariable(value = "documentId") UUID documentId,
                                                     @Valid @RequestBody CancelRequestPayload cancelRequestPayload) {
        log.debug("'{}' cancel document by ID: {}", RequestContext.getUsername(), documentId);

        Document document = readDocumentsUseCase.readDocumentById(documentId);
        checkCanCancelOrThrow(document.getCountry());

        return documentWithPossibleOperations(cancelDocumentUseCase
                .execute(document, cancelRequestPayload.toEntity(documentId)));
    }

    @PostMapping("{documentId}/decline-rejection")
    public DocumentApiResponsePayload declineDocumentRejection(@PathVariable(value = "documentId") UUID documentId) {
        log.debug("'{}' decline document rejection by ID: {}", RequestContext.getUsername(), documentId);

        Document document = readDocumentsUseCase.readDocumentById(documentId);
        checkCanDeclineDocumentRejectionOrThrow(document.getCountry());

        return documentWithPossibleOperations(declineDocumentRejectionUseCase.execute(document));
    }

    @PutMapping("/{documentId}/resend")
    public void resendToTaxi(
            @PathVariable("documentId") @NotEmpty UUID documentId) {
        log.info("'{}' resending a document directly to taxi: {}",
                RequestContext.getUsername(), jsonUtils.toJsonOrNull(documentId));

        Document document = readDocumentsUseCase.readDocumentById(documentId);
        checkCanRetryDocumentsByCountryOrThrow(document.getCountry());
        documentResendUseCase.execute(document);
    }

    @PostMapping("/migrate-new-decimal-fields")
    public ResponseEntity<Map<String, Integer>> migrateDecimalFields(
            @RequestParam(value = "startId", required = false, defaultValue = "0") Long startId,
            @RequestParam(value = "endId", required = false, defaultValue = "1000") Long endId) {
        log.debug("'{}' migrating decimal fields for documents from ID {} to {}",
                RequestContext.getUsername(), startId, endId);

        Map<String, Integer> result = migrateDecimalFieldsUseCase.migrateDocuments(startId, endId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/migrate-new-decimal-fields/status")
    public ResponseEntity<Map<String, Object>> getMigrationStatus() {
        log.debug("'{}' getting decimal fields migration status", RequestContext.getUsername());

        Map<String, Object> status = migrateDecimalFieldsUseCase.getMigrationStatus();
        return ResponseEntity.ok(status);
    }

    private DocumentApiResponsePayload documentWithPossibleOperations(Document document) {
        return new DocumentApiResponsePayload(
                documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document)
        );
    }

    private DocumentAggregateApiResponsePayload documentWithPossibleOperations(DocumentAggregate documentAggregate) {
        return new DocumentAggregateApiResponsePayload(
                documentAggregate.toBuilder().document(
                        documentAllowedOperationsUseCase.appendDocumentPossibleOperations(documentAggregate.getDocument())
                ).build()
        );
    }

    private void checkCanDeclineDocumentRejectionOrThrow(CountryCode countryCode) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanDeclineDocumentRejectionByCountryCodeOrThrow(
                RequestContext.getUser(),
                countryCode);
    }

    private void checkCanCancelOrThrow(CountryCode countryCode) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanCancelDocumentByCountryCodeOrThrow(RequestContext.getUser(), countryCode);
    }

    private void checkCanAcknowledgeOrThrow(CountryCode countryCode) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanAcknowledgeDocumentByCountryCodeOrThrow(RequestContext.getUser(), countryCode);
    }

    private void checkCanCreateAndResubmitDocumentsPermissionByCountryOrThrow(CountryCode countryCode)
            throws UserForbiddenException {
        /*
          We check for both Manage Documents and Resubmit documents for resubmitting documents
          This permission shall only be available for the NAV BI user since it is the only user
          allowed to add documents through this flow
         */
        userPermissionValidationUseCase.checkCanManageDocumentsByCountryCodeOrThrow(RequestContext.getUser(), countryCode);
        userPermissionValidationUseCase.checkCanResubmitDocumentByCountryOrThrow(RequestContext.getUser(), countryCode);
    }

    private void checkCanManageByCountryOrThrow(CountryCode countryCode) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanManageDocumentsByCountryCodeOrThrow(RequestContext.getUser(), countryCode);
    }

    private void checkCanViewByCountryOrThrow(CountryCode country) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(), country);
    }

    private void checkCanRetryDocumentsByCountryOrThrow(CountryCode country) throws UserForbiddenException {
        userPermissionValidationUseCase.checkCanRetryDocumentsByCountryCodeOrThrow(RequestContext.getUser(), country);
    }

    private List<CountryCode> getCountriesCanViewOrThrow() throws UserForbiddenException {
        return userPermissionValidationUseCase.getCountriesCanViewDocumentsOrThrow(RequestContext.getUser());
    }

    private DocumentFilter mergeFiltersWithCountriesWithPermission(DocumentFilter documentFilter)
            throws UserForbiddenException {
        if (documentFilter.getCountryCode() != null) {
            /*
             * if the country is allowed this will pass and then we can safely assume that this is allowed and return
             * the same filters, if not, it will throw UserForbiddenException which is what we need exactly
             */
            checkCanViewByCountryOrThrow(documentFilter.getCountryCode());
        } else {
            /*
             * However, in this case we will pass those allowed country codes to be used if and only
             * if we have no country filter.
             */
            List<CountryCode> countriesWithCanViewPermission = getCountriesCanViewOrThrow();
            documentFilter.setCountryCodes(countriesWithCanViewPermission);
        }
        return documentFilter;
    }

    private ResponseEntity<InputStreamResource> convertToInputStream(DocumentFile documentFile) {
        InputStreamResource inputStreamResource = new InputStreamResource(new ByteArrayInputStream(documentFile.getContent()));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentLength(documentFile.getContent().length);
        httpHeaders.setContentType(MediaType.parseMediaType(documentFile.getMediaType()));
        return new ResponseEntity<>(inputStreamResource, httpHeaders, HttpStatus.OK);
    }

    private void upsertDocumentTransformationInTaxAuthoritiesPayload(UUID documentId,
                                                                     List<DocumentTransformation> documentTransformationList) {
        if (documentTransformationList != null && !(documentTransformationList.isEmpty())) {
            upsertDocumentTransformationUseCase.execute(documentId, documentTransformationList);
        }
    }

    @PostMapping("/migrate-new-decimal-fields")
    public ResponseEntity<Map<String, Integer>> migrateDecimalFields(
            @RequestParam(value = "startId", required = false, defaultValue = "0") Long startId,
            @RequestParam(value = "endId", required = false, defaultValue = "1000") Long endId) {
        log.debug("'{}' migrating decimal fields for documents from ID {} to {}",
                RequestContext.getUsername(), startId, endId);

        Map<String, Integer> result = migrateDecimalFieldsUseCase.migrateDocuments(startId, endId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/migrate-new-decimal-fields/status")
    public ResponseEntity<Map<String, Object>> getMigrationStatus() {
        log.debug("'{}' getting decimal fields migration status", RequestContext.getUsername());

        Map<String, Object> status = migrateDecimalFieldsUseCase.getMigrationStatus();
        return ResponseEntity.ok(status);
    }
}

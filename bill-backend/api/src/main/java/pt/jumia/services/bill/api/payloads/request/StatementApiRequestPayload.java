package pt.jumia.services.bill.api.payloads.request;

import com.neovisionaries.i18n.CountryCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidCurrencyCode;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;
import pt.jumia.services.bill.domain.entities.SourceType;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class StatementApiRequestPayload {

    //
    // General information
    //

    @ValidEnumValue(required = true, enumClass = CountryCode.class)
    private String country;
    @NotBlank
    private String shop;
    @ValidEnumValue(required = true, enumClass = DocumentType.class)
    private String type;
    @NotBlank
    private String sid;
    @ValidEnumValue(required = true, enumClass = DocumentFlow.class)
    private String flow;
    @NotBlank
    private String generatedBy;
    private String referenceNumber;
    private String purchaseReferenceNumber;
    @NotNull
    private LocalDateTime issuedDate;
    @ValidCurrencyCode(required = true)
    private String currency;
    private BigDecimal issuedToLocalCurrencyExchangeRate;

    @NotNull
    @Valid
    private ReceiverApiRequestPayload receiver;
    @NotNull
    @Valid
    private IssuerApiRequestPayload issuer;
    @Valid
    private DocumentIdApiRequestPayload originalDocument;
    private String notes;
    @Valid
    private DocumentApiRequestPayload.IssuedReasonApiRequestPayload issuedReason;

    @NotEmpty
    @Valid
    private List<DocumentLineApiRequestPayload> lines;

    //
    // Summary
    //

    @NotNull
    private Integer lineCount;
    @NotNull
    private BigDecimal totalAmount;
    @NotNull
    private BigDecimal netAmount;
    @NotNull
    private BigDecimal taxAmount;
    private BigDecimal totalItemsDiscountAmount;
    private BigDecimal extraDiscountAmount;
    private BigDecimal discountAmount;

    @NotEmpty
    @Valid
    private List<TaxCategoryTotalApiRequestPayload> taxCategoryTotals;

    @Valid
    private PaymentDetailsApiRequestPayload payment;
    @Valid
    private DeliveryDetailsApiRequestPayload delivery;

    @ValidEnumValue(enumClass = SourceType.class)
    private String sourceType;
    private String postingGroup;

    public DocumentAggregate toEntity() {
        Document document = Document
                .builder()
                .country(CountryCode.valueOf(this.country))
                .shop(this.shop)
                .type(this.type != null ? DocumentType.valueOf(this.type) : null)
                .sid(this.sid)
                .flow(this.flow != null ? DocumentFlow.valueOf(this.flow) : null)
                .generatedBy(this.generatedBy)
                .referenceNumber(this.referenceNumber)
                .purchaseReferenceNumber(this.purchaseReferenceNumber)
                .issuedDate(this.issuedDate)
                .currency(Currency.getInstance(this.currency))
                .issuedToLocalCurrencyExchangeRate(this.issuedToLocalCurrencyExchangeRate)
                .receiver(this.receiver.toEntity())
                .issuer(this.issuer.toEntity())
                .originalDocument(originalDocument != null ? originalDocument.toEntity() : null)
                .notes(this.notes)
                .issuedReason(this.issuedReason != null ? issuedReason.toEntity() : null)
                .lineCount(this.lineCount)
                .totalAmount(this.totalAmount)
                .netAmount(this.netAmount)
                .taxAmount(this.taxAmount)
                .discountAmount(this.discountAmount)
                .totalItemsDiscountAmount(this.totalItemsDiscountAmount)
                .extraDiscountAmount(this.extraDiscountAmount)
                .payment(this.payment != null ? this.payment.toEntity() : null)
                .delivery(this.delivery != null ? this.delivery.toEntity() : null)
                .generateId()
                .sourceType(this.sourceType)
                .postingGroup(this.postingGroup)
                .build();

        List<DocumentLine> lines = this.lines
                .stream()
                .map(documentLine -> documentLine.toEntity(document))
                .collect(Collectors.toList());

        List<TaxCategoryTotal> taxCategoryTotals = this.taxCategoryTotals
                .stream()
                .map(taxCategoryTotal -> taxCategoryTotal.toEntity(document))
                .collect(Collectors.toList());

        return DocumentAggregate
                .builder()
                .document(document)
                .lines(lines)
                .taxCategoryTotals(taxCategoryTotals)
                .build();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class IssuedReasonApiRequestPayload {
        @ValidEnumValue(required = true, enumClass = IssuedReasonCode.class)
        private String code;
        private String notes;

        public IssuedReasonApiRequestPayload(IssuedReason entity) {
            this.code = entity.getCode() != null ? entity.getCode().name() : null;
            this.notes = entity.getNotes();
        }

        public IssuedReason toEntity() {
            return IssuedReason.builder()
                    .code(IssuedReasonCode.valueOf(code))
                    .notes(notes)
                    .generateId()
                    .build();
        }
    }
}

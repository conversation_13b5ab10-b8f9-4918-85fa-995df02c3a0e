package pt.jumia.services.bill.api.payloads.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder(toBuilder = true)
public class DocumentTransformationApiRequestPayload {


    private String oldValue;

    private String newValue;

    private String type;

    private String originalValue;

    public DocumentTransformationApiRequestPayload(DocumentTransformation documentTransformation) {
        this.type = documentTransformation.getType() != null ? documentTransformation.getType().name() : null;
        this.newValue = documentTransformation.getNewValue();
        this.originalValue = documentTransformation.getOriginalValue();

    }

    public DocumentTransformation toEntity() {
        return DocumentTransformation.builder()
                .type(type == null ? null : DocumentTransformation.EntityType.valueOf(type))
                .originalValue(originalValue)
                .newValue(newValue)
                .build();
    }

}

package pt.jumia.services.bill.api.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.bill.api.payloads.response.NotificationResponsePayload;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.notification.ReadNotificationUseCase;

@RestController
@RequestMapping(value = "/api/notifications")
@Slf4j
@RequiredArgsConstructor
public class NotificationController {

    private final UserPermissionValidationUseCase userPermissionValidationUseCase;
    private final ReadNotificationUseCase readNotificationUseCase;


    @GetMapping(value = "/purchase-portal")
    public NotificationResponsePayload fetchById() {

        userPermissionValidationUseCase.checkCanAccessOrThrow(RequestContext.getUser());

        return new NotificationResponsePayload(readNotificationUseCase.getPurchasePortal());
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.TaxCategory;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class AppliedTaxApiResponsePayload {

    private TaxCategory taxCategory;
    private BigDecimal taxRate;
    private BigDecimal taxFixedAmount;
    private BigDecimal taxAmount;

    public AppliedTaxApiResponsePayload(DocumentLine.AppliedTax document) {
        this.taxCategory = document.getTaxCategory();
        this.taxRate = document.getTaxRate();
        this.taxFixedAmount = document.getTaxFixedAmount();
        this.taxAmount = document.getTaxAmount();
    }

    public DocumentLine.AppliedTax toEntity() {
        return DocumentLine.AppliedTax.builder()
                .taxCategory(taxCategory)
                .taxRate(taxRate)
                .taxFixedAmount(taxFixedAmount)
                .taxAmount(taxAmount)
                .build();
    }
}

package pt.jumia.services.bill.api.export.entities;

import com.neovisionaries.i18n.CountryCode;
import com.opencsv.bean.CsvBindByName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.CsvBindByNameOrder;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.utils.DateUtils;

import java.math.BigDecimal;
import java.util.Currency;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@CsvBindByNameOrder({"Issuer Country", "Document Type", "Nav Document Id", "Nav Issue Date",
        "Generated By", "Receiver Type", "Receiver Id", "Receiver Name", "Receiver Country",
        "Receiver Address", "Currency", "Total Net Value", "Total Tax Value", "Discount Value",
        "Total Value", "Bill Document Id", "Document  Creation Date", "Document Submission Date",
        "Document Status", "Tax Authority Last Error Message"})
public class CSVExportDocument {

    @CsvBindByName(column = "Issuer Country")
    private CountryCode issuerCountry;

    @CsvBindByName(column = "Document Type")
    private DocumentType documentType;

    @CsvBindByName(column = "Nav Document Id")
    private String navDocumentId;

    @CsvBindByName(column = "Nav Issue Date")
    private String navIssueDate;

    @CsvBindByName(column = "Generated By")
    private String generatedBy;

    @CsvBindByName(column = "Receiver Type")
    private ReceiverType receiverType;

    @CsvBindByName(column = "Receiver Id")
    private String receiverId;

    @CsvBindByName(column = "Receiver Name")
    private String receiverName;

    @CsvBindByName(column = "Receiver Country")
    private CountryCode receiverCountry;

    @CsvBindByName(column = "Receiver Address")
    private Address receiverAddress;

    @CsvBindByName(column = "Currency")
    private Currency currency;

    @CsvBindByName(column = "Total Net Value")
    private BigDecimal totalNetValue;

    @CsvBindByName(column = "Total Tax Value")
    private BigDecimal totalTaxValue;

    @CsvBindByName(column = "Discount Value")
    private BigDecimal discountValue;

    @CsvBindByName(column = "Total Value")
    private BigDecimal totalValue;

    @CsvBindByName(column = "Bill Document Id")
    private UUID billDocumentId;

    @CsvBindByName(column = "Document  Creation Date")
    private String documentCreationDate;

    @CsvBindByName(column = "Document Submission Date")
    private String documentSubmissionDate;

    @CsvBindByName(column = "Document Status")
    private DocumentStatus documentStatus;

    @CsvBindByName(column = "Tax Authority Last Error Message")
    private String taxAuthorityLastErrorMessage;

    public static CSVExportDocument fromEntity(DocumentAggregate documentAggregate,
                                               int exceptionStackTraceLinesLimit) {
        Document document = documentAggregate.getDocument();
        TaxAuthoritiesDetails taxAuthoritiesDetails = documentAggregate.getTaxAuthoritiesDetails();
        return CSVExportDocument.builder()
                .issuerCountry(document.getIssuer() != null ?
                        document.getIssuer().getAddress() != null ? document.getIssuer().getAddress().getCountry() : null
                        : null)
                .documentType(document.getType())
                .navDocumentId(document.getSid())
                .navIssueDate(document.getIssuedDate() == null ? null :
                        document.getIssuedDate().format(DateUtils.DATE_TIME_FORMATTER_SHORT_VERSION))
                .generatedBy(document.getGeneratedBy())
                .receiverType(document.getReceiver() != null ? document.getReceiver().getType() : null)
                .receiverId(document.getReceiver() != null ? document.getReceiver().getTaxIdentificationNumber() : "")
                .receiverName(document.getReceiver() != null ? document.getReceiver().getName() : null)
                .receiverCountry(document.getReceiver() != null ?
                        document.getReceiver().getAddress() != null ? document.getReceiver().getAddress().getCountry() : null
                        : null)
                .receiverAddress(document.getReceiver() != null ? document.getReceiver().getAddress() : null)
                .currency(document.getCurrency())
                .totalNetValue(document.getNetAmount())
                .totalTaxValue(document.getTaxAmount())
                .discountValue(document.getDiscountAmount())
                .totalValue(document.getTotalAmount())
                .billDocumentId(document.getId())
                .documentCreationDate(document.getCreatedAt() == null ? null :
                        document.getCreatedAt().format(DateUtils.DATE_TIME_FORMATTER_SHORT_VERSION))
                .documentSubmissionDate(taxAuthoritiesDetails == null ? null :
                        taxAuthoritiesDetails.getCreatedAt()
                        .format(DateUtils.DATE_TIME_FORMATTER_SHORT_VERSION))
                .documentStatus(document.getStatus())
                .taxAuthorityLastErrorMessage(taxAuthoritiesDetails != null ?
                        formatAndTrimExceptionStackTrace(taxAuthoritiesDetails.getException(),
                                exceptionStackTraceLinesLimit) : null)
                .build();
    }

    private static String formatAndTrimExceptionStackTrace(String exceptionStackTrace,
                                                    int exceptionStackTraceLinesLimit) {
        if (exceptionStackTrace == null) {
            return null;
        }
        String[] splitStr = exceptionStackTrace.split("\r\n|\r|\n");

        if (splitStr.length > exceptionStackTraceLinesLimit) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < exceptionStackTraceLinesLimit; i++) {
                sb.append(splitStr[i]);
            }
            return sb.toString();
        }
        return exceptionStackTrace;
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class IssuerApiResponsePayload {

    UUID id;
    IssuerType type;

    //
    // Identification
    //

    String legalName;
    String name;
    String taxIdentificationNumber;
    String businessRegistrationNumber;
    String branch;

    //
    // Contact
    //

    AddressApiResponsePayload address;
    String email;
    String mobilePhone;
    String linePhone;

    //
    // Audit information
    //

    LocalDateTime createdAt;
    String createdBy;

    public IssuerApiResponsePayload(Issuer issuer){
        this.id = issuer.getId();
        this.type = issuer.getType();
        this.legalName = issuer.getLegalName();
        this.name = issuer.getName();
        this.taxIdentificationNumber = issuer.getTaxIdentificationNumber();
        this.businessRegistrationNumber = issuer.getBusinessRegistrationNumber();
        this.branch = issuer.getBranch();
        this.email = issuer.getEmail();
        this.mobilePhone = issuer.getMobilePhone();
        this.linePhone = issuer.getLinePhone();
        this.address = issuer.getAddress() != null ? new AddressApiResponsePayload(issuer.getAddress()) : null;
        this.createdAt = issuer.getCreatedAt();
        this.createdBy = issuer.getCreatedBy();
    }

    public Issuer toEntity() {
        return Issuer.builder()
                .id(id)
                .type(type)
                .legalName(legalName)
                .name(name)
                .taxIdentificationNumber(taxIdentificationNumber)
                .businessRegistrationNumber(businessRegistrationNumber)
                .branch(branch)
                .address(address != null ? address.toEntity() : null)
                .email(email)
                .mobilePhone(mobilePhone)
                .linePhone(linePhone)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .build();
    }
}

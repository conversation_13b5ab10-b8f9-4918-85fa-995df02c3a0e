package pt.jumia.services.bill.api.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.bill.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.CreateCsvException;
import pt.jumia.services.bill.domain.exceptions.CreateStatementException;
import pt.jumia.services.bill.domain.exceptions.DataIntegrityViolationException;
import pt.jumia.services.bill.domain.exceptions.DuplicatedEntryException;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.exceptions.ReceiverTypeException;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.exceptions.document.MissingDocumentPdfException;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationException;

import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Registers the error handlers. All the exceptions that are thrown by the controllers can be dealt with in here
 */
@ControllerAdvice
@Slf4j
public class RestErrorHandler extends ResponseEntityExceptionHandler {

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatus status,
                                                                  WebRequest request) {
        List<CodedErrorResponsePayload> errorFields = ex.getBindingResult().getFieldErrors().stream()
                .map(error -> CodedErrorResponsePayload.forSingleError(
                        ErrorCode.INVALID_PAYLOAD,
                        String.format("'%s' rejected for field '%s': %s",
                                error.getRejectedValue() == null ? null : error.getRejectedValue().toString(),
                                error.getField(),
                                error.getDefaultMessage())))
                .collect(Collectors.toList());

        if (errorFields.isEmpty()) {
            ObjectError globalError = ex.getBindingResult().getGlobalError();
            CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forSingleError(
                    ErrorCode.INVALID_PAYLOAD,
                    globalError != null ? globalError.getDefaultMessage() : null
            );
            return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
        }

        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forErrorWithCauses(
                ErrorCode.INVALID_PAYLOAD,
                "Invalid payload",
                errorFields
        );
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleBindException(BindException ex,
                                                         HttpHeaders headers,
                                                         HttpStatus status,
                                                         WebRequest request) {
        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forErrorWithCauses(
                ErrorCode.INVALID_PAYLOAD,
                "Invalid payload",
                ex.getFieldErrors().stream()
                        .map(fieldError -> CodedErrorResponsePayload.forSingleError(
                                ErrorCode.INVALID_PAYLOAD,
                                String.format("'%s' rejected for field '%s': %s",
                                        fieldError.getRejectedValue(),
                                        fieldError.getField(),
                                        fieldError.getDefaultMessage()
                                )))
                        .collect(Collectors.toList()));
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleTypeMismatch(TypeMismatchException ex,
                                                        HttpHeaders headers,
                                                        HttpStatus status,
                                                        WebRequest request) {
        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forSingleError(
                ErrorCode.INVALID_PAYLOAD,
                String.format("'%s' rejected for type %s",
                        ex.getValue(),
                        ex.getRequiredType() != null ?
                                ex.getRequiredType().getSimpleName() : ""
                ));
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex,
                                                                  HttpHeaders headers,
                                                                  HttpStatus status,
                                                                  WebRequest request) {
        //exception thrown by Spring when JSON format does not match the expected payload
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.INVALID_PAYLOAD, ex.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles constraint exceptions on arguments annotated with {@link org.springframework.validation.annotation.Validated}
     * <p>
     * Will make sure we have a detailed and comprehensive response with errors by field if applicable
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    public ResponseEntity<CodedErrorResponsePayload> handleConstraintValidation(ConstraintViolationException exception) {
        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forErrorWithCauses(
                ErrorCode.INVALID_PAYLOAD,
                "Invalid payload",
                exception.getConstraintViolations().stream()
                        .map(constraintViolation -> CodedErrorResponsePayload.forSingleError(
                                ErrorCode.INVALID_PAYLOAD,
                                String.format("'%s' rejected for field '%s': %s",
                                        constraintViolation.getInvalidValue(),
                                        constraintViolation.getPropertyPath().toString(),
                                        constraintViolation.getMessage()
                                )))
                        .collect(Collectors.toList()));
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler
    private ResponseEntity<CodedErrorResponsePayload> handleGeneric(Exception e) {
        log.error("Unhandled exception", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.INTERNAL_ERROR, "Unexpected exception");
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = CodedException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleGenericCodedException(CodedException e) {
        log.error("Unhandled coded exception", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), "Unexpected exception");
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = EntityNotFoundException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleNotFound(EntityNotFoundException e) {
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(value = DuplicatedEntryException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleDuplicateEntry(DuplicatedEntryException e) {
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = UserForbiddenException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleUserForbidden(UserForbiddenException e) {
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(value = AclErrorException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleAclException(AclErrorException e) {
        log.error("ACL error exception", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.ACL_ERROR, e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.valueOf(e.getCode()));
    }

    @ExceptionHandler(value = MissingDocumentPdfException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleDocumentJudgeSidException(MissingDocumentPdfException e) {
        log.error("Could not find judge sid in document", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.MISSING_DOCUMENT_PDF, e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(value = DocumentValidationException.class)
    public ResponseEntity<CodedErrorResponsePayload> handleConstraintValidation(DocumentValidationException exception) {
        List<CodedErrorResponsePayload> errors = exception.getErrors()
                .stream()
                .map(CodedErrorResponsePayload::new)
                .collect(Collectors.toList());

        CodedErrorResponsePayload errorPayload = CodedErrorResponsePayload.forErrorWithCauses(
                exception.getErrorCode(),
                exception.getMessage(),
                errors
        );
        return new ResponseEntity<>(errorPayload, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = ConflictOperationException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleConflictOperationException(ConflictOperationException e) {
        log.warn("", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = TaxiNetworkException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleTaxiNetworkException(TaxiNetworkException e) {
        log.warn("", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(value = DataIntegrityViolationException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleDataIntegrityViolationException(DataIntegrityViolationException e) {
        log.error("DataIntegrityViolationException ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.DATA_INTEGRITY_VIOLATION, e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("IllegalArgumentException ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.ILLEGAL_ARGUMENT, e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = ReceiverTypeException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleReceiverTypeException(ReceiverTypeException e) {
        log.error("ReceiverTypeException ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_IMPLEMENTED);
    }

    @ExceptionHandler(value = CreateCsvException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleCreateCsvException(CreateCsvException e) {
        log.error("CreateCsvException ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = CreateStatementException.class)
    private ResponseEntity<CodedErrorResponsePayload> handleCreateStatementException(CreateStatementException e) {
        log.error("CreateStatementException ", e);
        CodedErrorResponsePayload errorResponse = CodedErrorResponsePayload.forSingleError(e.getErrorCode(), e.getMessage());
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }
}

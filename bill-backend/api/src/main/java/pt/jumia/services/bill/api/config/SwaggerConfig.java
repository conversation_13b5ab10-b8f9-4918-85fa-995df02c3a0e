package pt.jumia.services.bill.api.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pt.jumia.services.bill.domain.properties.ApiProperties;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Collections;

import static springfox.documentation.builders.PathSelectors.regex;

/**
 * Swagger configuration for API documentation
 */
@Configuration
@RequiredArgsConstructor
@EnableSwagger2
public class SwaggerConfig {

    private static final String API_ENDPOINT = "/api.*";

    private final ApiProperties apiProperties;

    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .enable(apiProperties.isSwaggerEnabled())
                .groupName("Bill API")
                .apiInfo(new ApiInfoBuilder()
                        .title("Bill API")
                        .version("v1")
                        .description("Description")
                        .build())
                .globalOperationParameters(
                        Collections.singletonList(new ParameterBuilder()
                                .name("Authorization")
                                .description("Bearer token")
                                .modelRef(new ModelRef("string"))
                                .parameterType("header")
                                .required(true)
                                .build()))
                .select()
                .apis(RequestHandlerSelectors.any())
                .paths(regex(API_ENDPOINT))
                .build();
    }
}

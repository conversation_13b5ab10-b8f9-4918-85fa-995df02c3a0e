package pt.jumia.services.bill.api.payloads.response;

import com.neovisionaries.i18n.CountryCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentPossibleOperations;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DocumentAggregateApiResponsePayload {

    //
    // Internal fields
    //

    private UUID id;
    private DocumentStatus status;
    private String judgeSid;

    //
    // General information
    //

    private CountryCode country;
    private String shop;
    private DocumentType type;
    private String sid;
    private DocumentFlow flow;
    private String generatedBy;
    private String referenceNumber;
    private String purchaseReferenceNumber;
    private LocalDateTime issuedDate;

    private LocalDateTime receivedDate;
    private String currency;
    private BigDecimal issuedToLocalCurrencyExchangeRate;
    private ReceiverApiResponsePayload receiver;
    private IssuerApiResponsePayload issuer;
    private DocumentIdApiResponsePayload originalDocument;
    private String notes;

    private List<DocumentLineApiResponsePayload> lines;
    private List<DocumentTransformationApiResponsePayload> documentTransformations;
    private PaymentDetailsApiResponsePayload payment;
    private DeliveryDetailsApiResponsePayload delivery;
    private IssuedReasonApiResponsePayload issuedReason;

    //
    // Summary
    //

    private int lineCount;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalItemsDiscountAmount;
    private BigDecimal extraDiscountAmount;

    private List<TaxCategoryTotalApiResponsePayload> taxCategoryTotals;
    private TaxAuthoritiesDetailsApiResponsePayload taxAuthoritiesDetails;

    private DocumentPossibleOperationsApiResponsePayload operations;

    private OtherSplitDocumentApiResponsePayload otherSplitDocument;
    private boolean conflictStatusWithOtherSplitDocument;

    //
    // Audit information
    //

    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    private Boolean reviewed;
    private LocalDateTime reviewedAt;
    private String reviewedBy;

    public DocumentAggregateApiResponsePayload(DocumentAggregate aggregate) {
        Document document = aggregate.getDocument();
        this.id = document.getId();
        this.status = document.getStatus();
        this.judgeSid = document.getJudgeSid();
        this.country = document.getCountry();
        this.shop = document.getShop();
        this.type = document.getType();
        this.sid = document.getSid();
        this.flow = document.getFlow();
        this.generatedBy = document.getGeneratedBy();
        this.referenceNumber = document.getReferenceNumber();
        this.purchaseReferenceNumber = document.getPurchaseReferenceNumber();
        this.issuedDate = document.getIssuedDate();
        this.receivedDate = document.getReceivedDate();
        this.currency = document.getCurrency().getCurrencyCode();
        this.issuedToLocalCurrencyExchangeRate = document.getIssuedToLocalCurrencyExchangeRate();
        this.receiver = new ReceiverApiResponsePayload(document.getReceiver());
        this.issuer = new IssuerApiResponsePayload(document.getIssuer());
        this.originalDocument = document.getOriginalDocument() != null
                ? new DocumentIdApiResponsePayload(document.getOriginalDocument())
                : null;
        this.payment = document.getPayment() != null
                ? new PaymentDetailsApiResponsePayload(document.getPayment())
                : null;
        this.delivery = document.getDelivery() != null
                ? new DeliveryDetailsApiResponsePayload(document.getDelivery())
                : null;
        this.issuedReason = document.getIssuedReason() != null
                ? new IssuedReasonApiResponsePayload(document.getIssuedReason())
                : null;
        this.notes = document.getNotes();
        this.lineCount = document.getLineCount();
        this.totalAmount = document.getTotalAmount();
        this.netAmount = document.getNetAmount();
        this.taxAmount = document.getTaxAmount();
        this.discountAmount = document.getDiscountAmount();
        this.totalItemsDiscountAmount = document.getTotalItemsDiscountAmount();
        this.extraDiscountAmount = document.getExtraDiscountAmount();
        this.createdAt = document.getCreatedAt();
        this.createdBy = document.getCreatedBy();
        this.updatedAt = document.getUpdatedAt();
        this.updatedBy = document.getUpdatedBy();

        this.lines = aggregate.getLines().stream()
                .map(DocumentLineApiResponsePayload::new)
                .collect(Collectors.toList());
        this.documentTransformations =
                aggregate.getDocumentTransformations() == null
                        ? null :
                        aggregate.getDocumentTransformations().stream()
                                .map(DocumentTransformationApiResponsePayload::new)
                                .collect(Collectors.toList());
        this.taxCategoryTotals = aggregate.getTaxCategoryTotals().stream().map(TaxCategoryTotalApiResponsePayload::new)
                .collect(Collectors.toList());
        this.taxAuthoritiesDetails = aggregate.getTaxAuthoritiesDetails() != null
                ? new TaxAuthoritiesDetailsApiResponsePayload(aggregate.getTaxAuthoritiesDetails())
                : null;
        this.operations = new DocumentPossibleOperationsApiResponsePayload(document.getDocumentPossibleOperations());
        this.otherSplitDocument = document.getOtherSplitDocument() == null ? null :
                new OtherSplitDocumentApiResponsePayload(document.getOtherSplitDocument());
        this.conflictStatusWithOtherSplitDocument = document.getOtherSplitDocument() != null &&
                !document.getStatus().equals(document.getOtherSplitDocument().getStatus());
        this.reviewed = document.getReviewed();
        this.reviewedAt = document.getReviewedAt();
        this.reviewedBy = document.getReviewedBy();
    }

    public DocumentAggregate toEntity() {
        DocumentPossibleOperations documentPossibleOperations = null;
        if (this.operations != null) {
            documentPossibleOperations = DocumentPossibleOperations.builder()
                    .canApprove(this.operations.isCanApprove())
                    .canAcknowledge(this.operations.isCanAcknowledge())
                    .canCancel(this.operations.isCanCancel())
                    .canDeclineDocumentRejection(this.operations.isCanDeclineDocumentRejection())
                    .build();
        }
        Document document = Document
                .builder()
                .id(this.id)
                .judgeSid(this.judgeSid)
                .status(this.status)
                .country(this.country)
                .shop(this.shop)
                .type(this.type)
                .sid(this.sid)
                .flow(this.flow)
                .generatedBy(this.generatedBy)
                .referenceNumber(this.referenceNumber)
                .issuedDate(this.issuedDate)
                .receivedDate(this.receivedDate)
                .currency(Currency.getInstance(this.currency))
                .receiver(this.receiver.toEntity())
                .issuer(this.issuer.toEntity())
                .originalDocument(originalDocument != null ? originalDocument.toEntity() : null)
                .payment(payment != null ? payment.toEntity() : null)
                .delivery(delivery != null ? delivery.toEntity() : null)
                .issuedReason(issuedReason != null ? issuedReason.toEntity() : null)
                .notes(this.notes)
                .lineCount(this.lineCount)
                .totalAmount(this.totalAmount)
                .netAmount(this.netAmount)
                .taxAmount(this.taxAmount)
                .discountAmount(this.discountAmount)
                .totalItemsDiscountAmount(this.totalItemsDiscountAmount)
                .extraDiscountAmount(this.extraDiscountAmount)
                .documentPossibleOperations(documentPossibleOperations)
                .otherSplitDocument(otherSplitDocument != null ? otherSplitDocument.toEntity() :
                        null)
                .reviewed(this.reviewed)
                .reviewedAt(this.reviewedAt)
                .reviewedBy(this.reviewedBy)
                .build();

        List<DocumentLine> lines = this.lines
                .stream()
                .map(documentLine -> documentLine.toEntity(document))
                .collect(Collectors.toList());
        List<DocumentTransformation> documentTransformations = this.documentTransformations == null ? null :
                this.documentTransformations
                        .stream()
                        .map(documentTransformation -> documentTransformation.toEntity(document))
                        .collect(Collectors.toList());

        List<TaxCategoryTotal> taxCategoryTotals = this.taxCategoryTotals
                .stream()
                .map(taxCategoryTotal -> taxCategoryTotal.toEntity(document))
                .collect(Collectors.toList());

        return DocumentAggregate
                .builder()
                .document(document)
                .lines(lines)
                .documentTransformations(documentTransformations)
                .taxCategoryTotals(taxCategoryTotals)
                .taxAuthoritiesDetails(taxAuthoritiesDetails != null ? taxAuthoritiesDetails.toEntity(document) : null)
                .build();
    }
}

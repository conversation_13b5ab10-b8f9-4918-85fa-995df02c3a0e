package pt.jumia.services.bill.api.validations.validators;

import pt.jumia.services.bill.api.validations.annotations.ValidPercentRate;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

public class PercentRateValidator implements ConstraintValidator<ValidPercentRate, BigDecimal> {
    @Override
    public boolean isValid(BigDecimal rate, ConstraintValidatorContext context) {
        if (rate == null) {
            return true;
        }

        return rate.compareTo(BigDecimal.ZERO) >= 0
                && rate.compareTo(BigDecimal.ONE) <= 0;
    }
}

package pt.jumia.services.bill.api.payloads.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;

import javax.validation.Valid;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class ReceiverApiRequestPayload {

    @ValidEnumValue(required = true, enumClass = ReceiverType.class)
    private String type;

    //
    // Identification
    //

    private String legalName;
    private String name;
    private String nationalIdentificationNumber;
    private String taxIdentificationNumber;
    private String businessRegistrationNumber;

    //
    // Contact
    //

    @Valid
    private AddressApiRequestPayload address;
    private String email;
    private String mobilePhone;
    private String linePhone;

    public ReceiverApiRequestPayload(Receiver entity) {
        this.type = entity.getType().name();
        this.legalName = entity.getLegalName();
        this.name = entity.getName();
        this.nationalIdentificationNumber = entity.getNationalIdentificationNumber();
        this.taxIdentificationNumber = entity.getTaxIdentificationNumber();
        this.businessRegistrationNumber = entity.getBusinessRegistrationNumber();
        this.address = entity.getAddress() != null ? new AddressApiRequestPayload(entity.getAddress()) : null;
        this.email = entity.getEmail();
        this.mobilePhone = entity.getMobilePhone();
        this.linePhone = entity.getLinePhone();
    }

    public Receiver toEntity() {
        return Receiver
                .builder()
                .type(ReceiverType.valueOf(this.type))
                .legalName(this.legalName)
                .name(this.name)
                .nationalIdentificationNumber(this.nationalIdentificationNumber)
                .taxIdentificationNumber(this.taxIdentificationNumber)
                .businessRegistrationNumber(this.businessRegistrationNumber)
                .address(this.address != null ? this.address.toEntity() : null)
                .email(this.email)
                .mobilePhone(this.mobilePhone)
                .linePhone(this.linePhone)
                .generateId()
                .build();
    }
}

package pt.jumia.services.bill.api.payloads.request;

import com.neovisionaries.i18n.CountryCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.api.validations.annotations.ValidCurrencyCode;
import pt.jumia.services.bill.api.validations.annotations.ValidEnumValue;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;
import pt.jumia.services.bill.domain.entities.SourceType;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DocumentApiRequestPayload {

    //
    // General information
    //

    @ValidEnumValue(required = true, enumClass = CountryCode.class)
    private String country;
    @NotBlank
    private String shop;
    @ValidEnumValue(required = true, enumClass = DocumentType.class)
    private String type;
    @NotBlank
    private String sid;
    @ValidEnumValue(required = true, enumClass = DocumentFlow.class)
    private String flow;
    @NotBlank
    private String generatedBy;
    private String referenceNumber;
    private String purchaseReferenceNumber;
    @NotNull
    private LocalDateTime issuedDate;
    @ValidCurrencyCode(required = true)
    private String currency;
    private BigDecimal issuedToLocalCurrencyExchangeRate;

    @NotNull
    @Valid
    private ReceiverApiRequestPayload receiver;
    @NotNull
    @Valid
    private IssuerApiRequestPayload issuer;
    @Valid
    private DocumentIdApiRequestPayload originalDocument;
    private String notes;
    @Valid
    private DocumentApiRequestPayload.IssuedReasonApiRequestPayload issuedReason;

    @NotEmpty
    @Valid
    private List<DocumentLineApiRequestPayload> lines;

    //
    // Summary
    //

    @NotNull
    private Integer lineCount;
    @NotNull
    private BigDecimal totalAmount;
    @NotNull
    private BigDecimal netAmount;
    @NotNull
    private BigDecimal taxAmount;
    private BigDecimal totalItemsDiscountAmount;
    private BigDecimal extraDiscountAmount;
    private BigDecimal discountAmount;

    @NotEmpty
    @Valid
    private List<TaxCategoryTotalApiRequestPayload> taxCategoryTotals;

    @Valid
    private PaymentDetailsApiRequestPayload payment;
    @Valid
    private DeliveryDetailsApiRequestPayload delivery;

    @ValidEnumValue(enumClass = SourceType.class)
    private String sourceType;
    private String postingGroup;

    private String status;

    private LocalDateTime receivedDate;

    private TaxAuthoritiesDetailsApiRequestPayload taxAuthoritiesDetails;

    private String transactionType;

    private String transactionProgress;

    public DocumentApiRequestPayload(DocumentAggregate entity) {
        Document document = entity.getDocument();

        this.country = document.getCountry().getAlpha2();
        this.shop = document.getShop();
        this.type = document.getType().name();
        this.sid = document.getSid();
        this.flow = document.getFlow().name();
        this.generatedBy = document.getGeneratedBy();
        this.referenceNumber = document.getReferenceNumber();
        this.issuedDate = document.getIssuedDate();
        this.currency = document.getCurrency().getCurrencyCode();
        this.issuedToLocalCurrencyExchangeRate = document.getIssuedToLocalCurrencyExchangeRate();
        this.receiver = document.getReceiver() != null ? new ReceiverApiRequestPayload(document.getReceiver()) : null;
        this.issuer = document.getIssuer() != null ? new IssuerApiRequestPayload(document.getIssuer()) : null;
        this.originalDocument = document.getOriginalDocument() != null ?
                new DocumentIdApiRequestPayload(document.getOriginalDocument()) : null;
        this.notes = document.getNotes();
        this.issuedReason = document.getIssuedReason() == null ?
                null : new IssuedReasonApiRequestPayload(document.getIssuedReason());
        this.lineCount = document.getLineCount();
        this.totalAmount = document.getTotalAmount();
        this.netAmount = document.getNetAmount();
        this.taxAmount = document.getTaxAmount();
        this.discountAmount = document.getDiscountAmount();
        this.totalItemsDiscountAmount = document.getTotalItemsDiscountAmount();
        this.extraDiscountAmount = document.getExtraDiscountAmount();
        this.lines = entity.getLines() != null
                ? entity.getLines().stream().map(DocumentLineApiRequestPayload::new).collect(Collectors.toList())
                : null;
        this.taxCategoryTotals = entity.getTaxCategoryTotals() != null
                ? entity.getTaxCategoryTotals().stream().map(TaxCategoryTotalApiRequestPayload::new).collect(Collectors.toList())
                : null;
        this.payment = document.getPayment() != null ?
                new PaymentDetailsApiRequestPayload(document.getPayment()) : null;
        this.delivery = document.getDelivery() != null ?
                new DeliveryDetailsApiRequestPayload(document.getDelivery()) : null;

        this.sourceType = document.getSourceType();
        this.postingGroup = document.getPostingGroup();

        this.status = document.getStatus().name();
        this.receivedDate = document.getReceivedDate();
        this.transactionType = document.getTransactionType();
        this.transactionProgress = document.getTransactionProgress();

    }

    public DocumentAggregate toEntity() {

        DocumentStatus documentStatus = DocumentStatus.NEW;
        if (this.status != null) {
            if (this.flow.equals(DocumentFlow.RECEIVED.name())) {
                documentStatus = TaxAuthoritiesDetailsApiRequestPayload.ApiLogStatus.valueOf(this.status)
                        .getDocumentStatus();
            } else {
                documentStatus = DocumentStatus.valueOf(this.status);
            }
        }

        Document document = Document
                .builder()
                .country(CountryCode.valueOf(this.country))
                .shop(this.shop)
                .type(DocumentType.valueOf(this.type))
                .sid(this.sid)
                .flow(DocumentFlow.valueOf(this.flow))
                .generatedBy(this.generatedBy)
                .referenceNumber(this.referenceNumber)
                .purchaseReferenceNumber(this.purchaseReferenceNumber)
                .issuedDate(this.issuedDate)
                .currency(Currency.getInstance(this.currency))
                .issuedToLocalCurrencyExchangeRate(this.issuedToLocalCurrencyExchangeRate)
                .receiver(this.receiver.toEntity())
                .issuer(this.issuer.toEntity())
                .originalDocument(originalDocument != null ? originalDocument.toEntity() : null)
                .notes(this.notes)
                .issuedReason(this.issuedReason != null ? issuedReason.toEntity() : null)
                .lineCount(this.lineCount)
                .totalAmount(this.totalAmount)
                .netAmount(this.netAmount)
                .taxAmount(this.taxAmount)
                .discountAmount(this.discountAmount)
                .totalItemsDiscountAmount(this.totalItemsDiscountAmount)
                .extraDiscountAmount(this.extraDiscountAmount)
                .payment(this.payment != null ? this.payment.toEntity() : null)
                .delivery(this.delivery != null ? this.delivery.toEntity() : null)
                .generateId()
                .sourceType(this.sourceType)
                .postingGroup(this.postingGroup)
                .status(documentStatus)
                .receivedDate(this.receivedDate)
                .transactionType(this.transactionType)
                .transactionProgress(this.transactionProgress)
                .build();

        List<DocumentLine> lines = this.lines
                .stream()
                .map(documentLine -> documentLine.toEntity(document))
                .collect(Collectors.toList());

        List<TaxCategoryTotal> taxCategoryTotals = this.taxCategoryTotals
                .stream()
                .map(taxCategoryTotal -> taxCategoryTotal.toEntity(document))
                .collect(Collectors.toList());

        return DocumentAggregate
                .builder()
                .document(document)
                .lines(lines)
                .taxCategoryTotals(taxCategoryTotals)
                .taxAuthoritiesDetails(taxAuthoritiesDetails != null ?
                        taxAuthoritiesDetails.toEntity(document) : null)
                .build();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class IssuedReasonApiRequestPayload {
        @ValidEnumValue(required = true, enumClass = IssuedReasonCode.class)
        private String code;
        private String notes;

        public IssuedReasonApiRequestPayload(IssuedReason entity) {
            this.code = entity.getCode() != null ? entity.getCode().name() : null;
            this.notes = entity.getNotes();
        }

        public IssuedReason toEntity() {
            return IssuedReason.builder()
                    .code(IssuedReasonCode.valueOf(code))
                    .notes(notes)
                    .generateId()
                    .build();
        }
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.PaymentDetails;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class PaymentDetailsApiResponsePayload {

    private UUID id;
    private String bankName;
    private String bankAddress;
    private String bankAccountNo;
    private String bankAccountIBAN;
    private String swiftCode;
    private String terms;
    private LocalDateTime createdAt;
    private String createdBy;

    public PaymentDetailsApiResponsePayload(PaymentDetails entity){
        this.id = entity.getId();
        this.bankName = entity.getBankName();
        this.bankAddress = entity.getBankAddress();
        this.bankAccountNo = entity.getBankAccountNo();
        this.bankAccountIBAN = entity.getBankAccountIBAN();
        this.swiftCode = entity.getSwiftCode();
        this.terms = entity.getTerms();
        this.createdAt = entity.getCreatedAt();
        this.createdBy = entity.getCreatedBy();
    }

    public PaymentDetails toEntity() {
        return PaymentDetails
                .builder()
                .id(this.id)
                .bankName(this.bankName)
                .bankAddress(this.bankAddress)
                .bankAccountNo(this.bankAccountNo)
                .bankAccountIBAN(this.bankAccountIBAN)
                .swiftCode(this.swiftCode)
                .terms(this.terms)
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .build();
    }
}

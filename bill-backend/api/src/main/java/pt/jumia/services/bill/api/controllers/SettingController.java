package pt.jumia.services.bill.api.controllers;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import pt.jumia.services.bill.api.payloads.request.SettingRequestPayload;
import pt.jumia.services.bill.api.payloads.response.SettingResponsePayload;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.settings.UpdateSettingUseCase;
import pt.jumia.services.bill.domain.usecases.settings.ReadSettingUseCase;
import pt.jumia.services.bill.domain.usecases.settings.CreateSettingUseCase;
import pt.jumia.services.bill.domain.usecases.settings.DeleteSettingUseCase;
import pt.jumia.services.bill.domain.usecases.settings.ReloadSettingsUseCase;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/settings")
@RequiredArgsConstructor
@Slf4j
public class SettingController {

    private final UserPermissionValidationUseCase userPermissionValidationUseCase;
    private final ReadSettingUseCase readSettingUseCase;
    private final CreateSettingUseCase createSettingUseCase;
    private final UpdateSettingUseCase updateSettingUseCase;
    private final DeleteSettingUseCase deleteSettingUseCase;
    private final ReloadSettingsUseCase reloadSettingsUseCase;

    @GetMapping
    public List<SettingResponsePayload> fetch() {

        userPermissionValidationUseCase.checkCanAccessOrThrow(RequestContext.getUser());

        return readSettingUseCase.fetchAll()
                .stream()
                .map(SettingResponsePayload::new)
                .collect(Collectors.toList());
    }

    @GetMapping(value = "/{id}")
    public SettingResponsePayload fetchById(@PathVariable(value = "id") Long id) {

        userPermissionValidationUseCase.checkCanAccessOrThrow(RequestContext.getUser());

        return new SettingResponsePayload(readSettingUseCase.fetchById(id));
    }

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public SettingResponsePayload create(@RequestBody @Valid SettingRequestPayload settingRequestPayload) {

        userPermissionValidationUseCase.checkCanManageSettingsOrThrow(RequestContext.getUser());

        Setting persistedSetting = createSettingUseCase.execute(settingRequestPayload.toEntity());

        return new SettingResponsePayload(persistedSetting);
    }

    @PutMapping(value = "/{id}")
    public SettingResponsePayload update(@PathVariable(value = "id") Long id, @RequestBody @Valid SettingRequestPayload settingRequestPayload) {

        userPermissionValidationUseCase.checkCanManageSettingsOrThrow(RequestContext.getUser());

        return new SettingResponsePayload(updateSettingUseCase.execute(id, settingRequestPayload.toEntity()));
    }

    @PatchMapping(value = "/reload")
    public void reloadSettings() {

        userPermissionValidationUseCase.checkCanManageSettingsOrThrow(RequestContext.getUser());

        reloadSettingsUseCase.execute();
    }

    @DeleteMapping(value = "/{id}")
    public void delete(@PathVariable(value = "id") Long id) {

        userPermissionValidationUseCase.checkCanManageSettingsOrThrow(RequestContext.getUser());

        deleteSettingUseCase.execute(id);
    }
}

package pt.jumia.services.bill.api.payloads.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class IssuedReasonApiResponsePayload {

    private UUID id;
    private String code;
    private String notes;
    private LocalDateTime createdAt;
    private String createdBy;

    public IssuedReasonApiResponsePayload(IssuedReason entity){
        this.id = entity.getId();
        this.code = entity.getCode() != null ?
                entity.getCode().name() : null;
        this.notes = entity.getNotes();
        this.createdAt = entity.getCreatedAt();
        this.createdBy = entity.getCreatedBy();
    }

    public IssuedReason toEntity() {
        return IssuedReason
                .builder()
                .id(this.id)
                .code(this.code == null ? null :
                        IssuedReasonCode.valueOf(this.code))
                .notes(this.notes)
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .build();
    }
}

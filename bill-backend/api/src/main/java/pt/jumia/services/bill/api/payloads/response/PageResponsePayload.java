package pt.jumia.services.bill.api.payloads.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponsePayload<T> {
    Links links;
    Integer page;
    Integer size;
    List<T> results;

    /**
     * The payload with the links related to this pagination.
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Links {
        String base;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String previousPage;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        String nextPage;
    }
}

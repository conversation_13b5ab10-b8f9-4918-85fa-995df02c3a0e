package pt.jumia.services.bill.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import pt.jumia.services.bill.api.payloads.request.RejectionReasonRequestPayload;
import pt.jumia.services.bill.api.payloads.request.SendEmailRequestPayload;
import pt.jumia.services.bill.api.payloads.request.TaxAuthoritiesDetailsApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.bill.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentPossibleOperations;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentAllowedOperationsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DownloadDocumentFromTaxAuthoritiesUseCase;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.domain.usecases.documents.SendEmailDocumentToReceiverUseCase;
import pt.jumia.services.bill.domain.usecases.documents.UpdateDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.receiveddocuments.ApproveReceivedDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.receiveddocuments.DeclineReceivedDocumentCancellationUseCase;
import pt.jumia.services.bill.domain.usecases.receiveddocuments.RejectReceivedDocumentUseCase;

import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;

@WebMvcTest(ReceivedDocumentController.class)
public class ReceivedDocumentControllerTest extends BaseControllerTest {
    private static final String BASE_ROUTE = "/api/received-documents";
    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("even_more_dummy")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .generateId()
                    .category(Category.builder().sid("fake_category").build())
                    .appliedTaxes(List.of(DocumentLine.AppliedTax.builder().build()))
                    .build()))
            .taxCategoryTotals(List.of(TaxCategoryTotal.builder().generateId().build()))
            .build();

    @MockBean
    private UserPermissionValidationUseCase userPermissionValidationUseCase;

    @MockBean
    private RejectReceivedDocumentUseCase rejectReceivedDocumentUseCase;
    @MockBean
    private ApproveReceivedDocumentUseCase approveReceivedDocumentUseCase;
    @MockBean
    private ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    @MockBean
    private UpdateDocumentUseCase updateDocumentUseCase;
    @MockBean
    private DeclineReceivedDocumentCancellationUseCase declineCancellationReceivedDocumentUseCase;
    @MockBean
    private DownloadDocumentFromTaxAuthoritiesUseCase downloadDocumentFromTaxAuthoritiesUseCase;
    @MockBean
    private DocumentAllowedOperationsUseCase documentAllowedOperationsUseCase;
    @MockBean
    private SendEmailDocumentToReceiverUseCase sendEmailDocumentToReceiverUseCase;

    @Test
    public void testFetchTaxAuthoritiesPdf_success() throws Exception {

        UUID documentUUID = UUID.fromString("5ba7ed63-9b6e-4c95-b748-332fcd03a2ff");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .reviewed(false)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        DocumentFile fakeDocumentFromTaxAuthorities = DocumentFile.builder()
                .content("123".getBytes())
                .mediaType("application/pdf")
                .build();

        when(readDocumentAggregateUseCase.execute(documentUUID)).thenReturn(documentAggregate);
        when(downloadDocumentFromTaxAuthoritiesUseCase.execute(document.getId().toString()))
                .thenReturn(fakeDocumentFromTaxAuthorities);

        request(
                get(BASE_ROUTE + "/" + documentUUID + "/tax-authorities-pdf"),
                HttpStatus.OK
        );
        verify(readDocumentAggregateUseCase).execute(documentUUID);
        verify(downloadDocumentFromTaxAuthoritiesUseCase).execute(document.getId().toString());
    }

    @Test
    public void testSendReceivedDocumentEmail_success() throws Exception {

        UUID documentUUID = UUID.fromString("5ba7ed63-9b6e-4c95-b748-332fcd03a2ff");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .reviewed(false)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        SendEmailRequestPayload requestPayload = SendEmailRequestPayload.builder()
                .emails(List.of("<EMAIL>"))
                .build();

        when(readDocumentAggregateUseCase.execute(documentUUID)).thenReturn(documentAggregate);

        request(
                post(BASE_ROUTE + "/" + documentUUID + "/send-email")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestPayload)),
                HttpStatus.OK
        );

        verify(readDocumentAggregateUseCase).execute(documentUUID);
        verify(sendEmailDocumentToReceiverUseCase).execute(any(), any());
    }


    @Test
    public void SendReceivedDocumentEmail_forbidden() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .reviewed(false)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        SendEmailRequestPayload requestPayload = SendEmailRequestPayload.builder()
                .emails(List.of("<EMAIL>"))
                .build();

        when(readDocumentAggregateUseCase.execute(document.getId())).thenReturn(documentAggregate);

        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase)
                .checkCanSendDocumentsThroughEmailByCountryCodeOrThrow(REQUEST_USER, document.getCountry());

         request(
                post(BASE_ROUTE + "/" + documentUUID + "/send-email")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestPayload)),
                HttpStatus.FORBIDDEN
        );

        verify(userPermissionValidationUseCase)
                .checkCanSendDocumentsThroughEmailByCountryCodeOrThrow(
                        REQUEST_USER,
                        document.getCountry());
    }




    @Test
    public void testReview_forbidden_clientError() throws Exception {

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(DOCUMENT.toBuilder()
                        .id(UUID.randomUUID())
                        .country(CountryCode.UG)
                        .reviewed(true)
                        .build())
                .build();

        when(readDocumentAggregateUseCase.execute(documentAggregate.getDocument().getId())).thenReturn(documentAggregate);
        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase)
                .checkCanReviewReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.UG);

        CodedErrorResponsePayload response = request(
                post(BASE_ROUTE + "/" + documentAggregate.getDocument().getId() + "/review/" + true),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class
        );

        verify(userPermissionValidationUseCase)
                .checkCanReviewReceivedDocumentsByCountryCodeOrThrow(
                        REQUEST_USER,
                        CountryCode.UG);
        verify(readDocumentAggregateUseCase).execute(documentAggregate.getDocument().getId());
        verifyNoInteractions(updateDocumentUseCase);
        assertThat(response.getCode()).isEqualTo(ErrorCode.FORBIDDEN.getCode());
        assertThat(response.getName()).isEqualTo(ErrorCode.FORBIDDEN.name());
    }

    @Test
    public void testReview_success() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .reviewed(false)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();
        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        when(readDocumentAggregateUseCase.execute(documentUUID)).thenReturn(documentAggregate);
        when(updateDocumentUseCase.execute(eq(documentUUID),
                any(Document.class), eq(false)))
                .thenReturn(document);

        DocumentApiResponsePayload response = request(
                post(BASE_ROUTE + "/" + documentUUID + "/review/" + true),
                HttpStatus.OK,
                DocumentApiResponsePayload.class
        );

        verify(userPermissionValidationUseCase)
                .checkCanReviewReceivedDocumentsByCountryCodeOrThrow(
                        REQUEST_USER,
                        document.getCountry());
        verify(updateDocumentUseCase).execute(eq(documentUUID), any(Document.class), eq(false));
        assertThat(response.toEntity().withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(documentWithPossibleOperations.withoutDbField());
    }

    @Test
    public void testUpdateStatus_success() throws Exception {
        Document document = DOCUMENT.toBuilder()
                .id(UUID.randomUUID())
                .country(CountryCode.UG)
                .reviewed(false)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        when(readDocumentAggregateUseCase.execute(document.getId())).thenReturn(documentAggregate);

        request(put(BASE_ROUTE + "/" + document.getId() + "/update-status/" +
                        TaxAuthoritiesDetailsApiRequestPayload.ApiLogStatus.SUCCESS),
                HttpStatus.OK);

        verify(userPermissionValidationUseCase)
                .checkCanCreateReceivedDocumentsByCountryCodeOrThrow(
                        REQUEST_USER,
                        document.getCountry());

        verify(updateDocumentUseCase).execute(document.getId(), document.toBuilder()
                .status(DocumentStatus.TAX_SUCCESS)
                .build(), true);
    }

    @Test
    public void testUpdateStatus_forbidden() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .reviewed(false)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        when(readDocumentAggregateUseCase.execute(document.getId())).thenReturn(documentAggregate);

        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase)
                .checkCanCreateReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, document.getCountry());

        CodedErrorResponsePayload response = request(
                put(BASE_ROUTE + "/" + documentUUID + "/update-status/" +
                        TaxAuthoritiesDetailsApiRequestPayload.ApiLogStatus.SUCCESS),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class
        );

        assertThat(response.getCode()).isEqualTo(ErrorCode.FORBIDDEN.getCode());
        assertThat(response.getName()).isEqualTo(ErrorCode.FORBIDDEN.name());

        verify(userPermissionValidationUseCase)
                .checkCanCreateReceivedDocumentsByCountryCodeOrThrow(
                        REQUEST_USER,
                        document.getCountry());
    }

    @Test
    public void testRejectReceivedDocument_wrongParams() throws Exception {
        request(put(BASE_ROUTE + "/4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb/reject"),
                RejectionReasonRequestPayload.builder().rejectionReason(null).build(),
                HttpStatus.BAD_REQUEST);

        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(readDocumentAggregateUseCase);
        verifyNoInteractions(rejectReceivedDocumentUseCase);
    }

    @Test
    public void testRejectReceivedDocument_notFound() throws Exception {

        doThrow(EntityNotFoundException.createNotFound(Document.class,
                UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb")))
                .when(readDocumentAggregateUseCase)
                .execute(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"));

        request(put(BASE_ROUTE + "/4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb/reject"),
                RejectionReasonRequestPayload.builder().rejectionReason("reason").build(),
                HttpStatus.NOT_FOUND);

        verify(readDocumentAggregateUseCase)
                .execute(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(rejectReceivedDocumentUseCase);
    }

    @Test
    public void testRejectReceivedDocument_forbidden() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .reviewed(false)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        when(readDocumentAggregateUseCase.execute(document.getId())).thenReturn(documentAggregate);

        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase)
                .checkCanRejectReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, document.getCountry());


        request(put(BASE_ROUTE + "/" + documentUUID + "/reject"),
                RejectionReasonRequestPayload.builder().rejectionReason("reason").build(),
                HttpStatus.FORBIDDEN);

        verify(readDocumentAggregateUseCase).execute(document.getId());
        verify(userPermissionValidationUseCase)
                .checkCanRejectReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, document.getCountry());
        verifyNoInteractions(rejectReceivedDocumentUseCase);
    }

    @Test
    public void testRejectReceivedDocument_success() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        when(readDocumentAggregateUseCase.execute(documentUUID)).thenReturn(documentAggregate);

        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();

        when(rejectReceivedDocumentUseCase.execute(documentAggregate, "reason"))
                .thenReturn(document.toBuilder().status(DocumentStatus.TAX_REJECTED).build());

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        DocumentApiResponsePayload response = request(
                put(BASE_ROUTE + "/" + documentUUID + "/reject"),
                RejectionReasonRequestPayload.builder().rejectionReason("reason").build(),
                HttpStatus.OK,
                DocumentApiResponsePayload.class);

        assertThat(response.toEntity().withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(documentWithPossibleOperations.toBuilder()
                        .status(DocumentStatus.TAX_REJECTED)
                        .build()
                        .withoutDbField());

        verify(readDocumentAggregateUseCase).execute(documentUUID);
        verify(userPermissionValidationUseCase)
                .checkCanRejectReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, document.getCountry());
        verify(rejectReceivedDocumentUseCase).execute(documentAggregate, "reason");
    }


    @Test
    public void testDeclineReceivedDocumentCancellation_wrongParams() throws Exception {
        request(put(BASE_ROUTE + "/4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb/reject"),
                HttpStatus.BAD_REQUEST);

        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(readDocumentAggregateUseCase);
        verifyNoInteractions(declineCancellationReceivedDocumentUseCase);
    }

    @Test
    public void testDeclineReceivedDocumentCancellation_notFound() throws Exception {

        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        doThrow(EntityNotFoundException.createNotFound(Document.class,
                documentUUID))
                .when(readDocumentAggregateUseCase)
                .execute(documentUUID);

        request(put(BASE_ROUTE + "/" + documentUUID + "/decline-cancellation"),
                HttpStatus.NOT_FOUND);

        verify(readDocumentAggregateUseCase)
                .execute(documentUUID);
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(declineCancellationReceivedDocumentUseCase);
    }

    @Test
    public void testDeclineReceivedDocumentCancellation_forbidden() throws Exception {
        String documentUUID = "4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb";
        Document document = DOCUMENT.toBuilder()
                .id(UUID.fromString(documentUUID))
                .country(CountryCode.UG)
                .reviewed(false)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        when(readDocumentAggregateUseCase.execute(document.getId())).thenReturn(documentAggregate);

        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase)
                .checkCanDeclineReceivedDocumentsCancellationByCountryCodeOrThrow(REQUEST_USER, document.getCountry());


        request(put(BASE_ROUTE + "/" + documentUUID + "/decline-cancellation"),
                HttpStatus.FORBIDDEN);

        verify(readDocumentAggregateUseCase).execute(document.getId());
        verify(userPermissionValidationUseCase)
                .checkCanDeclineReceivedDocumentsCancellationByCountryCodeOrThrow(REQUEST_USER, document.getCountry());
        verifyNoInteractions(declineCancellationReceivedDocumentUseCase);
    }

    @Test
    public void testDeclineReceivedDocumentCancellation_success() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.UG)
                .reviewed(false)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
                .build();

        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();
        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        when(readDocumentAggregateUseCase.execute(documentUUID)).thenReturn(documentAggregate);
        when(declineCancellationReceivedDocumentUseCase.execute(documentAggregate))
                .thenReturn(document.toBuilder().status(DocumentStatus.TAX_SUCCESS).build());

        DocumentApiResponsePayload response = request(
                put(BASE_ROUTE + "/" + documentUUID + "/decline-cancellation"),
                HttpStatus.OK,
                DocumentApiResponsePayload.class);

        assertThat(response.toEntity().withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(documentWithPossibleOperations.toBuilder()
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build()
                        .withoutDbField());

        verify(readDocumentAggregateUseCase).execute(documentUUID);
        verify(userPermissionValidationUseCase)
                .checkCanDeclineReceivedDocumentsCancellationByCountryCodeOrThrow(REQUEST_USER, document.getCountry());
        verify(declineCancellationReceivedDocumentUseCase).execute(documentAggregate);
    }

    @Test
    public void testApproveReceivedDocument_notFound() throws Exception {

        doThrow(EntityNotFoundException.createNotFound(Document.class,
                UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb")))
                .when(readDocumentAggregateUseCase)
                .execute(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"));

        request(put(BASE_ROUTE + "/4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb/approve"),
                HttpStatus.NOT_FOUND);

        verify(readDocumentAggregateUseCase)
                .execute(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(approveReceivedDocumentUseCase);
    }

    @Test
    public void testApproveReceivedDocument_forbidden() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.KE)
                .reviewed(false)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        when(readDocumentAggregateUseCase.execute(document.getId())).thenReturn(documentAggregate);

        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase)
                .checkCanApprovedReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, document.getCountry());


        request(put(BASE_ROUTE + "/" + documentUUID + "/approve"),
                HttpStatus.FORBIDDEN);

        verify(readDocumentAggregateUseCase).execute(document.getId());
        verify(userPermissionValidationUseCase)
                .checkCanApprovedReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, document.getCountry());
        verifyNoInteractions(approveReceivedDocumentUseCase);
    }

    @Test
    public void testApproveReceivedDocument_success() throws Exception {
        UUID documentUUID = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        Document document = DOCUMENT.toBuilder()
                .id(documentUUID)
                .country(CountryCode.KE)
                .status(DocumentStatus.TAX_PENDING)
                .build();

        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(document)
                .build();

        when(readDocumentAggregateUseCase.execute(documentUUID)).thenReturn(documentAggregate);

        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canDeclineDocumentRejection(false)
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();

        when(approveReceivedDocumentUseCase.execute(documentAggregate))
                .thenReturn(document.toBuilder().status(DocumentStatus.TAX_SUCCESS).build());

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        DocumentApiResponsePayload response = request(
                put(BASE_ROUTE + "/" + documentUUID + "/approve"),
                HttpStatus.OK,
                DocumentApiResponsePayload.class);

        assertThat(response.toEntity().withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(documentWithPossibleOperations.toBuilder()
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build()
                        .withoutDbField());

        verify(readDocumentAggregateUseCase).execute(documentUUID);
        verify(userPermissionValidationUseCase)
                .checkCanApprovedReceivedDocumentsByCountryCodeOrThrow(REQUEST_USER, document.getCountry());
        verify(approveReceivedDocumentUseCase).execute(documentAggregate);
    }

}

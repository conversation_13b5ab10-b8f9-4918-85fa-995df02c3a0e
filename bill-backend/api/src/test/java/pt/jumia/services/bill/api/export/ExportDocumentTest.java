package pt.jumia.services.bill.api.export;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import pt.jumia.services.bill.api.export.entities.CSVExportDocument;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.exceptions.CreateCsvException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.settings.ExportationSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ExportDocumentTest {

    @Mock
    protected DocumentAggregateRepository documentAggregateRepository;

    @Mock
    private ExportationSettings exportationSettings;

    @Mock
    private OverallSettings overallSettings;

    @InjectMocks
    private ExportDocument exportDocument;
    private static final List<CountryCode> ALLOWED_COUNTRIES = List.of(CountryCode.EG, CountryCode.UG);
    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    private static final DocumentFilter DOCUMENT_FILTER = DocumentFilter
            .builder()
            .include(List.of(Document.Details.ISSUER,
                    Document.Details.RECEIVER,
                    Document.Details.TAX_AUTHORITIES_DETAILS))
            .status(DocumentStatus.NEW)
            .referenceNumber("string")
            .currency(Currency.getInstance("EGP"))
            .receiverCountry(CountryCode.EG)
            .countryCodes(ALLOWED_COUNTRIES)
            .build();
    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .document(DOCUMENT)
                    .position(0)
                    .quantity(new BigDecimal(1.00))
                    .unitOfMeasure(UnitOfMeasure.NOT_APPLICABLE)
                    .itemCode("test-product")
                    .itemName("Test product")
                    .itemType(ItemType.PRODUCT)
                    .category(Category.builder()
                            .sid(UUID.randomUUID().toString())
                            .name("Test Category")
                            .taxAuthorityCode("C1")
                            .build())
                    .unitPrice(new BigDecimal(1.00))
                    .totalAmount(new BigDecimal(1.00))
                    .netAmount(new BigDecimal(1.00))
                    .totalTaxAmount(new BigDecimal(1.00))
                    .discount(DocumentLine.Discount.builder()
                            .amount(new BigDecimal(1.00))
                            .rate(new BigDecimal(1.00))
                            .build())
                    .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                            .taxCategory(TaxCategory.VAT_GENERAL)
                            .taxRate(new BigDecimal(1.00))
                            .taxAmount(new BigDecimal(1.00))
                            .build()))
                    .generateId()
                    .build()
            ))
            .documentTransformations(Collections.emptyList())
            .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                    .document(DOCUMENT)
                    .taxCategory(TaxCategory.VAT_GENERAL)
                    .taxRate(new BigDecimal(1.00))
                    .totalAmount(new BigDecimal(1.00))
                    .netAmount(new BigDecimal(1.00))
                    .taxAmount(new BigDecimal(1.00))
                    .generateId()
                    .build()
            ))
            .taxAuthoritiesDetails(null)
            .build();

    @Test
    public void testExecuteDocumentWhenNotEmptyList() throws Exception {
        int fileRowsLimit = 1;
        int exceptionStackTraceLinesLimit = 1;
        List<DocumentAggregate> mockDocumentAggregateList = List.of(DOCUMENT_AGGREGATE);
        List<CSVExportDocument> exportDocumentList = mockDocumentAggregateList.stream()
                .map(document -> CSVExportDocument.fromEntity(document, exceptionStackTraceLinesLimit))
                .collect(Collectors.toList());
        HttpServletResponse response = new MockHttpServletResponse();
        DocumentSortFilters sort = DocumentSortFilters.builder()
                .field(Document.SortingFields.CREATED_AT)
                .direction(OrderDirection.DESC).build();
        PageFilters pageFilters = PageFilters.builder().page(1).size(fileRowsLimit).build();

        mockOverallSettings(fileRowsLimit, exceptionStackTraceLinesLimit);

        when(documentAggregateRepository.findAll(eq(DOCUMENT_FILTER), eq(sort), eq(pageFilters))).thenReturn(mockDocumentAggregateList);

        exportDocument.execute(DOCUMENT_FILTER, response);
        verify(documentAggregateRepository).findAll(eq(DOCUMENT_FILTER), eq(sort), eq(pageFilters));
    }

    @Test
    public void testExecuteDocument_Exception() {
        int fileRowsLimit = 1;
        int exceptionStackTraceLinesLimit = 1;

        HttpServletResponse response = new MockHttpServletResponse();

        mockOverallSettings(fileRowsLimit, exceptionStackTraceLinesLimit);

        when(documentAggregateRepository.findAll(any(), any(), any())).thenReturn(null);

        assertThrows(CreateCsvException.class, () -> exportDocument.execute(DOCUMENT_FILTER, response));

    }

    private void mockOverallSettings(int fileRowsLimit, int exceptionStackTraceLinesLimit) {
        when(overallSettings.getExportationSettings()).thenReturn(exportationSettings);
        when(exportationSettings.getExportationDocumentRowsLimit()).thenReturn(fileRowsLimit);
        when(exportationSettings.getExportationDocumentExceptionTraceLinesLimit()).thenReturn(exceptionStackTraceLinesLimit);
    }

}

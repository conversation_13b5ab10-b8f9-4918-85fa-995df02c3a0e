package pt.jumia.services.bill.api.controllers;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.bill.api.payloads.response.JwtResponsePayload;
import pt.jumia.services.bill.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.bill.domain.usecases.ReadUserAccessUseCase;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@WebMvcTest(AuthController.class)
public class AuthControllerTest extends BaseControllerTest {

    private static final String BASE_PUBLIC_ENDPOINT = "/auth";
    private static final String BASE_ENDPOINT = "/api/auth";

    @MockBean
    private ReadUserAccessUseCase readUserAccessUseCase;

    @Test
    public void swapTempToken() throws Exception {
        String tempToken = "temp-token-to-swap";
        String finalToken = "final-token";
        when(readUserAccessUseCase.findRealToken(tempToken)).thenReturn(finalToken);

        JwtResponsePayload swappedJwtResponsePayload = request(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                tempToken,
                HttpStatus.OK,
                JwtResponsePayload.class);

        verify(readUserAccessUseCase).findRealToken(tempToken);
        assertThat(swappedJwtResponsePayload.getJwt()).isEqualTo(finalToken);
    }

    @Test
    public void swapTempTokenNull() throws Exception {
        CodedErrorResponsePayload errorResponsePayload = request(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class);

        verifyNoInteractions(readUserAccessUseCase);
        assertThat(errorResponsePayload.getDetails()).contains("Required request body is missing");
    }

    @Test
    public void swapTempTokenEmpty() throws Exception {
        CodedErrorResponsePayload errorResponsePayload = request(
                post(BASE_PUBLIC_ENDPOINT + "/swap-token"),
                "",
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class);

        verifyNoInteractions(readUserAccessUseCase);
        assertThat(errorResponsePayload.getDetails()).contains("Required request body is missing");
    }

    @Test
    public void fetchAllPermissions() throws Exception {
        Map<String, Map<String, List<String>>> userPermissions = Map.of(
                "APPLICATION", Map.of(
                        "BootProject", List.of("can_access", "manage_everything")
                )
        );
        when(readUserAccessUseCase.execute(REQUEST_USER)).thenReturn(userPermissions);

        Map<String, Map<String, List<String>>> permissionsResponsePayload = request(
                get(BASE_ENDPOINT + "/user/permissions"),
                HttpStatus.OK,
                Map.class);

        verify(readUserAccessUseCase).execute(REQUEST_USER);
        assertThat(permissionsResponsePayload).isEqualTo(userPermissions);
    }

    @Test
    public void logout() throws Exception {
        request(get(BASE_ENDPOINT + "/logout"), HttpStatus.OK);

        verify(readUserAccessUseCase).logout(REQUEST_USER);
    }
}

package pt.jumia.services.bill.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Captor;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.bill.api.export.ExportDocument;

import pt.jumia.services.bill.api.payloads.request.AddressApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.AppliedTaxApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.CancelRequestPayload;
import pt.jumia.services.bill.api.payloads.request.CategoryApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.DiscountApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.DocumentApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.DocumentIdApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.DocumentLineApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.IssuerApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.ReceiverApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.StatementApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.TaxAuthoritiesDetailsApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.TaxCategoryTotalApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.DocumentAggregateApiResponsePayload;
import pt.jumia.services.bill.api.payloads.response.DocumentApiResponsePayload;
import pt.jumia.services.bill.api.payloads.response.PageResponsePayload;
import pt.jumia.services.bill.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.bill.api.utils.PaginationService;
import pt.jumia.services.bill.domain.RequestContext;

import pt.jumia.services.bill.domain.entities.CancelRequest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.DocumentPossibleOperations;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.dtos.TaxAuthoritiesDetailsUpdateDto;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.usecases.acl.GetAclUserUseCase;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.documenettransformation.UpsertDocumentTransformationUseCase;
import pt.jumia.services.bill.domain.usecases.documentapilogs.UpsertDocumentApiLogUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentResendUseCase;
import pt.jumia.services.bill.domain.usecases.documents.AckDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.documents.CancelDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.documents.CreateDocumentAggregateUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DeclineDocumentRejectionUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentAllowedOperationsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DocumentRetryUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DownloadDocumentFromTaxAuthoritiesUseCase;
import pt.jumia.services.bill.domain.usecases.documents.DownloadDocumentUseCase;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.UpdateTaxAuthoritiesDetailsUseCase;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationException;
import pt.jumia.services.bill.domain.usecases.documents.validators.ValidationError;
import pt.jumia.services.bill.domain.utils.JsonUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.header;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import static pt.jumia.services.bill.api.test.utils.AssertionsHelper.WITHOUT_DB_FIELDS_COMPARATOR;

@WebMvcTest( DocumentController.class )
public class DocumentControllerTest extends BaseControllerTest {

    private static final String BASE_ROUTE = "/api/documents";

    private static final String DOCUMENT_SID = "SID-123";
    private static final String DOCUMENT_ID = "0afc962c-1af4-4d78-9758-595a3744249a";
    private static final List<CountryCode> ALLOWED_COUNTRIES = List.of(CountryCode.EG, CountryCode.UG);

    private static final Document DOCUMENT = Document.builder()
            .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    @MockBean
    private JsonUtils jsonUtils;

    @MockBean
    private CreateDocumentAggregateUseCase createDocumentAggregateUseCase;

    @MockBean
    private ReadDocumentsUseCase readDocumentsUseCase;

    @MockBean
    private ReadDocumentAggregateUseCase readDocumentAggregateUseCase;

    @MockBean
    private UpsertDocumentApiLogUseCase upsertDocumentApiLogUseCase;

    @MockBean
    private PaginationService paginationService;

    @MockBean
    private UserPermissionValidationUseCase userPermissionValidationUseCase;

    @MockBean
    private DownloadDocumentUseCase downloadDocumentUseCase;

    @MockBean
    private DownloadDocumentFromTaxAuthoritiesUseCase downloadDocumentFromTaxAuthoritiesUseCase;

    @MockBean
    private ExportDocument exportDocument;

    @MockBean
    private GetAclUserUseCase getAclUserUseCase;

    @MockBean
    private AckDocumentUseCase ackDocumentUseCase;

    @MockBean
    private CancelDocumentUseCase cancelDocumentUseCase;

    @MockBean
    private DeclineDocumentRejectionUseCase declineDocumentRejectionUseCase;

    @MockBean
    private UpdateTaxAuthoritiesDetailsUseCase updateTaxAuthoritiesDetailsUseCase;

    @MockBean
    private UpsertDocumentTransformationUseCase upsertDocumentTransformationUseCase;

    @MockBean
    private DocumentAllowedOperationsUseCase documentAllowedOperationsUseCase;

    @MockBean
    private DocumentRetryUseCase documentRetryUseCase;

    @MockBean
    private DocumentResendUseCase documentResendUseCase;

    @Captor
    private ArgumentCaptor<TaxAuthoritiesDetailsUpdateDto> taxAuthoritiesUpdatePayloadCaptor;

    @Test
    public void testCreate_invalidPayload_clientError() throws Exception {
        CodedErrorResponsePayload response = request(
                post(BASE_ROUTE),
                new DocumentApiRequestPayload(),
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class
        );

        assertThat(response.getCode()).isEqualTo(ErrorCode.INVALID_PAYLOAD.getCode());
        assertThat(response.getName()).isEqualTo(ErrorCode.INVALID_PAYLOAD.name());
        assertThat(response.getErrors()).isNotEmpty().allSatisfy(error -> {
            assertThat(error.getCode()).isEqualTo(ErrorCode.INVALID_PAYLOAD.getCode());
            assertThat(error.getName()).isEqualTo(ErrorCode.INVALID_PAYLOAD.name());
        });
        verifyNoInteractions(createDocumentAggregateUseCase);
    }

    @Test
    public void testCreate_invalidDocumentPayload_clientError() throws Exception {
        // Prepare
        doThrow(new DocumentValidationException("Global error", List.of(ValidationError.builder()
                .code(ErrorCode.INVALID_ORIGINAL_DOCUMENT)
                .errorMessage("Invalid original invoice")
                .build()))).when(createDocumentAggregateUseCase).execute(any(), any());
        CodedErrorResponsePayload expectedResponse = CodedErrorResponsePayload.forErrorWithCauses(
                ErrorCode.INVALID_DOCUMENT,
                "Global error",
                List.of(CodedErrorResponsePayload.forSingleError(
                        ErrorCode.INVALID_ORIGINAL_DOCUMENT,
                        "Invalid original invoice"
                )));

        // Execute
        CodedErrorResponsePayload response = request(
                post(BASE_ROUTE),
                generateValidPayload(),
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class
        );

        // Verify
        assertThat(response).isEqualTo(expectedResponse);
    }

    @Test
    public void testCreate_validPayload_callsUseCase() throws Exception {
        // Prepare
        DocumentApiRequestPayload documentApiRequestPayload = generateValidPayload();
        DocumentAggregate documentAggregate = documentApiRequestPayload.toEntity();
        when(createDocumentAggregateUseCase.execute(any(DocumentApiLog.class), any(DocumentAggregate.class)))
                .thenReturn(documentAggregate);
        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        when(upsertDocumentApiLogUseCase.execute(any(DocumentApiLog.class)))
                .thenReturn(DocumentApiLog.builder().id(1L).build());

        documentAggregate.setDocument(documentWithPossibleOperations);
        documentAggregate.getLines().stream()
                .forEach(documentLine -> documentLine.setDocument(documentWithPossibleOperations));

        documentAggregate.getTaxCategoryTotals().stream()
                .forEach(taxCategoryTotal -> taxCategoryTotal.setDocument(documentWithPossibleOperations));

        // Execute
        DocumentAggregateApiResponsePayload response = request(
                post(BASE_ROUTE),
                documentApiRequestPayload,
                HttpStatus.CREATED,
                DocumentAggregateApiResponsePayload.class
        );

        // Verify
        assertThat(response.toEntity()).usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR)
                .isEqualTo(documentAggregate);
        verify(createDocumentAggregateUseCase).execute(any(), any());
        verify(userPermissionValidationUseCase).checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
    }

    @Test
    public void testCreateStatement_success() throws Exception {
        // Prepare
        StatementApiRequestPayload documentApiRequestPayload = generateValidStatementPayload();
        DocumentAggregate documentAggregate = documentApiRequestPayload.toEntity();

        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();

        when(createDocumentAggregateUseCase.executeAsStatement(any(), any())).thenReturn(List.of(documentAggregate));

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        documentAggregate.setDocument(documentWithPossibleOperations);
        documentAggregate.getLines().stream()
                .forEach(documentLine -> documentLine.setDocument(documentWithPossibleOperations));

        documentAggregate.getTaxCategoryTotals().stream()
                .forEach(taxCategoryTotal -> taxCategoryTotal.setDocument(documentWithPossibleOperations));

        List<DocumentAggregateApiResponsePayload> response = requestList(
                post(String.format("%s/create/statement", BASE_ROUTE)),
                documentApiRequestPayload,
                HttpStatus.CREATED,
                DocumentAggregateApiResponsePayload.class
        );

        verify(userPermissionValidationUseCase).checkCanResubmitDocumentByCountryOrThrow(REQUEST_USER, CountryCode.NG);
        assertThat(response.size()).isEqualTo(1);
    }

    @Test
    public void testCreateStatement_forbidden() throws Exception {
        doThrow(UserForbiddenException.createCannotAccess())
                .when(userPermissionValidationUseCase)
                .checkCanResubmitDocumentByCountryOrThrow(any(), any());

        StatementApiRequestPayload documentApiRequestPayload = generateValidStatementPayload();

        request(post(String.format("%s/create/statement", BASE_ROUTE)),
                documentApiRequestPayload,
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class
        );
        verify(userPermissionValidationUseCase).checkCanResubmitDocumentByCountryOrThrow(REQUEST_USER, CountryCode.NG);
    }

    @Test
    public void retryDocument_success() throws Exception {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        when(readDocumentsUseCase.readDocumentById(documentId)).thenReturn(DOCUMENT);

        Document documentWithPossibleOperations = DOCUMENT.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(true)
                        .build())
                .build();

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(DOCUMENT))
                .thenReturn(documentWithPossibleOperations);

        request(put(String.format("%s/%s/retry", BASE_ROUTE, documentId)), HttpStatus.OK);

        verify(readDocumentsUseCase).readDocumentById(documentId);
        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                DOCUMENT.getCountry());
        verify(documentRetryUseCase).execute(DOCUMENT);
    }

    @Test
    public void retryDocument_error_documentId_notExist() throws Exception {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        when(readDocumentsUseCase.readDocumentById(documentId))
                .thenThrow(EntityNotFoundException.createNotFound(Document.class, documentId));

        request(put(String.format("%s/%s/retry", BASE_ROUTE, documentId)),
                HttpStatus.NOT_FOUND,
                EntityNotFoundException.class);

        verify(readDocumentsUseCase).readDocumentById(documentId);
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(documentRetryUseCase);
    }

    @Test
    public void retryDocument_forbidden() throws Exception {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        when(readDocumentsUseCase.readDocumentById(documentId)).thenReturn(DOCUMENT);

        doThrow(UserForbiddenException.createDontHavePermission(REQUEST_USER.getUsername(), "auto_retry_errors"))
                .when(userPermissionValidationUseCase)
                .checkCanRetryDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());

        request(put(String.format("%s/%s/retry", BASE_ROUTE, documentId)),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(
                RequestContext.getUser(),
                DOCUMENT.getCountry());
        verifyNoInteractions(documentRetryUseCase);
    }

    @Test
    public void fetchAsPdfBySidTest() throws Exception {
        String attachmentContent = "Once upon a time, there was ...";
        String contentType = "application/pdf";
        when(readDocumentsUseCase.readDocumentBySidAndSuccessStatus(DOCUMENT_SID)).thenReturn(DOCUMENT);
        when(downloadDocumentUseCase.execute(DOCUMENT))
                .thenReturn(DocumentFile.builder()
                        .content(attachmentContent.getBytes())
                        .mediaType(contentType)
                        .build());

        mockMvc
                .perform(get(String.format("%s/sid/%s/pdf", BASE_ROUTE, DOCUMENT_SID)))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", contentType))
                .andExpect(header().longValue("Content-Length", attachmentContent.length()))
                .andReturn();

        verify(readDocumentsUseCase).readDocumentBySidAndSuccessStatus(DOCUMENT_SID);
        verify(downloadDocumentUseCase).execute(DOCUMENT);
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());
    }

    @Test
    public void fetchAsPdfBySidNotFound() throws Exception {
        doThrow(EntityNotFoundException.createNotFoundBySid(DocumentFile.class, DOCUMENT_SID))
                .when(readDocumentsUseCase).readDocumentBySidAndSuccessStatus(DOCUMENT_SID);

        mockMvc
                .perform(get(String.format("%s/sid/%s/pdf", BASE_ROUTE, DOCUMENT_SID)))
                .andExpect(status().isNotFound())
                .andReturn();

        verify(readDocumentsUseCase).readDocumentBySidAndSuccessStatus(DOCUMENT_SID);
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(downloadDocumentUseCase);
    }

    @Test
    public void fetchAsPdfBySidForbiddenTest() throws Exception {
        when(readDocumentsUseCase.readDocumentBySidAndSuccessStatus(DOCUMENT_SID)).thenReturn(DOCUMENT);

        doThrow(UserForbiddenException.createCannotViewDocuments(""))
                .when(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());

        mockMvc
                .perform(get(String.format("%s/sid/%s/pdf", BASE_ROUTE, DOCUMENT_SID)))
                .andExpect(status().isForbidden())
                .andReturn();

        verify(readDocumentsUseCase).readDocumentBySidAndSuccessStatus(DOCUMENT_SID);
        verify(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());
        verifyNoInteractions(downloadDocumentUseCase);
    }

    @Test
    public void fetchAsPdfByIdTest() throws Exception {
        String attachmentContent = "Once upon a time, there was ...";
        String contentType = "application/pdf";

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);
        when(downloadDocumentUseCase.execute(DOCUMENT))
                .thenReturn(DocumentFile.builder()
                        .content(attachmentContent.getBytes())
                        .mediaType(contentType)
                        .build());

        mockMvc
                .perform(get(String.format("%s/%s/pdf", BASE_ROUTE, DOCUMENT_ID)))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", contentType))
                .andExpect(header().longValue("Content-Length", attachmentContent.length()))
                .andReturn();

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(downloadDocumentUseCase).execute(DOCUMENT);
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());
    }

    @Test
    public void fetchAsPdfByIdNotFound() throws Exception {
        doThrow(EntityNotFoundException.createNotFoundBySid(DocumentFile.class, DOCUMENT_ID))
                .when(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));

        mockMvc
                .perform(get(String.format("%s/%s/pdf", BASE_ROUTE, DOCUMENT_ID)))
                .andExpect(status().isNotFound())
                .andReturn();

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(downloadDocumentUseCase);
    }

    @Test
    public void fetchAsPdfByIdWrongUUIDFormatTest() throws Exception {

        mockMvc
                .perform(get(String.format("%s/%s/pdf", BASE_ROUTE, "wrong-uuid")))
                .andExpect(status().isBadRequest())
                .andReturn();

        verifyNoInteractions(readDocumentsUseCase);
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(getAclUserUseCase);
        verifyNoInteractions(downloadDocumentUseCase);

    }

    @Test
    public void fetchAsPdfByIdForbiddenTest() throws Exception {

        String attachmentContent = "Once upon a time, there was ...";
        String contentType = "application/pdf";
        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);
        when(downloadDocumentUseCase.execute(DOCUMENT))
                .thenReturn(DocumentFile.builder()
                        .content(attachmentContent.getBytes())
                        .mediaType(contentType)
                        .build());

        doThrow(UserForbiddenException.createCannotViewDocuments(""))
                .when(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());

        mockMvc
                .perform(get(String.format("%s/%s/pdf", BASE_ROUTE, DOCUMENT_ID)))
                .andExpect(status().isForbidden())
                .andReturn();

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());
        verifyNoInteractions(downloadDocumentUseCase);

    }

    @Test
    public void fetchTaxAuthoritiesPdfByIdTest() throws Exception {
        String attachmentContent = "Once upon a time, there was ...";
        String contentType = "application/pdf";
        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);
        when(downloadDocumentFromTaxAuthoritiesUseCase.execute(DOCUMENT.getId().toString()))
                .thenReturn(DocumentFile.builder()
                        .content(attachmentContent.getBytes())
                        .mediaType(contentType)
                        .build());

        mockMvc
                .perform(get(String.format("%s/%s/tax-authorities-pdf", BASE_ROUTE, DOCUMENT_ID)))
                .andExpect(status().isOk())
                .andExpect(header().string("Content-Type", contentType))
                .andExpect(header().longValue("Content-Length", attachmentContent.length()))
                .andReturn();

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(downloadDocumentFromTaxAuthoritiesUseCase).execute(DOCUMENT.getId().toString());
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());
    }

    @Test
    public void fetchTaxAuthoritiesPdfByIdNotFound() throws Exception {
        doThrow(EntityNotFoundException.createNotFound(DocumentFile.class, UUID.fromString(DOCUMENT_ID)))
                .when(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));

        mockMvc
                .perform(get(String.format("%s/%s/tax-authorities-pdf", BASE_ROUTE, DOCUMENT_ID)))
                .andExpect(status().isNotFound())
                .andReturn();

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(downloadDocumentFromTaxAuthoritiesUseCase);
    }

    @Test
    public void fetchTaxAuthoritiesPdfByIdWrongUUIDFormatTest() throws Exception {

        mockMvc
                .perform(get(String.format("%s/%s/tax-authorities-pdf", BASE_ROUTE, "wrong-uuid")))
                .andExpect(status().isBadRequest())
                .andReturn();

        verifyNoInteractions(readDocumentsUseCase);
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(getAclUserUseCase);
        verifyNoInteractions(downloadDocumentFromTaxAuthoritiesUseCase);

    }

    @Test
    public void fetchTaxAuthoritiesPdfByIdForbiddenTest() throws Exception {
        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        doThrow(UserForbiddenException.createCannotViewDocuments(""))
                .when(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());

        mockMvc
                .perform(get(String.format("%s/%s/tax-authorities-pdf", BASE_ROUTE, DOCUMENT_ID)))
                .andExpect(status().isForbidden())
                .andReturn();

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());
        verifyNoInteractions(downloadDocumentFromTaxAuthoritiesUseCase);

    }

    @Test
    public void fetchAsCsvTest() throws Exception {
        mockMvc
                .perform(get(String.format("%s/export/csv", BASE_ROUTE)))
                .andExpect(status().isOk())
                .andReturn();

        verify(exportDocument).execute(any(), any());
        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(REQUEST_USER);
    }

    @Test
    public void fetchAsCsvForbiddenTest() throws Exception {
        doThrow(UserForbiddenException.createCannotViewDocuments(""))
                .when(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(REQUEST_USER);

        mockMvc
                .perform(get(String.format("%s/export/csv", BASE_ROUTE)))
                .andExpect(status().isForbidden())
                .andReturn();

        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(REQUEST_USER);
        verifyNoInteractions(getAclUserUseCase);
        verifyNoInteractions(exportDocument);

    }

    @Test
    public void readDocumentBySid() throws Exception {
        String documentSid = "some-good-old-fake-sid";
        DocumentAggregate documentAggregate = generateValidPayload().toEntity();
        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        when(readDocumentAggregateUseCase.executeBySidAndSuccessStatus(documentSid)).thenReturn(documentAggregate);

        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();
        documentAggregate.getLines().stream()
                .forEach(documentLine -> documentLine.setDocument(documentWithPossibleOperations));

        documentAggregate.getTaxCategoryTotals().stream()
                .forEach(taxCategoryTotal -> taxCategoryTotal.setDocument(documentWithPossibleOperations));

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        DocumentAggregateApiResponsePayload response = request(
                get(String.format("%s/sid/%s", BASE_ROUTE, documentSid)),
                HttpStatus.OK,
                DocumentAggregateApiResponsePayload.class
        );

        assertThat(response.toEntity())
                .usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR)
                .isEqualTo(documentAggregate);
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                documentAggregate.getDocument().getCountry());
        verify(documentAllowedOperationsUseCase).appendDocumentPossibleOperations(documentWithoutPossibleOperations);
        verify(readDocumentAggregateUseCase).executeBySidAndSuccessStatus(documentSid);
    }

    @Test
    public void readDocumentBySid_notFound() throws Exception {
        String documentSid = "some-good-old-fake-sid";
        when(readDocumentAggregateUseCase.executeBySidAndSuccessStatus(documentSid))
                .thenThrow(EntityNotFoundException.createNotFoundBySid(Document.class, documentSid));

        CodedErrorResponsePayload response = request(
                get(String.format("%s/sid/%s", BASE_ROUTE, documentSid)),
                HttpStatus.NOT_FOUND,
                CodedErrorResponsePayload.class);

        String expectedErrorMessage = Document.class.getName() + " with sid " + documentSid + " not found";
        CodedErrorResponsePayload expectedResponse = CodedErrorResponsePayload
                .forSingleError(ErrorCode.ENTITY_NOT_FOUND, expectedErrorMessage);
        assertThat(response).isEqualTo(expectedResponse);

        verify(readDocumentAggregateUseCase).executeBySidAndSuccessStatus(documentSid);
        verifyNoInteractions(userPermissionValidationUseCase);

    }

    @Test
    public void readDocumentBySid_forbidden() throws Exception {
        String documentSid = "some-good-old-fake-sid";
        DocumentAggregate documentAggregate = generateValidPayload().toEntity();
        when(readDocumentAggregateUseCase.executeBySidAndSuccessStatus(documentSid)).thenReturn(documentAggregate);

        doThrow(UserForbiddenException.createCannotAccess()).when(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                        documentAggregate.getDocument().getCountry());

        CodedErrorResponsePayload response = request(
                get(String.format("%s/sid/%s", BASE_ROUTE, documentSid)),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        String expectedErrorMessage = "User does not have permission to access Bill";
        CodedErrorResponsePayload expectedResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.FORBIDDEN, expectedErrorMessage);
        assertThat(response).isEqualTo(expectedResponse);

        verify(readDocumentAggregateUseCase).executeBySidAndSuccessStatus(documentSid);
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                documentAggregate.getDocument().getCountry());
    }


    @Test
    public void readDocumentById() throws Exception {
        String documentId = "68015c5a-e809-41d1-926b-a25db4c98c14";
        DocumentAggregate documentAggregate = generateValidPayload().toEntity();
        Document documentWithoutPossibleOperations = documentAggregate.getDocument();
        List<DocumentTransformation> documentTransformations = new ArrayList<>();
        DocumentTransformation documentTransformation = DocumentTransformation.builder()
                .originalValue("oldValue")
                .newValue("newValue").build();
        documentTransformations.add(documentTransformation);
        documentAggregate.setDocumentTransformations(documentTransformations);
        when(readDocumentAggregateUseCase.execute(UUID.fromString(documentId))).thenReturn(documentAggregate);

        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canCancel(false)
                        .canAcknowledge(true)
                        .build())
                .build();
        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(documentWithoutPossibleOperations))
                .thenReturn(documentWithPossibleOperations);
        documentAggregate.getLines().stream()
                .forEach(documentLine -> documentLine.setDocument(documentWithPossibleOperations));

        documentAggregate.getDocumentTransformations().stream()
                .forEach(documentTransformationAg -> documentTransformation.setDocument(documentWithPossibleOperations));

        documentAggregate.getTaxCategoryTotals().stream()
                .forEach(taxCategoryTotal -> taxCategoryTotal.setDocument(documentWithPossibleOperations));


        DocumentAggregateApiResponsePayload response = request(
                get(String.format("%s/%s", BASE_ROUTE, documentId)),
                HttpStatus.OK,
                DocumentAggregateApiResponsePayload.class
        );

        assertThat(response.toEntity())
                .usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR)
                .isEqualTo(documentAggregate);
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                documentAggregate.getDocument().getCountry());
        verify(documentAllowedOperationsUseCase).appendDocumentPossibleOperations(documentWithoutPossibleOperations);
        verify(readDocumentAggregateUseCase).execute(UUID.fromString(documentId));
    }

    @Test
    public void readDocumentById_notFound() throws Exception {
        String documentId = "68015c5a-e809-41d1-926b-a25db4c98c14";
        when(readDocumentAggregateUseCase.execute(UUID.fromString(documentId)))
                .thenThrow(EntityNotFoundException.createNotFound(Document.class, UUID.fromString(documentId)));

        CodedErrorResponsePayload response = request(
                get(String.format("%s/%s", BASE_ROUTE, documentId)),
                HttpStatus.NOT_FOUND,
                CodedErrorResponsePayload.class);

        String expectedErrorMessage = Document.class.getName() + " with id " + documentId + " not found";
        CodedErrorResponsePayload expectedResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.ENTITY_NOT_FOUND, expectedErrorMessage);
        assertThat(response).isEqualTo(expectedResponse);

        verify(readDocumentAggregateUseCase).execute(UUID.fromString(documentId));
        verifyNoInteractions(userPermissionValidationUseCase);
    }

    @Test
    public void readDocumentById_WrongUUID() throws Exception {
        String documentId = "wrong-uuid";

        request(
                get(String.format("%s/%s", BASE_ROUTE, documentId)),
                HttpStatus.BAD_REQUEST);

        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(readDocumentAggregateUseCase);
    }

    @Test
    public void readDocumentById_forbidden() throws Exception {
        String documentId = "68015c5a-e809-41d1-926b-a25db4c98c14";
        DocumentAggregate documentAggregate = generateValidPayload().toEntity();
        when(readDocumentAggregateUseCase.execute(UUID.fromString(documentId))).thenReturn(documentAggregate);
        doThrow(UserForbiddenException.createCannotAccess()).when(userPermissionValidationUseCase)
                .checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                        documentAggregate.getDocument().getCountry());

        CodedErrorResponsePayload response = request(
                get(String.format("%s/%s", BASE_ROUTE, documentId)),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        String expectedErrorMessage = "User does not have permission to access Bill";
        CodedErrorResponsePayload expectedResponse = CodedErrorResponsePayload.forSingleError(ErrorCode.FORBIDDEN, expectedErrorMessage);
        assertThat(response).isEqualTo(expectedResponse);
        verify(readDocumentAggregateUseCase).execute(UUID.fromString(documentId));
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                documentAggregate.getDocument().getCountry());
    }

    @Test
    public void updateTaxAuthoritiesDetailsSuccessfulDocument() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/tax-details";
        TaxAuthoritiesDetailsApiRequestPayload requestPayload = generateTaxAuthoritiesDetailsRequestPayload();

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        request(post(route), requestPayload, HttpStatus.OK);

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase).checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verify(updateTaxAuthoritiesDetailsUseCase).execute(taxAuthoritiesUpdatePayloadCaptor.capture());
        assertThat(taxAuthoritiesUpdatePayloadCaptor.getValue().getDocumentStatus())
                .isEqualTo(DocumentStatus.TAX_SUCCESS);
    }

    @Test
    public void updateTaxAuthoritiesDetailsDocumentInError() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/tax-details";
        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID))).thenReturn(DOCUMENT);

        TaxAuthoritiesDetailsApiRequestPayload requestPayload = generateTaxAuthoritiesDetailsRequestPayload();
        requestPayload.setApiLogStatus(String.valueOf(TaxAuthoritiesDetailsApiRequestPayload.ApiLogStatus.FAILED_MAPPING));

        request(post(route), requestPayload, HttpStatus.OK);

        verify(userPermissionValidationUseCase).checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verify(updateTaxAuthoritiesDetailsUseCase).execute(taxAuthoritiesUpdatePayloadCaptor.capture());
        assertThat(taxAuthoritiesUpdatePayloadCaptor.getValue().getDocumentStatus())
                .isEqualTo(DocumentStatus.TAXI_FAILED_MAPPING);
    }

    @Test
    public void updateTaxAuthoritiesDetails_documentNotFount() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/tax-details";
        TaxAuthoritiesDetailsApiRequestPayload requestPayload = generateTaxAuthoritiesDetailsRequestPayload();
        doThrow(EntityNotFoundException.createNotFound(Document.class, UUID.fromString(DOCUMENT_ID)))
                .when(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));


        request(post(route), requestPayload, HttpStatus.NOT_FOUND);

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(updateTaxAuthoritiesDetailsUseCase);
    }

    @Test
    public void updateTaxAuthoritiesDetails_requiredDocumentStatus() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/tax-details";
        TaxAuthoritiesDetailsApiRequestPayload requestPayload = generateTaxAuthoritiesDetailsRequestPayload()
                .toBuilder().apiLogStatus(null).build();

        request(post(route), requestPayload, HttpStatus.BAD_REQUEST);

        verifyNoInteractions(userPermissionValidationUseCase, updateTaxAuthoritiesDetailsUseCase);
    }

    @Test
    public void updateTaxAuthoritiesDetails_unauthorized() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/tax-details";
        TaxAuthoritiesDetailsApiRequestPayload requestPayload = generateTaxAuthoritiesDetailsRequestPayload();
        doThrow(UserForbiddenException.createCannotAccess())
                .when(userPermissionValidationUseCase).checkCanManageDocumentsByCountryCodeOrThrow(any(), any());

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        request(post(route), requestPayload, HttpStatus.FORBIDDEN);

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase).checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verifyNoInteractions(updateTaxAuthoritiesDetailsUseCase);
    }

    @Test
    public void testList_validRequest_noParams() throws Exception {
        DocumentApiRequestPayload document = generateValidPayload();

        Document documentWithoutPossibleOperations = document.toEntity().getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations
                .toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .build())
                .build();
        List<Document> documents = List.of(documentWithoutPossibleOperations);

        when(readDocumentsUseCase.execute(any(), any(), any()))
                .thenReturn(documents);

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(documentWithoutPossibleOperations))
                .thenReturn(documentWithPossibleOperations);

        when(paginationService.buildPageResponsePayload(
                any(), any(),
                ArgumentMatchers.<DocumentApiResponsePayload>anyList()))
                .thenReturn(new PageResponsePayload<>(
                        null, 1, 10,
                        List.of(documentWithPossibleOperations)
                                .stream()
                                .map(DocumentApiResponsePayload::new)
                                .collect(Collectors.toList())));

        PageResponsePayload<DocumentApiResponsePayload> response = requestPage(
                get(BASE_ROUTE),
                HttpStatus.OK,
                DocumentApiResponsePayload.class
        );
        verify(readDocumentsUseCase).execute(any(), any(), any());
        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(any());
    }

    @Test
    public void testList_validRequest_withParamsSingleCountry() throws Exception {
        DocumentApiRequestPayload document = generateValidPayload();

        Document documentWithoutPossibleOperations = document.toEntity().getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations
                .toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .build())
                .build();
        List<Document> documents = List.of(documentWithoutPossibleOperations);

        when(readDocumentsUseCase.execute(any(), any(), any()))
                .thenReturn(documents);

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(documentWithoutPossibleOperations))
                .thenReturn(documentWithPossibleOperations);

        when(paginationService.buildPageResponsePayload(
                any(), any(),
                ArgumentMatchers.<DocumentApiResponsePayload>anyList()))
                .thenReturn(new PageResponsePayload<>(
                        null, 1, 10,
                        List.of(documentWithPossibleOperations)
                                .stream()
                                .map(DocumentApiResponsePayload::new)
                                .collect(Collectors.toList())));

        PageResponsePayload<DocumentApiResponsePayload> response = requestPage(
                get(BASE_ROUTE + "?status=NEW&referenceNumber=string&countryCode=EG"),
                HttpStatus.OK,
                DocumentApiResponsePayload.class
        );
        verify(readDocumentsUseCase).execute(any(), any(), any());
        verify(userPermissionValidationUseCase).checkCanViewDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                CountryCode.EG);
    }

    @Test
    public void testList_validRequest_withParamsAndAllowedCountries() throws Exception {
        DocumentApiRequestPayload document = generateValidPayload();

        Document documentWithoutPossibleOperations = document.toEntity().getDocument();
        Document documentWithPossibleOperations = documentWithoutPossibleOperations
                .toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .build())
                .build();
        List<Document> documents = List.of(documentWithoutPossibleOperations);

        when(userPermissionValidationUseCase.getCountriesCanViewDocumentsOrThrow(REQUEST_USER))
                .thenReturn(ALLOWED_COUNTRIES);

        when(readDocumentsUseCase.execute(eq(DocumentFilter
                .builder()
                .status(DocumentStatus.NEW)
                .referenceNumber("string")
                .currency(Currency.getInstance("EGP"))
                .receiverCountry(CountryCode.EG)
                .countryCodes(ALLOWED_COUNTRIES)
                .build()), any(), any()))
                .thenReturn(documents);

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(documentWithoutPossibleOperations))
                .thenReturn(documentWithPossibleOperations);

        when(paginationService.buildPageResponsePayload(
                any(), any(),
                ArgumentMatchers.<DocumentApiResponsePayload>anyList()))
                .thenReturn(new PageResponsePayload<>(
                        null, 1, 10,
                        List.of(documentWithPossibleOperations)
                                .stream()
                                .map(DocumentApiResponsePayload::new)
                                .collect(Collectors.toList())));

        PageResponsePayload<DocumentApiResponsePayload> response = requestPage(
                get(BASE_ROUTE + "?status=NEW&referenceNumber=string&currency=EGP&receiverCountry=EG"),
                HttpStatus.OK,
                DocumentApiResponsePayload.class
        );
        verify(readDocumentsUseCase).execute(any(), any(), any());
        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(RequestContext.getUser());
    }

    @Test
    public void testList_invalidRequest_wrongParams() throws Exception {

        request(
                get(BASE_ROUTE + "?status=NEWZ&referenceNumber=string&countryCode=ECCG"),
                HttpStatus.BAD_REQUEST
        );
        verifyNoInteractions(paginationService);
        verifyNoInteractions(readDocumentsUseCase);
    }

    @Test
    public void testList_invalidRequest_noPermission() throws Exception {

        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(REQUEST_USER);

        request(
                get(BASE_ROUTE),
                HttpStatus.FORBIDDEN
        );
        verifyNoInteractions(paginationService);
        verifyNoInteractions(readDocumentsUseCase);
        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(any());
    }

    @Test
    public void acknowledgeDocument_success() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/acknowledge";

        Document documentWithoutPossibleOperations = DOCUMENT.toBuilder()
                .status(DocumentStatus.TAX_ERROR_ACKED).build();

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);
        when(ackDocumentUseCase.execute(any(Document.class))).thenReturn(documentWithoutPossibleOperations);
        Document documentWithPossibleOperations = documentWithoutPossibleOperations.toBuilder()
                .documentPossibleOperations(
                        DocumentPossibleOperations
                                .builder()
                                .canApprove(false)
                                .canAcknowledge(false)
                                .canCancel(true)
                                .build()
                )
                .build();
        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(documentWithoutPossibleOperations))
                .thenReturn(documentWithPossibleOperations);
        DocumentApiResponsePayload response = request(post(route), HttpStatus.OK, DocumentApiResponsePayload.class);

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        assertThat(response.toEntity()).isEqualTo(documentWithPossibleOperations);
        verify(documentAllowedOperationsUseCase).appendDocumentPossibleOperations(documentWithoutPossibleOperations);
        verify(ackDocumentUseCase).execute(any(Document.class));
    }

    @Test
    public void acknowledgeDocument_forbidden() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/acknowledge";
        doThrow(UserForbiddenException.createCannotAccess())
                .when(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(any(), any());

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        request(post(route), null, HttpStatus.FORBIDDEN);

        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verifyNoInteractions(ackDocumentUseCase);
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void acknowledgeDocument_notfound() throws Exception {
        doThrow(EntityNotFoundException.createNotFound(Document.class, UUID.fromString(DOCUMENT_ID)))
                .when(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));

        CodedErrorResponsePayload codedErrorResponse = request(post(String.format("%s/%s/acknowledge", BASE_ROUTE, DOCUMENT_ID)),
                HttpStatus.NOT_FOUND,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.ENTITY_NOT_FOUND.getCode());
        assertThat(codedErrorResponse.getDetails()).isEqualTo(String.format("%s with id %s not found", Document.class.getName(), DOCUMENT_ID));
        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(ackDocumentUseCase);
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void acknowledgeDocument_conflict() throws Exception {
        doThrow(ConflictOperationException.createInvalidAcknowledgeStatus(DocumentStatus.NEW))
                .when(ackDocumentUseCase).execute(any(Document.class));
        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        CodedErrorResponsePayload codedErrorResponse = request(post(String.format("%s/%s/acknowledge", BASE_ROUTE, DOCUMENT_ID)),
                HttpStatus.CONFLICT,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.INVALID_DOCUMENT_STATUS_ACK.getCode());
        assertThat(codedErrorResponse.getName()).isEqualTo(ErrorCode.INVALID_DOCUMENT_STATUS_ACK.name());
        assertThat(codedErrorResponse.getDetails()).isEqualTo("Only error statuses can be acknowledge (got 'NEW').");
        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verify(ackDocumentUseCase).execute(any(Document.class));
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void acknowledgeDocument_taxiException() throws Exception {
        doThrow(TaxiNetworkException.buildFailedRequest("", new IOException("")))
                .when(ackDocumentUseCase).execute(any(Document.class));
        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        CodedErrorResponsePayload codedErrorResponse = request(post(String.format("%s/%s/acknowledge", BASE_ROUTE, DOCUMENT_ID)),
                HttpStatus.INTERNAL_SERVER_ERROR,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.TAXI_REQUEST_FAILED.getCode());
        assertThat(codedErrorResponse.getName()).isEqualTo(ErrorCode.TAXI_REQUEST_FAILED.name());
        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verify(ackDocumentUseCase).execute(any(Document.class));
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void testCount_validRequest_noParams() throws Exception {

        when(readDocumentsUseCase.executeCountAll(any()))
                .thenReturn(5L);

        request(
                get(BASE_ROUTE + "/count"),
                HttpStatus.OK
        );
        verify(readDocumentsUseCase).executeCountAll(any());
        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(any());
    }

    @Test
    public void testCount_invalidRequest_wrongParams() throws Exception {

        request(
                get(BASE_ROUTE + "/count?status=NEWZ&referenceNumber=string&countryCode=ECCG"),
                HttpStatus.BAD_REQUEST
        );
        verifyNoInteractions(readDocumentsUseCase);
    }

    @Test
    public void cancelDocument_success() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/cancel";

        Document document = DOCUMENT.toBuilder()
                .status(DocumentStatus.TAX_SUCCESS).build();

        Document documentWithPossibleOperations = document.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canApprove(false)
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT.toBuilder()
                        .id(UUID.fromString(DOCUMENT_ID))
                        .build());
        when(cancelDocumentUseCase.execute(any(Document.class), any(CancelRequest.class))).thenReturn(document);

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        DocumentApiResponsePayload response = request(post(route),
                CancelRequestPayload.builder().cancellationReason("cancel reason").build(),
                HttpStatus.OK,
                DocumentApiResponsePayload.class);

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase).checkCanCancelDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        assertThat(response.toEntity()).isEqualTo(documentWithPossibleOperations);
        verify(cancelDocumentUseCase).execute(DOCUMENT.toBuilder()
                        .id(UUID.fromString(DOCUMENT_ID))
                        .build(),
                CancelRequest.builder()
                        .documentId(UUID.fromString(DOCUMENT_ID))
                        .cancellationReason("cancel reason")
                        .build());
        verify(documentAllowedOperationsUseCase).appendDocumentPossibleOperations(any(Document.class));
    }

    @Test
    public void cancelDocument_invalidPayload() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/cancel";

        request(post(route),
                CancelRequestPayload.builder().build(),
                HttpStatus.BAD_REQUEST);

        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(cancelDocumentUseCase);
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void cancelDocument_forbidden() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/cancel";
        doThrow(UserForbiddenException.createCannotAccess())
                .when(userPermissionValidationUseCase).checkCanCancelDocumentByCountryCodeOrThrow(any(), any());

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        request(post(route),
                CancelRequestPayload.builder().cancellationReason("cancel reason").build(),
                HttpStatus.FORBIDDEN);

        verify(userPermissionValidationUseCase).checkCanCancelDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verifyNoInteractions(cancelDocumentUseCase);
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void cancelDocument_notfound() throws Exception {
        doThrow(EntityNotFoundException.createNotFound(Document.class, UUID.fromString(DOCUMENT_ID)))
                .when(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));

        CodedErrorResponsePayload codedErrorResponse = request(post(String.format("%s/%s/cancel", BASE_ROUTE, DOCUMENT_ID)),
                CancelRequestPayload.builder().cancellationReason("cancel reason").build(),
                HttpStatus.NOT_FOUND,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.ENTITY_NOT_FOUND.getCode());
        assertThat(codedErrorResponse.getDetails()).isEqualTo(String.format("%s with id %s not found", Document.class.getName(), DOCUMENT_ID));
        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(cancelDocumentUseCase);
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }


    @Test
    public void declineDocumentRejection_success() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/decline-rejection";

        Document document = DOCUMENT.toBuilder()
                .status(DocumentStatus.TAX_SUCCESS).build();

        Document documentWithPossibleOperations = document.toBuilder()
                .documentPossibleOperations(DocumentPossibleOperations.builder()
                        .canApprove(false)
                        .canCancel(false)
                        .canAcknowledge(false)
                        .build())
                .build();

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT.toBuilder()
                        .id(UUID.fromString(DOCUMENT_ID))
                        .build());
        when(declineDocumentRejectionUseCase.execute(any(Document.class))).thenReturn(document);

        when(documentAllowedOperationsUseCase.appendDocumentPossibleOperations(any(Document.class)))
                .thenReturn(documentWithPossibleOperations);

        DocumentApiResponsePayload response = request(post(route), HttpStatus.OK, DocumentApiResponsePayload.class);

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase)
                .checkCanDeclineDocumentRejectionByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        assertThat(response.toEntity()).isEqualTo(documentWithPossibleOperations);
        verify(declineDocumentRejectionUseCase).execute(any(Document.class));
        verify(documentAllowedOperationsUseCase)
                .appendDocumentPossibleOperations(any(Document.class));
    }


    @Test
    public void declineDocumentRejection_forbidden() throws Exception {
        String route = BASE_ROUTE + "/" + DOCUMENT_ID + "/decline-rejection";
        doThrow(UserForbiddenException.createCannotAccess())
                .when(userPermissionValidationUseCase)
                .checkCanDeclineDocumentRejectionByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);

        when(readDocumentsUseCase.readDocumentById(UUID.fromString(DOCUMENT_ID)))
                .thenReturn(DOCUMENT);

        request(post(route), null, HttpStatus.FORBIDDEN);

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verify(userPermissionValidationUseCase)
                .checkCanDeclineDocumentRejectionByCountryCodeOrThrow(REQUEST_USER, CountryCode.NG);
        verifyNoInteractions(declineDocumentRejectionUseCase);
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void declineDocumentRejection_notfound() throws Exception {
        doThrow(EntityNotFoundException.createNotFound(Document.class, UUID.fromString(DOCUMENT_ID)))
                .when(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));

        CodedErrorResponsePayload codedErrorResponse = request(post(String.format("%s/%s/decline-rejection", BASE_ROUTE, DOCUMENT_ID)),
                HttpStatus.NOT_FOUND,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.ENTITY_NOT_FOUND.getCode());
        assertThat(codedErrorResponse.getDetails()).isEqualTo(String.format("%s with id %s not found",
                Document.class.getName(), DOCUMENT_ID));

        verify(readDocumentsUseCase).readDocumentById(UUID.fromString(DOCUMENT_ID));
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(declineDocumentRejectionUseCase);
        verifyNoInteractions(documentAllowedOperationsUseCase);
    }

    @Test
    public void resendDocument_success() throws Exception {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        when(readDocumentsUseCase.readDocumentById(documentId)).thenReturn(DOCUMENT);

        request(put(String.format("%s/%s/resend", BASE_ROUTE, documentId)), HttpStatus.OK);

        verify(readDocumentsUseCase).readDocumentById(documentId);
        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(RequestContext.getUser(),
                DOCUMENT.getCountry());
        verify(documentResendUseCase).execute(DOCUMENT);
    }

    @Test
    public void resendDocument_error_documentId_notExist() throws Exception {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        when(readDocumentsUseCase.readDocumentById(documentId))
                .thenThrow(EntityNotFoundException.createNotFound(Document.class, documentId));

        request(put(String.format("%s/%s/resend", BASE_ROUTE, documentId)),
                HttpStatus.NOT_FOUND,
                EntityNotFoundException.class);

        verify(readDocumentsUseCase).readDocumentById(documentId);
        verifyNoInteractions(userPermissionValidationUseCase);
        verifyNoInteractions(documentResendUseCase);
    }

    @Test
    public void resendDocument_forbidden() throws Exception {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        when(readDocumentsUseCase.readDocumentById(documentId)).thenReturn(DOCUMENT);

        doThrow(UserForbiddenException.createDontHavePermission(REQUEST_USER.getUsername(), "auto_retry_errors"))
                .when(userPermissionValidationUseCase)
                .checkCanRetryDocumentsByCountryCodeOrThrow(REQUEST_USER, DOCUMENT.getCountry());

        request(put(String.format("%s/%s/resend", BASE_ROUTE, documentId)),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        verifyNoInteractions(documentResendUseCase);
    }

    private DocumentApiRequestPayload generateValidPayload() {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        return DocumentApiRequestPayload.builder()
                .country("NG")
                .shop("jumia")
                .type("SALES_INVOICE")
                .sid("sid")
                .flow("RETAIL")
                .generatedBy("NAV")
                .referenceNumber("referenceNumber")
                .issuedDate(now)
                .currency("NGN")
                .receiver(ReceiverApiRequestPayload.builder()
                        .type("CUSTOMER")
                        .legalName("legalName")
                        .name("name")
                        .nationalIdentificationNumber("nationalIdentificationNumber")
                        .taxIdentificationNumber("taxIdentificationNumber")
                        .businessRegistrationNumber("businessRegistrationNumber")
                        .address(AddressApiRequestPayload.builder()
                                .country("NG")
                                .region("region")
                                .city("city")
                                .street("street")
                                .buildingNumber("buildingNumber")
                                .floor("floor")
                                .postalCode("postalCode")
                                .additionalInformation("additionalInformation")
                                .build())
                        .email("email")
                        .mobilePhone("mobilePhone")
                        .linePhone("linePhone")
                        .build())
                .issuer(IssuerApiRequestPayload.builder()
                        .type("BUSINESS")
                        .legalName("legalName")
                        .name("name")
                        .taxIdentificationNumber("taxIdentificationNumber")
                        .businessRegistrationNumber("businessRegistrationNumber")
                        .branch("branch")
                        .address(AddressApiRequestPayload.builder()
                                .country("NG")
                                .region("region")
                                .city("city")
                                .street("street")
                                .buildingNumber("buildingNumber")
                                .floor("floor")
                                .postalCode("postalCode")
                                .additionalInformation("additionalInformation")
                                .build())
                        .email("email")
                        .mobilePhone("mobilePhone")
                        .linePhone("linePhone")
                        .build())
                .originalDocument(DocumentIdApiRequestPayload.builder()
                        .sid("sid")
                        .build())
                .notes("notes")
                .lines(List.of(DocumentLineApiRequestPayload.builder()
                        .position(0)
                        .quantity(new BigDecimal("1.0"))
                        .unitOfMeasure("NOT_APPLICABLE")
                        .itemCode("itemCode")
                        .itemName("itemName")
                        .itemType("PRODUCT")
                        .category(CategoryApiRequestPayload.builder()
                                .sid("sid")
                                .name("name")
                                .taxAuthorityCode("taxAuthorityCode")
                                .build())
                        .unitPrice(new BigDecimal("2.0"))
                        .totalAmount(new BigDecimal("3.0"))
                        .netAmount(new BigDecimal("4.0"))
                        .totalTaxAmount(new BigDecimal("5.0"))
                        .appliedTaxes(List.of(AppliedTaxApiRequestPayload.builder()
                                .taxCategory("VAT_GENERAL")
                                .taxRate(new BigDecimal("0.6"))
                                .taxFixedAmount(new BigDecimal("7.0"))
                                .taxAmount(new BigDecimal("8.0"))
                                .build()))
                        .discount(DiscountApiRequestPayload.builder()
                                .amount(new BigDecimal("9.0"))
                                .rate(new BigDecimal("0.10"))
                                .build())
                        .build()))
                .lineCount(1)
                .totalAmount(new BigDecimal("11.0"))
                .netAmount(new BigDecimal("12.0"))
                .taxAmount(new BigDecimal("13.0"))
                .discountAmount(new BigDecimal("14.0"))
                .taxCategoryTotals(List.of(TaxCategoryTotalApiRequestPayload.builder()
                        .taxCategory("VAT_GENERAL")
                        .taxRate(new BigDecimal("0.14"))
                        .taxFixedAmount(new BigDecimal("15.0"))
                        .totalAmount(new BigDecimal("16.0"))
                        .netAmount(new BigDecimal("17.0"))
                        .taxAmount(new BigDecimal("18.0"))
                        .build()))
                .build();
    }

    private StatementApiRequestPayload generateValidStatementPayload() {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);
        return StatementApiRequestPayload.builder()
                .country("NG")
                .shop("jumia")
                .type("SALES_INVOICE")
                .sid("sid")
                .flow("RETAIL")
                .generatedBy("NAV")
                .referenceNumber("referenceNumber")
                .issuedDate(now)
                .currency("NGN")
                .receiver(ReceiverApiRequestPayload.builder()
                        .type("CUSTOMER")
                        .legalName("legalName")
                        .name("name")
                        .nationalIdentificationNumber("nationalIdentificationNumber")
                        .taxIdentificationNumber("taxIdentificationNumber")
                        .businessRegistrationNumber("businessRegistrationNumber")
                        .address(AddressApiRequestPayload.builder()
                                .country("NG")
                                .region("region")
                                .city("city")
                                .street("street")
                                .buildingNumber("buildingNumber")
                                .floor("floor")
                                .postalCode("postalCode")
                                .additionalInformation("additionalInformation")
                                .build())
                        .email("email")
                        .mobilePhone("mobilePhone")
                        .linePhone("linePhone")
                        .build())
                .issuer(IssuerApiRequestPayload.builder()
                        .type("BUSINESS")
                        .legalName("legalName")
                        .name("name")
                        .taxIdentificationNumber("taxIdentificationNumber")
                        .businessRegistrationNumber("businessRegistrationNumber")
                        .branch("branch")
                        .address(AddressApiRequestPayload.builder()
                                .country("NG")
                                .region("region")
                                .city("city")
                                .street("street")
                                .buildingNumber("buildingNumber")
                                .floor("floor")
                                .postalCode("postalCode")
                                .additionalInformation("additionalInformation")
                                .build())
                        .email("email")
                        .mobilePhone("mobilePhone")
                        .linePhone("linePhone")
                        .build())
                .originalDocument(DocumentIdApiRequestPayload.builder()
                        .sid("sid")
                        .build())
                .notes("notes")
                .lines(List.of(DocumentLineApiRequestPayload.builder()
                        .position(0)
                        .quantity(new BigDecimal("1.0"))
                        .unitOfMeasure("NOT_APPLICABLE")
                        .itemCode("itemCode")
                        .itemName("itemName")
                        .itemType("PRODUCT")
                        .category(CategoryApiRequestPayload.builder()
                                .sid("sid")
                                .name("name")
                                .taxAuthorityCode("taxAuthorityCode")
                                .build())
                        .unitPrice(new BigDecimal("2.0"))
                        .totalAmount(new BigDecimal("3.0"))
                        .netAmount(new BigDecimal("4.0"))
                        .totalTaxAmount(new BigDecimal("5.0"))
                        .appliedTaxes(List.of(AppliedTaxApiRequestPayload.builder()
                                .taxCategory("VAT_GENERAL")
                                .taxRate(new BigDecimal("0.6"))
                                .taxFixedAmount(new BigDecimal("7.0"))
                                .taxAmount(new BigDecimal("8.0"))
                                .build()))
                        .discount(DiscountApiRequestPayload.builder()
                                .amount(new BigDecimal("9.0"))
                                .rate(new BigDecimal("0.10"))
                                .build())
                        .build()))
                .lineCount(1)
                .totalAmount(new BigDecimal("11.0"))
                .netAmount(new BigDecimal("12.0"))
                .taxAmount(new BigDecimal("13.0"))
                .discountAmount(new BigDecimal("14.0"))
                .taxCategoryTotals(List.of(TaxCategoryTotalApiRequestPayload.builder()
                        .taxCategory("VAT_GENERAL")
                        .taxRate(new BigDecimal("0.14"))
                        .taxFixedAmount(new BigDecimal("15.0"))
                        .totalAmount(new BigDecimal("16.0"))
                        .netAmount(new BigDecimal("17.0"))
                        .taxAmount(new BigDecimal("18.0"))
                        .build()))
                .build();
    }

    private TaxAuthoritiesDetailsApiRequestPayload generateTaxAuthoritiesDetailsRequestPayload() {
        return TaxAuthoritiesDetailsApiRequestPayload.builder()
                .submissionId("Fake submission id")
                .taxDocumentNumber("Take tax document number")
                .qrCode("YADA")
                .verificationCode("BLA")
                .deviceNumber("*********")
                .statusCode("200")
                .errorCode("45")
                .exception(null)
                .apiLogStatus(TaxAuthoritiesDetailsApiRequestPayload.ApiLogStatus.SUCCESS.name())
                .build();
    }


}

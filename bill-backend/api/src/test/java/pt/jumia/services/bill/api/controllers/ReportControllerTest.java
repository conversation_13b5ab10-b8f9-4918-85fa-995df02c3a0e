package pt.jumia.services.bill.api.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.report.DailyIssuedDocumentsReportUseCase;
import pt.jumia.services.bill.domain.usecases.report.DailyReceivedDocumentsReportUseCase;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@WebMvcTest(ReportController.class)
public class ReportControllerTest extends BaseControllerTest {

    private static final String BASE_ROUTE = "/api/report";
    private static final RequestUser REQUEST_USER = RequestUser
            .builder()
            .username("controllerTestUser")
            .build();


    @MockBean
    private UserPermissionValidationUseCase userPermissionValidationUseCase;
    @MockBean
    private DailyIssuedDocumentsReportUseCase dailyIssuedDocumentsReportUseCase;
    @MockBean
    private DailyReceivedDocumentsReportUseCase dailyReceivedDocumentsReportUseCase;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void generateReport_noPermissions() throws Exception {
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(REQUEST_USER);

        request(
                post(BASE_ROUTE + "/daily"),
                null,
                HttpStatus.FORBIDDEN
        );

        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(REQUEST_USER);
        verifyNoInteractions(dailyIssuedDocumentsReportUseCase);
    }

    @Test
    public void generateReport_success() throws Exception {
        request(
                post(BASE_ROUTE + "/daily"),
                null,
                HttpStatus.OK
        );

        verify(userPermissionValidationUseCase).getCountriesCanViewDocumentsOrThrow(REQUEST_USER);
        verify(dailyIssuedDocumentsReportUseCase).generateDailyReport(any(LocalDateTime.class));
    }


    @Test
    public void generateReviewReceivedDocsReport_noPermissions() throws Exception {
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase).getCountriesCanViewReceivedDocumentsOrThrow(REQUEST_USER);

        request(
                post(BASE_ROUTE + "/review-received-documents"),
                null,
                HttpStatus.FORBIDDEN
        );

        verify(userPermissionValidationUseCase).getCountriesCanViewReceivedDocumentsOrThrow(REQUEST_USER);
        verifyNoInteractions(dailyReceivedDocumentsReportUseCase);
    }

    @Test
    public void generateReviewReceivedDocsReport_success() throws Exception {
        request(
                post(BASE_ROUTE + "/review-received-documents"),
                null,
                HttpStatus.OK
        );

        verify(userPermissionValidationUseCase).getCountriesCanViewReceivedDocumentsOrThrow(REQUEST_USER);
        verify(dailyReceivedDocumentsReportUseCase).generateReport(any(LocalDateTime.class));
    }
}

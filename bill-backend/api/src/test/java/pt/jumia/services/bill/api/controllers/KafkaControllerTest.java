package pt.jumia.services.bill.api.controllers;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.bill.domain.KafkaProducer;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;
import pt.jumia.services.bill.domain.utils.JsonUtils;

import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@WebMvcTest(KafkaController.class)
public class KafkaControllerTest extends BaseControllerTest {

    private static final String BASE_ROUTE = "/api/kafka";

    private static final BillPayload BILL_PAYLOAD = BillPayload.builder()
            .sid("fake-sid")
            .build();

    @MockBean
    private JsonUtils jsonUtils;
    @MockBean
    private KafkaProducer billProducer;

    @Test
    public void publishBill_success() throws Exception {
        request(post(BASE_ROUTE + "/publish/bill"), BILL_PAYLOAD, HttpStatus.OK);
        verify(billProducer).sendMessage(BILL_PAYLOAD);
    }
}

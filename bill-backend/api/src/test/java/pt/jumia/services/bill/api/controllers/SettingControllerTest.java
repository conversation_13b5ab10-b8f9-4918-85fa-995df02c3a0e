package pt.jumia.services.bill.api.controllers;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.api.payloads.response.SettingResponsePayload;
import pt.jumia.services.bill.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.settings.*;
import pt.jumia.services.bill.domain.utils.ResourceLoader;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(SettingController.class)
public class SettingControllerTest extends BaseControllerTest {

    private static final String BASE_ENDPOINT = "/api/settings";
    private static final RequestUser REQUEST_USER = RequestUser.builder().username("controllerTestUser").build();

    private static final Setting SETTING_1 = Setting.builder()
            .id(124L)
            .type(Setting.Type.OVERRIDE)
            .property("some.property")
            .value("some-value")
            .overrideKey("some-override-key")
            .description("some-description")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC).minusDays(1))
            .createdBy("unit-test-user")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC).minusHours(12))
            .updatedBy("unit-test-user")
            .build();
    private static final Setting SETTING_2 = Setting.builder()
            .id(234L)
            .type(Setting.Type.OVERRIDE)
            .property("some.property.2")
            .value("some-value-2")
            .overrideKey("some-override-key-2")
            .description("some-description-2")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC).minusDays(1))
            .createdBy("unit-test-user")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC).minusHours(12))
            .updatedBy("unit-test-user")
            .build();
    private static final Setting VALID_RESOURCE_SETTING = Setting.builder()
                .property("test.setting")
                .type(Setting.Type.OVERRIDE)
                .overrideKey("some-override")
                .description("some-description")
                .value("value-test")
                .build();

    @MockBean
    private UserPermissionValidationUseCase userPermissionValidationUseCase;
    @MockBean
    private ReadSettingUseCase readSettingUseCase;
    @MockBean
    private CreateSettingUseCase createSettingUseCase;
    @MockBean
    private UpdateSettingUseCase updateSettingUseCase;
    @MockBean
    private DeleteSettingUseCase deleteSettingUseCase;
    @MockBean
    private ReloadSettingsUseCase reloadSettingsUseCase;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void fetchAll_success() throws Exception {
        when(readSettingUseCase.fetchAll()).thenReturn(List.of(SETTING_1, SETTING_2));

        List<SettingResponsePayload> settings = requestList(
                get(BASE_ENDPOINT),
                SettingResponsePayload.class);

        assertThat(settings)
                .hasSize(2)
                .containsExactlyInAnyOrder(
                        new SettingResponsePayload(SETTING_1),
                        new SettingResponsePayload(SETTING_2)
                );
        verify(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);
        verify(readSettingUseCase).fetchAll();
    }

    @Test
    public void fetchAll_forbidden() throws Exception {
        doThrow(UserForbiddenException.createCannotAccess(""))
                .when(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);

        CodedErrorResponsePayload codedError = request(
                get(BASE_ENDPOINT),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        assertThat(codedError.getCode()).isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);
        verifyNoInteractions(readSettingUseCase);
    }

    @Test
    public void fetchById_success() throws Exception {
        when(readSettingUseCase.fetchById(SETTING_1.getId()))
                .thenReturn(SETTING_1);

        SettingResponsePayload settingResponse = request(
                get(String.format("%s/%d", BASE_ENDPOINT, SETTING_1.getId())),
                SettingResponsePayload.class);

        assertThat(settingResponse).isEqualTo(new SettingResponsePayload(SETTING_1));
        verify(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);
        verify(readSettingUseCase).fetchById(SETTING_1.getId());
    }

    @Test
    public void fetchById_notFound() throws Exception {
        doThrow(EntityNotFoundException.createNotFound(Setting.class, SETTING_1.getId()))
                .when(readSettingUseCase).fetchById(SETTING_1.getId());

        CodedErrorResponsePayload codedErrorResponse = request(
                get(String.format("%s/%d", BASE_ENDPOINT, SETTING_1.getId())),
                HttpStatus.NOT_FOUND,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.ENTITY_NOT_FOUND.getCode());
        verify(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);
        verify(readSettingUseCase).fetchById(SETTING_1.getId());
    }

    @Test
    public void fetchById_forbidden() throws Exception {
        doThrow(UserForbiddenException.createCannotAccess(""))
                .when(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);

        CodedErrorResponsePayload codedError = request(
                get(BASE_ENDPOINT),
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        assertThat(codedError.getCode()).isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);
        verifyNoInteractions(readSettingUseCase);
    }

    @Test
    public void create_success() throws Exception {
        String payload = ResourceLoader.getStringFromFile("settings/setting_request_payload.json");
        when(createSettingUseCase.execute(VALID_RESOURCE_SETTING)).thenReturn(SETTING_1);

        SettingResponsePayload settingResponse = request(
                post(BASE_ENDPOINT),
                payload,
                HttpStatus.CREATED,
                SettingResponsePayload.class);

        assertThat(settingResponse).isEqualTo(new SettingResponsePayload(SETTING_1));
        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(createSettingUseCase).execute(VALID_RESOURCE_SETTING);
    }

    @Test
    public void create_forbidden() throws Exception {
        String payload = ResourceLoader.getStringFromFile("settings/setting_request_payload.json");
        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);

        CodedErrorResponsePayload codedErrorResponse = request(
                post(BASE_ENDPOINT),
                payload,
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verifyNoInteractions(createSettingUseCase);
    }

    @Test
    public void create_illegalArgument() throws Exception {
        String payload = ResourceLoader.getStringFromFile("settings/setting_request_payload.json");
        doThrow(new IllegalArgumentException(""))
                .when(createSettingUseCase).execute(VALID_RESOURCE_SETTING);

        CodedErrorResponsePayload codedErrorResponse = request(
                post(BASE_ENDPOINT),
                payload,
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.ILLEGAL_ARGUMENT.getCode());
        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(createSettingUseCase).execute(VALID_RESOURCE_SETTING);
    }

    @Test
    public void update_success() throws Exception {
        String payload = ResourceLoader.getStringFromFile("settings/setting_request_payload.json");
        when(updateSettingUseCase.execute(1L, VALID_RESOURCE_SETTING)).thenReturn(SETTING_1);

        SettingResponsePayload settingResponse = request(
                put(String.format("%s/%d", BASE_ENDPOINT, 1)),
                payload,
                HttpStatus.OK,
                SettingResponsePayload.class);

        assertThat(settingResponse).isEqualTo(new SettingResponsePayload(SETTING_1));
        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(updateSettingUseCase).execute(1L, VALID_RESOURCE_SETTING);
    }

    @Test
    public void update_forbidden() throws Exception {
        String payload = ResourceLoader.getStringFromFile("settings/setting_request_payload.json");
        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);

        CodedErrorResponsePayload codedErrorResponse = request(
                put(String.format("%s/%d", BASE_ENDPOINT, 1)),
                payload,
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verifyNoInteractions(updateSettingUseCase);
    }

    @Test
    public void update_illegalArgument() throws Exception {
        String payload = ResourceLoader.getStringFromFile("settings/setting_request_payload.json");
        doThrow(new IllegalArgumentException(""))
                .when(updateSettingUseCase).execute(1L, VALID_RESOURCE_SETTING);

        CodedErrorResponsePayload codedErrorResponse = request(
                put(String.format("%s/%d", BASE_ENDPOINT, 1)),
                payload,
                HttpStatus.BAD_REQUEST,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.ILLEGAL_ARGUMENT.getCode());
        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(updateSettingUseCase).execute(1L, VALID_RESOURCE_SETTING);
    }

    @Test
    public void reload_success() throws Exception {
        mockMvc
                .perform(patch(BASE_ENDPOINT + "/reload"))
                .andExpect(status().is(HttpStatus.OK.value()))
                .andReturn();

        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(reloadSettingsUseCase).execute();
    }

    @Test
    public void reload_forbidden() throws Exception {
        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);

        mockMvc
                .perform(patch(BASE_ENDPOINT + "/reload"))
                .andExpect(status().is(HttpStatus.FORBIDDEN.value()))
                .andReturn();

        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verifyNoInteractions(reloadSettingsUseCase);
    }

    @Test
    public void delete_success() throws Exception {
        mockMvc
                .perform(delete(String.format("%s/%d", BASE_ENDPOINT , 1)))
                .andExpect(status().is(HttpStatus.OK.value()))
                .andReturn();

        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(deleteSettingUseCase).execute(1L);
    }

    @Test
    public void delete_forbidden() throws Exception {
        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);

        mockMvc
                .perform(delete(String.format("%s/%d", BASE_ENDPOINT , 1)))
                .andExpect(status().is(HttpStatus.FORBIDDEN.value()))
                .andReturn();

        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verifyNoInteractions(deleteSettingUseCase);
    }

    @Test
    public void delete_notFound() throws Exception {
        doThrow(EntityNotFoundException.createNotFound(Setting.class, 1L))
                .when(deleteSettingUseCase).execute(1L);

        mockMvc
                .perform(delete(String.format("%s/%d", BASE_ENDPOINT , 1)))
                .andExpect(status().is(HttpStatus.NOT_FOUND.value()))
                .andReturn();

        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(deleteSettingUseCase).execute(1L);
    }

    @Test
    public void delete_illegalArgument() throws Exception {
        doThrow(new IllegalArgumentException(""))
                .when(deleteSettingUseCase).execute(1L);

        mockMvc
                .perform(delete(String.format("%s/%d", BASE_ENDPOINT , 1)))
                .andExpect(status().is(HttpStatus.BAD_REQUEST.value()))
                .andReturn();

        verify(userPermissionValidationUseCase).checkCanManageSettingsOrThrow(REQUEST_USER);
        verify(deleteSettingUseCase).execute(1L);
    }
}

package pt.jumia.services.bill.api.controllers;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.api.payloads.request.AddressApiRequestPayload;
import pt.jumia.services.bill.api.payloads.request.IssuerTemplateApiRequestPayload;
import pt.jumia.services.bill.api.payloads.response.IssuerTemplateApiResponsePayload;
import pt.jumia.services.bill.api.payloads.response.error.CodedErrorResponsePayload;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.documents.CreateIssuerTemplateUseCase;
import pt.jumia.services.bill.domain.utils.JsonUtils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

@WebMvcTest(IssuerTemplateController.class)
public class IssuerTemplateControllerTest extends BaseControllerTest {

    private static final String BASE_ENDPOINT = "/api/issuer-template";
    private static final RequestUser REQUEST_USER = RequestUser.builder().username("controllerTestUser").build();

    private static final IssuerTemplateApiRequestPayload ISSUER_TEMPLATE_API_REQUEST_PAYLOAD =
            IssuerTemplateApiRequestPayload.builder()
            .type("BUSINESS")
            .shop("jumia")
            .legalName("legalName")
            .name("name")
            .taxIdentificationNumber("taxIdentificationNumber")
            .businessRegistrationNumber("businessRegistrationNumber")
            .branch("branch")
            .address(AddressApiRequestPayload.builder()
                    .country("EG")
                    .region("region")
                    .city("city")
                    .street("street")
                    .buildingNumber("buildingNumber")
                    .floor("floor")
                    .postalCode("postalCode")
                    .additionalInformation("additionalInformation")
                    .build())
            .email("email")
            .mobilePhone("mobilePhone")
            .linePhone("linePhone")
            .build();


    private static final IssuerTemplate VALID_RESOURCE_ISSUER_TEMPLATE = IssuerTemplate.builder()
            .type(IssuerType.BUSINESS)
            .shop("jumia")
            .legalName("legalName")
            .name("name")
            .taxIdentificationNumber("taxIdentificationNumber")
            .businessRegistrationNumber("businessRegistrationNumber")
            .branch("branch")
            .address(Address.builder()
                    .country(CountryCode.EG)
                    .region("region")
                    .city("city")
                    .street("street")
                    .buildingNumber("buildingNumber")
                    .floor("floor")
                    .postalCode("postalCode")
                    .additionalInformation("additionalInformation")
                    .build())
            .email("email")
            .mobilePhone("mobilePhone")
            .linePhone("linePhone")
            .build();

    @MockBean
    private UserPermissionValidationUseCase userPermissionValidationUseCase;
    @MockBean
    private CreateIssuerTemplateUseCase createIssuerTemplateUseCase;
    @MockBean
    private JsonUtils jsonUtils;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }


    @Test
    public void create_success() throws Exception {
        when(createIssuerTemplateUseCase.execute(VALID_RESOURCE_ISSUER_TEMPLATE)).
                thenReturn(ISSUER_TEMPLATE_API_REQUEST_PAYLOAD.toEntity());

        IssuerTemplateApiResponsePayload issuerTemplateApiResponsePayload = request(
                post(BASE_ENDPOINT),
                ISSUER_TEMPLATE_API_REQUEST_PAYLOAD,
                HttpStatus.CREATED,
                IssuerTemplateApiResponsePayload.class);

        assertThat(issuerTemplateApiResponsePayload)
                .isEqualTo(new IssuerTemplateApiResponsePayload(VALID_RESOURCE_ISSUER_TEMPLATE));
        verify(userPermissionValidationUseCase)
                .checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);
        verify(createIssuerTemplateUseCase).execute(VALID_RESOURCE_ISSUER_TEMPLATE);
    }

    @Test
    public void create_forbidden() throws Exception {
        doThrow(UserForbiddenException.createDontHavePermission("", ""))
                .when(userPermissionValidationUseCase)
                .checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);

        CodedErrorResponsePayload codedErrorResponse = request(
                post(BASE_ENDPOINT),
                ISSUER_TEMPLATE_API_REQUEST_PAYLOAD,
                HttpStatus.FORBIDDEN,
                CodedErrorResponsePayload.class);

        assertThat(codedErrorResponse.getCode()).isEqualTo(ErrorCode.FORBIDDEN.getCode());
        verify(userPermissionValidationUseCase)
                .checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);
        verifyNoInteractions(createIssuerTemplateUseCase);
    }

}

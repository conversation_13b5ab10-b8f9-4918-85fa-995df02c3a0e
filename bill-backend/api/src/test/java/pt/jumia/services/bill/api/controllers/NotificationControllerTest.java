package pt.jumia.services.bill.api.controllers;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import pt.jumia.services.bill.domain.entities.Notification;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;
import pt.jumia.services.bill.domain.usecases.notification.ReadNotificationUseCase;

import java.util.List;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;

@WebMvcTest(NotificationController.class)
public class NotificationControllerTest extends BaseControllerTest {

    private static final String BASE_ROUTE = "/api/notifications";

    @MockBean
    private  UserPermissionValidationUseCase userPermissionValidationUseCase;

    @MockBean
    private ReadNotificationUseCase readNotificationUseCase;

    @Test
    public void testSendReceivedDocumentEmail_success() throws Exception {

        List<Notification> purchasePortals = List.of(Notification.builder()
                .email("test-portal").build());

        when(readNotificationUseCase.getPurchasePortal()).thenReturn(purchasePortals);

        request(
                get(BASE_ROUTE + "/purchase-portal"),
                HttpStatus.OK
        );
        verify(userPermissionValidationUseCase).checkCanAccessOrThrow(REQUEST_USER);
        verify(readNotificationUseCase).getPurchasePortal();
    }
}

# Uncomment to run the application with the fake implementations (repository, network, etc.)
#spring.profiles.active=fake

info.build.version: 0.1.0
server.port: ${BILL_SERVER_PORT:8080}

spring:
  application.name: ${BILL_SPRING_APPLICATION_NAME:Bill}
  datasource.url: **********************************************************************
  flyway:
    enabled: ${BILL_SPRING_FLYWAY_ENABLED:false}
    locations: ${BILL_SPRING_FLYWAY_LOCATIONS:db/migration}
    schemas: ${BILL_SPRING_FLYWAY_SCHEMAS:audit,public}
  kafka:
    cluster-bill:
      bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
      properties:
        security:
          protocol: ${KAFKA_SECURITY_PROTOCOL:SASL_PLAINTEXT}
        sasl:
          mechanism: SCRAM-SHA-512
          jaas:
            config: org.apache.kafka.common.security.scram.ScramLoginModule optional username=bill password="somepass";
      consumer:
        key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
        value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      producer:
        value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

#Kafka
kafka:
  topics:
    generic:
      create: ${KAFKA_CREATE_GENERIC:false}
      auto-start: ${KAFKA_AUTOSTART_GENERIC:false}
      name: "taxiGeneric"
      group-id: "taxi-group-generic"
      partitions: 10
    taxi:
      create: ${KAFKA_CREATE_TAXI:true}
      auto-start: ${KAFKA_AUTOSTART_TAXI:true}
      name: >-
        taxiEG
        taxiUG
        taxiKE
      group-id: "bill-group-taxi"
      partitions: 1

endpoints.default.web.enabled: false

#Spring actuator management configs
management.endpoints.web:
  base-path: / # Force base path to be root for instead of default "/actuator"
  exposure.include: health, prometheus   # Expose only health endpoint

#data
data:
  events:
    check-connection-timeout: ${BILL_DATA_EVENTS_CHECK_CONNECTION_TIMEOUT:15s}
  db:
    driver: ${BILL_DATA_DB_DRIVER:org.postgresql.Driver}
    url: **********************************************************************
    username: ${BILL_DATA_DB_USERNAME:postgres}
    password: ${BILL_DATA_DB_PASSWORD:postgres}
    application-schema: ${BILL_DATA_DB_APPLICATION_SCHEMA:public}
    audit-schema: ${BILL_DATA_DB_AUDIT_SCHAMA:audit}
    quartz-schema: ${BILL_DATA_DB_QUARTZ_SCHEMA:public}
    max-pool-size: ${BILL_DATA_DB_MAX_POOL_SIZE:15}
    flyway:
      repair: ${BILL_DATA_DB_MAX_POOL_SIZE:false}

#API
api:
  swagger-enabled: ${BILL_API_SWAGGER_ENABLED:true}
  self-host: ${BILL_API_SELF_HOST:http://localhost:8080/}
  allowed-domains: ${BILL_API_ALLOWED_DOMAINS:http://localhost:9000,http://localhost:8080,http://localhost:3000,http://localhost:4210}

#Network
#network:

#ACL
acl:
  skip: ${BILL_ACL_SKIP:false}
  url: ${BILL_ACL_HOST:http://internal-api-acl-staging.jumia.services}
  app-name: ${BILL_ACL_APP_NAME:bill}
  cache:
    strategy: ${BILL_ACL_CACHE_STRATEGY:in-memory}
    in-memory:
      expiration-duration: ${BILL_ACL_CACHE_IN_MEMORY_EXPIRATION_DURATION:5m}
    redis:
      host: ${BILL_ACL_REDIS_HOST:dev-communications.2smgfr.0001.euw1.cache.amazonaws.com}
      port: ${BILL_ACL_REDIS_PORT:6379}
      username-key-prefix: ${BILL_ACL_REDIS_USERNAME_KEY_PREFIX:bill}
      password: ${BILL_ACL_REDIS_PASSWORD:dummy}
      expiration-duration: ${BILL_ACL_REDIS_EXPIRATION_DURATION:5m}
      timeout: ${BILL_ACL_REDIS_TIMEOUT:0s}
  migrator-user:
    username: ${BILL_ACL_MIGRATOR_USER_USERNAME:dummy}
    password: ${BILL_ACL_MIGRATOR_USER_PASSWORD:dummy}

package pt.jumia.services.bill.data.repository.psql;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.springframework.dao.DataIntegrityViolationException;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.IssuerType;

import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class PsqlIssuerTemplateRepositoryTest extends BaseRepositoryTest {
    private final IssuerTemplate ISSUER_TEMPLATE_1 = IssuerTemplate.builder()
            .shop("jumia")
            .branch("branch")
            .type(IssuerType.BUSINESS)
            .legalName("Some issuer")
            .email("<EMAIL>")
            .address(Address.builder()
                    .country(CountryCode.EG)
                    .street("Testing street")
                    .build())
            .build();
    private final IssuerTemplate ISSUER_TEMPLATE_2 = IssuerTemplate.builder()
            .shop("jumia")
            .branch("branch")
            .type(IssuerType.BUSINESS)
            .legalName("Some issuer")
            .email("<EMAIL>")
            .address(Address.builder()
                    .country(CountryCode.UG)
                    .street("Testing street")
                    .build())
            .build();
    private final IssuerTemplate ISSUER_TEMPLATE_3 = IssuerTemplate.builder()
            .shop("jumia")
            .branch("branch")
            .type(IssuerType.BUSINESS)
            .legalName("Some issuer")
            .email("<EMAIL>")
            .address(Address.builder()
                    .country(CountryCode.UG)
                    .street("Testing street")
                    .build())
            .build();

    @Test
    void testFindById_notFound() {
        Optional<IssuerTemplate> issuerTemplate = issuerTemplateRepository.findById(1L);
        assertTrue(issuerTemplate.isEmpty());
    }

    @Test
    void testFindById_success() {
        IssuerTemplate insertedIssuerTemplate = insert(ISSUER_TEMPLATE_1);
        insert(ISSUER_TEMPLATE_2);
        Optional<IssuerTemplate> issuerTemplate = issuerTemplateRepository.findById(insertedIssuerTemplate.getId());
        assertThat(issuerTemplate.get().withoutDbField())
                .isEqualTo(ISSUER_TEMPLATE_1);
    }

    @Test
    void testFindByShopAndCountry_success() {
        insert(ISSUER_TEMPLATE_1);
        insert(ISSUER_TEMPLATE_2);
        Optional<IssuerTemplate> issuerTemplate = issuerTemplateRepository
                .findByShopAndCountry("jumia", CountryCode.UG);
        assertThat(issuerTemplate.get().withoutDbField())
                .isEqualTo(ISSUER_TEMPLATE_2);
    }

    @Test
    void testInvalidInsertOfShopAndCountryConstraint() {
        insert(ISSUER_TEMPLATE_2);
        assertThatThrownBy(() -> insert(ISSUER_TEMPLATE_3))
                .isInstanceOf(DataIntegrityViolationException.class);
    }
}

package pt.jumia.services.bill.data.repository.psql;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

public class PsqlDocumentApiLogRepositoryTest extends BaseRepositoryTest {

    @Test
    void testFindById_noExistingDocumentLog_returnsEmpty() {
        assertThat(documentApiLogRepository.findById(1L)).isEmpty();
    }

    @Test
    void testInsert_findById_existingDocumentLog() {
        DocumentApiLog documentApiLog = documentApiLogRepository
                .insert(DocumentApiLog.builder()
                        .originalRequest("original-document")
                        .documentSid("document-sid")
                        .createdBy("test-user")
                        .updatedBy("test-user")
                        .build());
        Optional<DocumentApiLog> documentLogFromDb = documentApiLogRepository.findById(documentApiLog.getId());
        assertThat(documentLogFromDb).isNotEmpty();
        assertThat(documentLogFromDb.get().withoutDbFields())
                .usingRecursiveComparison()
                .isEqualTo(documentApiLog.withoutDbFields());
    }

    @Test
    void testUpdate_findById_existingDocumentLog() {
        DocumentApiLog documentApiLogInserted = documentApiLogRepository
                .insert(DocumentApiLog.builder()
                        .originalRequest("original-document")
                        .documentSid("document-sid")
                        .createdBy("test-user")
                        .updatedBy("test-user")
                        .build());
        DocumentApiLog documentApiLogUpdated = documentApiLogRepository
                .update(documentApiLogInserted.getId(), documentApiLogInserted.toBuilder()
                        .originalRequest("original-document-2")
                        .documentSid("document-sid-2")
                        .errors("errors-update")
                        .createdBy("test-user")
                        .updatedBy("test-user-2")
                        .build());
        Optional<DocumentApiLog> documentLogFromDb = documentApiLogRepository
                .findById(documentApiLogInserted.getId());
        assertThat(documentLogFromDb).isNotEmpty();
        assertThat(DocumentApiLog.builder()
                .originalRequest("original-document-2")
                .documentSid("document-sid-2")
                .errors("errors-update")
                .createdBy("test-user")
                .updatedBy("test-user-2")
                .build()
                .withoutDbFields())
                .usingRecursiveComparison()
                .isEqualTo(documentApiLogUpdated.withoutDbFields());
        assertThat(documentLogFromDb.get().withoutDbFields())
                .usingRecursiveComparison()
                .isEqualTo(documentApiLogUpdated.withoutDbFields());
    }
}

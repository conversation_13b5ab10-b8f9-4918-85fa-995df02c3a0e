package pt.jumia.services.bill.data.repository.psql;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.data.DecimalTestUtils;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;

import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class PsqlTaxAuthoritiesDetailsRepositoryTest extends BaseRepositoryTest {

    @Test
    void testFindById_noExistingDocument_returnsEmpty() {
        assertThat(taxAuthoritiesDetailsRepository.findById(UUID.randomUUID())).isEmpty();
    }

    @Test
    void testFindById_returnsExpectedTaxAuthoritiesDetails() {
        Document document = insert(generateDocument("1234"));
        TaxAuthoritiesDetails expectedDetails = insert(generateTaxAuthoritiesDetails(
                "tax-document-number01", document));

        assertThat(taxAuthoritiesDetailsRepository.findById(expectedDetails.getId())).contains(expectedDetails);
    }

    @Test
    void testFindByDocumentId_noExistingDocument_returnsEmpty() {
        assertThat(taxAuthoritiesDetailsRepository.findByDocumentId(UUID.randomUUID())).isEmpty();
    }

    @Test
    void testFindByDocumentId_existingDocument_returnsExpectedTaxAuthoritiesDetails() {
        Document document = insert(generateDocument("1234"));
        Document anotherDocument = insert(generateDocument("4321"));
        TaxAuthoritiesDetails expectedDetails = insert(generateTaxAuthoritiesDetails("tax-document-number01", document));
        insert(generateTaxAuthoritiesDetails("tax-document-number02", anotherDocument));

        assertThat(taxAuthoritiesDetailsRepository.findByDocumentId(document.getId())).contains(expectedDetails);
    }

    private Document generateDocument(String sid) {
        return Document.builder()
                .status(DocumentStatus.NEW)
                .judgeSid(null)
                .country(CountryCode.NG)
                .shop("jumia")
                .type(DocumentType.SALES_INVOICE)
                .sid(sid)
                .flow(DocumentFlow.RETAIL)
                .generatedBy("NAV")
                .issuedDate(LocalDateTime.now())
                .currency(Currency.getInstance("UGX"))
                .receiver(Receiver.builder()
                        .type(ReceiverType.CUSTOMER)
                        .legalName("Some receiver")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .issuer(Issuer.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .lineCount(1)
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                .discountAmount(DecimalTestUtils.decimalValue("0.00"))
                .originalDocument(null)
                .notes(null)
                .generateId()
                .build();
    }

    private TaxAuthoritiesDetails generateTaxAuthoritiesDetails(String taxDocumentNumber, Document document) {
        return TaxAuthoritiesDetails.builder()
                .document(document)
                .submissionId("some-submission-di")
                .taxDocumentNumber(taxDocumentNumber)
                .qrCode("some-qrcode")
                .verificationCode("some-verification-code")
                .deviceNumber("some-device-number")
                .generateId()
                .build();
    }

}

package pt.jumia.services.bill.data.test.utils;

import org.assertj.core.api.recursive.comparison.RecursiveComparisonConfiguration;

public class AssertionsHelper {

    public static RecursiveComparisonConfiguration WITHOUT_DB_FIELDS_COMPARATOR = RecursiveComparisonConfiguration.builder()
            .withIgnoreAllOverriddenEquals(true)
            .withIgnoredFieldsMatchingRegexes(".*createdAt", ".*createdBy", ".*updatedAt", ".*updatedBy")
            .build();

}

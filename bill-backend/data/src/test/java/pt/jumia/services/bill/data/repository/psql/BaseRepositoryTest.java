package pt.jumia.services.bill.data.repository.psql;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;


import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentApiLogRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentLineRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentTransformationRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaIssuerRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaIssuerTemplateRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaReceiverRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaTaxAuthoritiesDetailsRepository;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaTaxCategoryTotalRepository;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.repository.DocumentApiLogRepository;
import pt.jumia.services.bill.domain.repository.DocumentLineRepository;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;
import pt.jumia.services.bill.domain.repository.IssuerTemplateRepository;
import pt.jumia.services.bill.domain.repository.TaxAuthoritiesDetailsRepository;
import pt.jumia.services.bill.domain.repository.TaxCategoryTotalRepository;
import pt.jumia.services.bill.network.kafka.producers.TaxIProducer;

import java.util.Stack;
import java.util.UUID;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@ExtendWith(SpringExtension.class)
@SpringBootTest
@TestPropertySource(
        properties = {"spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration"}
)
public class BaseRepositoryTest {

    private final Stack<UUID> documentLinesToDelete = new Stack<>();
    private final Stack<Long> documentTransformationToDelete = new Stack<>();
    private final Stack<UUID> documentsToDelete = new Stack<>();
    private final Stack<UUID> issuersToDelete = new Stack<>();
    private final Stack<UUID> receiversToDelete = new Stack<>();
    private final Stack<UUID> taxAuthoritiesDetailsToDelete = new Stack<>();
    private final Stack<UUID> taxCategoryTotalsToDelete = new Stack<>();
    private final Stack<Long> issuerTemplatesToDelete = new Stack<>();
    private final Stack<Long> documentLogsToDelete = new Stack<>();
    private final Stack<UUID> receivedDocumentsToDelete = new Stack<>();

    @Autowired
    private JpaDocumentLineRepository jpaDocumentLineRepository;
    @Autowired
    private JpaDocumentApiLogRepository jpaDocumentApiLogRepository;
    @Autowired
    private JpaDocumentRepository jpaDocumentRepository;
    @Autowired
    private JpaIssuerRepository jpaIssuerRepository;
    @Autowired
    private JpaReceiverRepository jpaReceiverRepository;
    @Autowired
    private JpaTaxAuthoritiesDetailsRepository jpaTaxAuthoritiesDetailsRepository;
    @Autowired
    private JpaTaxCategoryTotalRepository jpaTaxCategoryTotalRepository;
    @Autowired
    private JpaIssuerTemplateRepository jpaIssuerTemplateRepository;
    @Autowired
    private JpaDocumentTransformationRepository jpaDocumentTransformationRepository;

    @Autowired
    protected DocumentAggregateRepository documentAggregateRepository;
    @Autowired
    protected DocumentRepository documentRepository;
    @Autowired
    protected DocumentLineRepository documentLineRepository;
    @Autowired
    protected DocumentApiLogRepository documentApiLogRepository;
    @Autowired
    protected TaxAuthoritiesDetailsRepository taxAuthoritiesDetailsRepository;
    @Autowired
    protected TaxCategoryTotalRepository taxCategoryTotalRepository;
    @Autowired
    protected IssuerTemplateRepository issuerTemplateRepository;

    @Autowired
    protected DocumentTransformationRepository documentTransformationRepository;

    @MockBean
    private TaxIProducer taxIProducer;

    @AfterEach
    public void baseTearDown() {
        clearStack(taxAuthoritiesDetailsToDelete, jpaTaxAuthoritiesDetailsRepository::deleteById);
        clearStack(taxCategoryTotalsToDelete, jpaTaxCategoryTotalRepository::deleteById);
        clearStack(documentLinesToDelete, jpaDocumentLineRepository::deleteById);
        clearStack(documentTransformationToDelete,jpaDocumentTransformationRepository::deleteById);
        clearStack(documentsToDelete, jpaDocumentRepository::deleteById);
        clearStack(issuersToDelete, jpaIssuerRepository::deleteById);
        clearStack(receiversToDelete, jpaReceiverRepository::deleteById);
        clearStack(issuerTemplatesToDelete, jpaIssuerTemplateRepository::deleteById);
        clearStack(documentLogsToDelete, jpaDocumentApiLogRepository::deleteById);
    }

    private <T> void clearStack(Stack<T> stack, Consumer<T> delete) {
        while (!stack.empty()) {
            try {
                delete.accept(stack.pop());
            } catch (EmptyResultDataAccessException e) {
                // Can happen if duplicate IDs end up in the stack to delete. Ignore
            }
        }
    }

    protected DocumentAggregate insert(DocumentAggregate aggregate) {
        DocumentAggregate saved = documentAggregateRepository.save(aggregate);
        Document document = saved.getDocument();
        if (document != null) {
            documentsToDelete.push(document.getId());
            if (document.getReceiver() != null) {
                receiversToDelete.push(document.getReceiver().getId());
            }
            if (document.getIssuer() != null) {
                issuersToDelete.push(document.getIssuer().getId());
            }
        }
        if (saved.getLines() != null) {
            documentLinesToDelete.addAll(saved.getLines().stream().map(DocumentLine::getId).collect(Collectors.toList()));
        }
        if (saved.getTaxCategoryTotals() != null) {
            taxCategoryTotalsToDelete.addAll(saved.getTaxCategoryTotals().stream().map(TaxCategoryTotal::getId).collect(Collectors.toList()));
        }
        if (saved.getTaxAuthoritiesDetails() != null) {
            taxAuthoritiesDetailsToDelete.add(saved.getTaxAuthoritiesDetails().getId());
        }
        return saved;
    }

    protected Document insert(Document document) {
        Document saved = documentRepository.save(document);
        documentsToDelete.push(document.getId());
        if (document.getReceiver() != null) {
            receiversToDelete.push(document.getReceiver().getId());
        }
        if (document.getIssuer() != null) {
            issuersToDelete.push(document.getIssuer().getId());
        }
        return saved;
    }

    protected DocumentLine insert(DocumentLine documentLine) {
        DocumentLine saved = documentLineRepository.save(documentLine);
        documentLinesToDelete.push(saved.getId());
        return saved;
    }

    protected DocumentTransformation insert(DocumentTransformation documentTransformation) {
        DocumentTransformation saved = documentTransformationRepository.insert(documentTransformation);
        documentTransformationToDelete.push(saved.getId());
        return saved;
    }

    protected DocumentApiLog insert(DocumentApiLog documentApiLog) {
        DocumentApiLog saved = documentApiLogRepository.insert(documentApiLog);
        documentLogsToDelete.push(saved.getId());
        return saved;
    }

    protected TaxAuthoritiesDetails insert(TaxAuthoritiesDetails taxAuthoritiesDetails) {
        TaxAuthoritiesDetails saved = taxAuthoritiesDetailsRepository.save(taxAuthoritiesDetails);
        taxAuthoritiesDetailsToDelete.push(saved.getId());
        return saved;
    }

    protected TaxCategoryTotal insert(TaxCategoryTotal taxCategoryTotal) {
        TaxCategoryTotal saved = taxCategoryTotalRepository.save(taxCategoryTotal);
        taxCategoryTotalsToDelete.push(saved.getId());
        return saved;
    }

    protected IssuerTemplate insert(IssuerTemplate issuerTemplate) {
        IssuerTemplate saved = issuerTemplateRepository.save(issuerTemplate);
        issuerTemplatesToDelete.push(saved.getId());
        return saved;
    }
}

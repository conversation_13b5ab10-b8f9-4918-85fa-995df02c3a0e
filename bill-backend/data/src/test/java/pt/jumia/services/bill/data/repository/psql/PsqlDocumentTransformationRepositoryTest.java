package pt.jumia.services.bill.data.repository.psql;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.data.DecimalTestUtils;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;


import java.time.LocalDateTime;
import java.util.Currency;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.anyOf;
import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.bill.data.test.utils.AssertionsHelper.WITHOUT_DB_FIELDS_COMPARATOR;


public class PsqlDocumentTransformationRepositoryTest extends BaseRepositoryTest {


    @Test
    void testFindById_noExistingDocument_returnsEmpty() {
        assertThat(documentTransformationRepository.findById(1L)).isEmpty();
    }

    @Test
    void testFindById_existingId_returnsExpectedDocumentTransformation() {
        Document document = insert(generateDocument());
        DocumentTransformation expectedDocumentTransformation = insert(generateDocumentTransformatiom(document));
        assertThat(documentTransformationRepository.findById(expectedDocumentTransformation.getId()))
                .usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR)
                .isEqualTo(Optional.of(expectedDocumentTransformation));
    }

    @Test
    void testFindByDocumentId_noExistingDocument_returnsEmpty(){
        assertThat(documentTransformationRepository.findByDocumentId(UUID.randomUUID())).isEmpty();
    }

    @Test
    void testFindByDocumentId_existingDocument_returnsExpectedDocumentTransformation(){
        Document document = insert(generateDocument());
        DocumentTransformation expectedDocumentTransformation = insert(generateDocumentTransformatiom(document));
        assertThat(documentTransformationRepository.findByDocumentId(document.getId()))
                .contains(expectedDocumentTransformation);

    }
    @Test
    void testFindByDocumentIdAndTypeAndOriginalValue_noExisting_returnsEmpty(){
        Document document = insert(generateDocument());
        DocumentTransformation documentTransformation = insert(generateDocumentTransformatiom(document));
        assertThat(documentTransformationRepository.findByDocumentIdAndTypeAndOriginalValue(UUID.randomUUID(),documentTransformation.getType(),
                documentTransformation.getOriginalValue())).isEmpty();
    }

    @Test
    void testFindByDocumentIdAndTypeAndOriginalValue_existing_returnsExpectedDocumentTransformation(){
        Document document = insert(generateDocument());
        DocumentTransformation documentTransformation = insert(generateDocumentTransformatiom(document));
        Optional<DocumentTransformation> optionalDocumentTransformation=
                documentTransformationRepository.findByDocumentIdAndTypeAndOriginalValue(document.getId(),documentTransformation.getType()
                        ,documentTransformation.getOriginalValue());
        assertThat(optionalDocumentTransformation.get())
                .isEqualTo(documentTransformation);
    }



    private Document generateDocument() {
        return Document.builder()
                .id(UUID.randomUUID())
                .status(DocumentStatus.NEW)
                .judgeSid(null)
                .country(CountryCode.NG)
                .shop("jumia")
                .type(DocumentType.SALES_INVOICE)
                .sid("1234")
                .flow(DocumentFlow.RETAIL)
                .generatedBy("NAV")
                .issuedDate(LocalDateTime.now())
                .currency(Currency.getInstance("UGX"))
                .receiver(Receiver.builder()
                        .type(ReceiverType.CUSTOMER)
                        .legalName("Some receiver")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .issuer(Issuer.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .lineCount(1)
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                .discountAmount(DecimalTestUtils.decimalValue("0.00"))
                .originalDocument(null)
                .notes(null)
                .generateId()
                .build();
    }
    private DocumentTransformation generateDocumentTransformatiom(Document document) {
        return DocumentTransformation.builder()
                .document(document)
                .originalValue("oldValue")
                .newValue("newValue")
                .type(DocumentTransformation.EntityType.ITEM_NAME_CHANGE)
                .build();
    }

}

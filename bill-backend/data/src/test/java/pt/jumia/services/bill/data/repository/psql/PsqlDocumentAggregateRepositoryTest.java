package pt.jumia.services.bill.data.repository.psql;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.data.DecimalTestUtils;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.ItemType;
import pt.jumia.services.bill.domain.entities.OrderDirection;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.bill.data.test.utils.AssertionsHelper.WITHOUT_DB_FIELDS_COMPARATOR;

class PsqlDocumentAggregateRepositoryTest extends BaseRepositoryTest {

    @Test
    void save() {
        DocumentAggregate aggregate = generateDocument("1234");
        aggregate.setDocumentTransformations(null);
        DocumentAggregate saved = insert(aggregate);
        assertThat(saved).usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR).isEqualTo(aggregate);
        saved.setDocumentTransformations(Collections.emptyList());
        assertThat(documentAggregateRepository.findByDocumentId(saved.getDocument().getId())).contains(saved);

        String documentSid2 = "4321";
        DocumentAggregate aggregate2 = generateDocument(documentSid2);
        aggregate2.setDocumentTransformations(null);
        aggregate2.getDocument().setOriginalDocument(DocumentId.builder()
                .id(aggregate.getDocument().getId())
                .sid(aggregate.getDocument().getSid())
                .build());
        DocumentAggregate saved2 = insert(aggregate2);
        assertThat(saved2).usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR).isEqualTo(aggregate2);
        saved2.setDocumentTransformations(Collections.emptyList());
        assertThat(documentAggregateRepository.findByDocumentId(saved2.getDocument().getId())).contains(saved2);

        String documentSid3 = "9876";
        DocumentAggregate aggregate3 = generateDocument(documentSid3);
        aggregate3.getDocument().setIssuer(aggregate3.getDocument()
                .getIssuer().toBuilder()
                .email("<EMAIL>")
                .taxIdentificationNumber("issuer-dummy-tin")
                .generateId().build());
        aggregate3.setDocumentTransformations(null);
        DocumentAggregate saved3 = insert(aggregate3);
        assertThat(saved3).usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR).isEqualTo(aggregate3);
        saved3.setDocumentTransformations(Collections.emptyList());
        assertThat(documentAggregateRepository.findByDocumentId(saved3.getDocument().getId())).contains(saved3);

        //test find all by sid
        DocumentFilter filterBySid = DocumentFilter.builder()
                .sid(documentSid2)
                .include(List.of(Document.Details.values()))
                .build();
        List<DocumentAggregate> documentsBySid = documentAggregateRepository.findAll(filterBySid);
        assertThat(documentsBySid.get(0)).usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR).isEqualTo(aggregate2);
        //test find all by tin
        DocumentFilter filterByIssuerTin = DocumentFilter.builder()
                .issuerTin("issuer-dummy-tin")
                .include(List.of(Document.Details.values()))
                .build();
        List<DocumentAggregate> documentsByIssuerTin = documentAggregateRepository.findAll(filterByIssuerTin);
        assertThat(documentsByIssuerTin.get(0))
                .usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR)
                .isEqualTo(aggregate3);

    }

    @Test
    void findAllByFiltersSortAndPagination() {
        DocumentAggregate aggregate1 = generateDocument("1234");
        DocumentAggregate savedDocument1 = insert(aggregate1);

        String documentSid2 = "4321";
        DocumentAggregate aggregate2 = generateDocument(documentSid2);
        DocumentAggregate savedDocument2 = insert(aggregate2);

        String documentSid3 = "9876";
        DocumentAggregate aggregate3 = generateDocument(documentSid3);
        insert(aggregate3);

        //test find all by status and sort by SID
        List<DocumentAggregate> documents = documentAggregateRepository.findAll(
                DocumentFilter.builder().status(DocumentStatus.NEW)
                        .countryCode(CountryCode.NG)
                        .include(List.of(Document.Details.values()))
                        .build(),
                DocumentSortFilters.builder().field(Document.SortingFields.CREATED_AT).direction(OrderDirection.ASC).build(),
                PageFilters.builder().page(1).size(1).build()
        );
        assertThat(documents).containsOnly(savedDocument1, savedDocument2);
    }


    private DocumentAggregate generateDocument(String sid) {
        Document document = Document.builder()
                .status(DocumentStatus.NEW)
                .judgeSid(null)
                .country(CountryCode.NG)
                .shop("jumia")
                .type(DocumentType.SALES_INVOICE)
                .sid(sid)
                .flow(DocumentFlow.RETAIL)
                .generatedBy("NAV")
                .issuedDate(LocalDateTime.now())
                .currency(Currency.getInstance("UGX"))
                .receiver(Receiver.builder()
                        .type(ReceiverType.CUSTOMER)
                        .legalName("Some receiver")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .issuer(Issuer.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .lineCount(1)
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                .discountAmount(DecimalTestUtils.decimalValue("0.00"))
                .originalDocument(null)
                .notes(null)
                .generateId()
                .build();
        return DocumentAggregate.builder()
                .document(document)
                .lines(List.of(DocumentLine.builder()
                        .document(document)
                        .position(0)
                        .quantity(DecimalTestUtils.decimalValue("1.0"))
                        .unitOfMeasure(UnitOfMeasure.NOT_APPLICABLE)
                        .itemCode("test-product")
                        .itemName("Test product")
                        .itemType(ItemType.PRODUCT)
                        .category(Category.builder()
                                .sid(UUID.randomUUID().toString())
                                .name("Test Category")
                                .taxAuthorityCode("C1")
                                .build())
                        .unitPrice(DecimalTestUtils.decimalValue("120.00"))
                        .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                        .netAmount(DecimalTestUtils.decimalValue("100.00"))
                        .totalTaxAmount(DecimalTestUtils.decimalValue("20.00"))
                        .discount(DocumentLine.Discount.builder()
                                .amount(DecimalTestUtils.decimalValue("0.00"))
                                .rate(DecimalTestUtils.decimalValue("0.00"))
                                .build())
                        .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .taxRate(DecimalTestUtils.decimalValue("0.20"))
                                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                                .build()))
                        .generateId()
                        .build()
                ))
                .documentTransformations(Collections.emptyList())
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .document(document)
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(DecimalTestUtils.decimalValue("0.20"))
                        .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                        .netAmount(DecimalTestUtils.decimalValue("100.00"))
                        .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                        .generateId()
                        .build()
                ))
                .taxAuthoritiesDetails(null)
                .build();
    }

}

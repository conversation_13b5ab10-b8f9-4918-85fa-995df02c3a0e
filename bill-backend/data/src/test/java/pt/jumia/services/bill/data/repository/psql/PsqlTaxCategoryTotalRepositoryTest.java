package pt.jumia.services.bill.data.repository.psql;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.data.DecimalTestUtils;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class PsqlTaxCategoryTotalRepositoryTest extends BaseRepositoryTest {

    @Test
    void testFindById_noExistingDocument_returnsEmpty() {
        assertThat(taxCategoryTotalRepository.findById(UUID.randomUUID())).isEmpty();
    }

    @Test
    void testFindById_existingId_returnsExpectedTaxCategoryTotal() {
        Document document = insert(generateDocument("1234"));
        TaxCategoryTotal expectedLine =
                insert(generateTaxCategoryTotal(document,
                        TaxCategory.VAT_GENERAL,
                        DecimalTestUtils.decimalValue("20.10"),
                        DecimalTestUtils.decimalValue("0.20")));
        insert(generateTaxCategoryTotal(document,
                TaxCategory.EXCISE_RATE,
                DecimalTestUtils.decimalValue("10.54"),
                DecimalTestUtils.decimalValue("0.60")));

        assertThat(taxCategoryTotalRepository.findById(expectedLine.getId())).contains(expectedLine);
    }

    @Test
    void testFindByDocumentId_noExistingDocument_returnsEmpty() {
        assertThat(taxCategoryTotalRepository.findByDocumentId(UUID.randomUUID())).isEmpty();
    }

    @Test
    void testFindByDocumentId_existingDocument_returnsExpectedTaxCategoryTotals() {
        Document document = insert(generateDocument("1234"));
        Document anotherDocument = insert(generateDocument("4321"));
        TaxCategoryTotal expectedLine1 =
                insert(generateTaxCategoryTotal(document,
                        TaxCategory.VAT_GENERAL,
                        DecimalTestUtils.decimalValue("20.10"),
                        DecimalTestUtils.decimalValue("0.20")));
        TaxCategoryTotal expectedLine2 =
                insert(generateTaxCategoryTotal(document,
                        TaxCategory.EXCISE_RATE,
                        DecimalTestUtils.decimalValue("10.10"),
                        DecimalTestUtils.decimalValue("0.50")));
        insert(generateTaxCategoryTotal(anotherDocument,
                TaxCategory.VAT_GENERAL,
                DecimalTestUtils.decimalValue("20.10"),
                DecimalTestUtils.decimalValue("0.20")));

        assertThat(taxCategoryTotalRepository.findByDocumentId(document.getId())).containsExactlyInAnyOrder(
                expectedLine1,
                expectedLine2
        );
    }

    private Document generateDocument(String sid) {
        return Document.builder()
                .status(DocumentStatus.NEW)
                .judgeSid(null)
                .country(CountryCode.NG)
                .shop("jumia")
                .type(DocumentType.SALES_INVOICE)
                .sid(sid)
                .flow(DocumentFlow.RETAIL)
                .generatedBy("NAV")
                .issuedDate(LocalDateTime.now())
                .currency(Currency.getInstance("UGX"))
                .receiver(Receiver.builder()
                        .type(ReceiverType.CUSTOMER)
                        .legalName("Some receiver")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .issuer(Issuer.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .lineCount(1)
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                .discountAmount(DecimalTestUtils.decimalValue("0.00"))
                .originalDocument(null)
                .notes(null)
                .generateId()
                .build();
    }

    private TaxCategoryTotal generateTaxCategoryTotal(Document document, TaxCategory category, BigDecimal taxFixedAmount, BigDecimal taxRate) {
        return TaxCategoryTotal.builder()
                .document(document)
                .taxCategory(category)
                .taxRate(taxRate)
                .taxFixedAmount(taxFixedAmount)
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                .generateId()
                .build();
    }

}

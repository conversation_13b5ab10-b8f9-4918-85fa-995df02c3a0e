package pt.jumia.services.bill.data.repository.psql;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.data.DecimalTestUtils;
import pt.jumia.services.bill.domain.entities.*;

import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static pt.jumia.services.bill.data.test.utils.AssertionsHelper.WITHOUT_DB_FIELDS_COMPARATOR;

class PsqlDocumentLineRepositoryTest extends BaseRepositoryTest {

    @Test
    void testFindById_noExistingDocument_returnsEmpty() {
        assertThat(documentLineRepository.findById(UUID.randomUUID())).isEmpty();
    }

    @Test
    void testFindById_existingId_returnsExpectedDocumentLine() {
        Document document = insert(generateDocument("1234"));
        DocumentLine expectedLine = insert(generateDocumentLine(document, 0));
        insert(generateDocumentLine(document, 1));

        assertThat(documentLineRepository.findById(expectedLine.getId())).usingRecursiveComparison(WITHOUT_DB_FIELDS_COMPARATOR)
                .isEqualTo(Optional.of(expectedLine));
    }

    @Test
    void testFindByDocumentId_noExistingDocument_returnsEmpty() {
        assertThat(documentLineRepository.findByDocumentId(UUID.randomUUID())).isEmpty();
    }

    @Test
    void testFindByDocumentId_existingDocument_returnsExpectedDocumentLines() {
        Document document = insert(generateDocument("1234"));
        Document anotherDocument = insert(generateDocument("4321"));
        DocumentLine expectedLine2 = insert(generateDocumentLine(document, 1));
        DocumentLine expectedLine1 = insert(generateDocumentLine(document, 0));
        insert(generateDocumentLine(anotherDocument, 0));

        assertThat(documentLineRepository.findByDocumentId(document.getId())).contains(expectedLine1, expectedLine2);
    }

    private Document generateDocument(String sid) {
        return Document.builder()
                .status(DocumentStatus.NEW)
                .judgeSid(null)
                .country(CountryCode.NG)
                .shop("jumia")
                .type(DocumentType.SALES_INVOICE)
                .sid(sid)
                .flow(DocumentFlow.RETAIL)
                .generatedBy("NAV")
                .issuedDate(LocalDateTime.now())
                .currency(Currency.getInstance("UGX"))
                .receiver(Receiver.builder()
                        .type(ReceiverType.CUSTOMER)
                        .legalName("Some receiver")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .issuer(Issuer.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .lineCount(1)
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                .discountAmount(DecimalTestUtils.decimalValue("0.00"))
                .originalDocument(null)
                .notes(null)
                .generateId()
                .build();
    }

    private DocumentLine generateDocumentLine(Document document, int position) {
        return DocumentLine.builder()
                .document(document)
                .position(position)
                .quantity(DecimalTestUtils.decimalValue("1.0"))
                .unitOfMeasure(UnitOfMeasure.NOT_APPLICABLE)
                .itemCode("test-product")
                .itemName("Test product")
                .itemType(ItemType.PRODUCT)
                .category(Category.builder()
                        .sid(UUID.randomUUID().toString())
                        .name("Test Category")
                        .taxAuthorityCode("C1")
                        .build())
                .unitPrice(DecimalTestUtils.decimalValue("120.00"))
                .totalAmount(DecimalTestUtils.decimalValue("120.00"))
                .netAmount(DecimalTestUtils.decimalValue("100.00"))
                .totalTaxAmount(DecimalTestUtils.decimalValue("20.00"))
                .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(DecimalTestUtils.decimalValue("0.200"))
                        .taxAmount(DecimalTestUtils.decimalValue("20.00"))
                        .build()))
                .discount(DocumentLine.Discount.builder()
                        .rate(DecimalTestUtils.decimalValue("0.00"))
                        .amount(DecimalTestUtils.decimalValue("0.00"))
                        .build())
                .generateId()
                .build();
    }

}

ALTER TABLE documents_aud ADD COLUMN IF NOT EXISTS purchase_reference_number VARCHAR(512);
ALTER TABLE documents_aud ADD COLUMN IF NOT EXISTS issued_to_local_currency_exchange_rate DECIMAL(15, 4);
ALTER TABLE documents_aud ADD COLUMN IF NOT EXISTS total_items_discount_amount DECIMAL(15, 4);
ALTER TABLE documents_aud ADD COLUMN IF NOT EXISTS extra_discount_amount DECIMAL(15, 4);
ALTER TABLE documents_aud ADD COLUMN IF NOT EXISTS fk_delivery UUID;
ALTER TABLE documents_aud ADD COLUMN IF NOT EXISTS fk_payment UUID;

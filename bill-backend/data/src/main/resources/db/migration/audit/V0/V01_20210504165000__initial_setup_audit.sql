CREATE TABLE revision_info
(
    ID        INTEGER,
    <PERSON><PERSON><PERSON><PERSON><PERSON> BIGINT,
    EMAIL     VARCHAR(255),
    time      TIMESTAMP NOT NULL,

    PRIMARY KEY (ID)
);


CREATE TABLE IF NOT EXISTS documents_aud
(
    -- internal fields
    id                   UUID,
    status               VARCHAR(100),
    judge_sid            VARCHAR(36),
    -- general information,
    country              VARCHAR(4),
    shop                 VARCHAR(50),
    type                 VA<PERSON><PERSON><PERSON>(100),
    sid                  VARCHAR(256),
    flow                 VARCHAR(256),
    generated_by         VARCHAR(256),
    reference_number     VARCHAR(512),
    issued_date          TIMESTAMP,
    currency             VARCHAR(4),
    fk_receiver          UUID,
    fk_issuer            UUID,
    fk_original_document UUID,
    notes                VARCHAR,
    -- summary,
    line_count           INT,
    total_amount         DECIMAL(15, 4),
    net_amount           DECIMAL(15, 4),
    tax_amount           DECIMAL(15, 4),
    discount_amount      DECIMAL(15, 4),
    -- audit information
    created_at           TIMESTAMP,
    created_by           <PERSON><PERSON><PERSON><PERSON>(256),
    updated_at           TIMESTAMP,
    updated_by           <PERSON><PERSON><PERSON><PERSON>(256),
    rev                  INTEGER,
    revtype              INTEGER,
    <PERSON><PERSON>AR<PERSON> KEY (id, rev)
);

CREATE TABLE IF NOT EXISTS tax_authorities_details_aud
(
    id                  UUID,
    fk_document         UUID,
    submission_id       VARCHAR(512),
    tax_document_number VARCHAR(512),
    qr_code             VARCHAR,
    verification_code   VARCHAR,
    device_number       VARCHAR(512),
    status_code         VARCHAR,
    error_code          VARCHAR,
    exception_message   VARCHAR,
    -- audit information
    created_at          TIMESTAMP,
    created_by          VARCHAR(256),
    updated_at          TIMESTAMP,
    updated_by          VARCHAR(256),
    rev                 INTEGER,
    revtype             INTEGER,
    PRIMARY KEY (id, rev)
);

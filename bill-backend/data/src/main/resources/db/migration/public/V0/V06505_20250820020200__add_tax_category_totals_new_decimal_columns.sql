-- Add new decimal columns with higher precision to tax_category_totals table
ALTER TABLE tax_category_totals
    ADD COLUMN IF NOT EXISTS tax_rate_new DECIMAL(23, 10) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS tax_fixed_amount_new DECIMAL(23, 10) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS net_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS tax_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0;

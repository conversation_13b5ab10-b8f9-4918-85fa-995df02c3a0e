-- Add new decimal columns with higher precision to documents table
ALTER TABLE documents
    ADD COLUMN IF NOT EXISTS total_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS net_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS tax_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS discount_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_items_discount_amount_new DECIMAL(23, 10) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS extra_discount_amount_new DECIMAL(23, 10) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS issued_to_local_currency_exchange_rate_new DECIMAL(23, 10) DEFAULT 0;

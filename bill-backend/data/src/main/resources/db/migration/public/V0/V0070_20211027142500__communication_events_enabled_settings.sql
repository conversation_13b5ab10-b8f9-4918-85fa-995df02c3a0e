INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'communications.event',
    'DEFAULT',
    null,
    'Flag that determines the communications event',
    'invoice_created',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'communications.event' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'communications.enabled',
    'DEFAULT',
    null,
    'Flag that determines the communications enabled',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'communications.enabled' AND type = 'DEFAULT');


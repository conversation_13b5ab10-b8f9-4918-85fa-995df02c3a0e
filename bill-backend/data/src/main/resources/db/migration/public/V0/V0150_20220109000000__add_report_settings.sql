INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'reporting.detailed_time_frame_in_hours',
    'DEFAULT',
    null,
    'Number of hours for which we render the detailed report in bill daily report email',
    '24',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'reporting.detailed_time_frame_in_hours' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'reporting.overview_time_frame_in_days',
    'DEFAULT',
    null,
    'Number of days for which we render the overview report in bill daily report email',
    '7',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'reporting.overview_time_frame_in_days' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'reporting.enabled_business_lines',
    'DEFAULT',
    null,
    'The enabled business lines for which we should send the email' ||
    'in the format of jumiaEG where shop is the shop name in documents ' ||
    'jumia and EG is the country code in the format of Alpha2, business lines are added comma separated eg: ' ||
    'jumiaEG,jumiaUG',
    'jumiaEG',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'reporting.enabled_business_lines' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'reporting.communication_mod_event_name',
    'DEFAULT',
    null,
    'Communication module event name for the report, this can be overridden by the country and shop codes jumiaEG',
    'billDailyReport',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'reporting.communication_mod_event_name' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'reporting.business_lines_communications_emails',
    'DEFAULT',
    null,
    'Emails which shall receive the report in a comma seperated format, this shall be overridden based on the business line eg: jumiaEG',
    '<EMAIL>',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'reporting.business_lines_communications_emails' AND type = 'DEFAULT');

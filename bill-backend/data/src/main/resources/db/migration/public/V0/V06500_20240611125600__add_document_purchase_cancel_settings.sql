INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_credit_note.cancellation_enabled',
    'DEFAULT',
    null,
    'Flag that determines if cancellation enabled for purchase credit note',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_credit_note.cancellation_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_debit_note.cancellation_enabled',
    'DEFAULT',
    null,
    'Flag that determines if cancellation enabled for purchase debit note',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_debit_note.cancellation_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_invoice.cancellation_enabled',
    'DEFAULT',
    null,
    'Flag that determines if cancellation enabled for purchase invoice',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_invoice.cancellation_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_credit_note.rejection_decline_enabled',
    'DEFAULT',
    null,
    'Flag that determines if rejection decline enabled for purchase credit note',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_credit_note.rejection_decline_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_debit_note.rejection_decline_enabled',
    'DEFAULT',
    null,
    'Flag that determines if rejection decline enabled for purchase debit note',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_debit_note.rejection_decline_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_invoice.rejection_decline_enabled',
    'DEFAULT',
    null,
    'Flag that determines if rejection decline enabled for purchase invoice',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_invoice.rejection_decline_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_credit_note.approve_received_documents_enabled',
    'DEFAULT',
    null,
    'Flag that determines if approve received documents enabled for purchase credit note',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_credit_note.approve_received_documents_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_debit_note.approve_received_documents_enabled',
    'DEFAULT',
    null,
    'Flag that determines if approve received documents enabled for purchase debit note',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_debit_note.approve_received_documents_enabled' AND override_key = null AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.purchase_invoice.approve_received_documents_enabled',
    'DEFAULT',
    null,
    'Flag that determines if approve received documents enabled for purchase invoice',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.purchase_invoice.approve_received_documents_enabled' AND override_key = null AND type = 'DEFAULT');

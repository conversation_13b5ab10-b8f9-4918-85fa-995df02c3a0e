ALTER TABLE documents ADD COLUMN IF NOT EXISTS purchase_reference_number VARCHAR(512) NULL;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS issued_to_local_currency_exchange_rate DECIMAL(15, 4) NULL;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS total_items_discount_amount DECIMAL(15, 4) NULL;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS extra_discount_amount DECIMAL(15, 4) NULL;
ALTER TABLE documents ADD COLUMN IF NOT EXISTS fk_delivery UUID NULL;
ALTER TABLE documents ADD CONSTRAINT documents_delivery_fk
    FOREIGN KEY (fk_delivery) REFERENCES delivery_details(id);

ALTER TABLE documents ADD COLUMN IF NOT EXISTS fk_payment UUID NULL;
ALTER TABLE documents ADD CONSTRAINT documents_payment_fk
    FOREIGN KEY (fk_payment) REFERENCES payment_details(id);


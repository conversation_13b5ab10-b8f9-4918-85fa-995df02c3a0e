UPDATE settings
SET value = 'false', override_key = null
WHERE property = 'documents.sales_invoice.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT';

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_invoice.cancellation_enabled',
       'OVERRIDE',
       'jumiaEG',
       'Flag that determines if cancellation enabled for invoice',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_invoice.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'OVERRIDE');

UPDATE settings
SET value = 'false', override_key = null
WHERE property = 'documents.sales_credit_note.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT';

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_credit_note.cancellation_enabled',
       'OVERRIDE',
       'jumiaEG',
       'Flag that determines if cancellation enabled for credit note',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_note.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'OVERRIDE');

UPDATE settings
SET value = 'false', override_key = null
WHERE property = 'documents.sales_credit_memo.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT';

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_credit_memo.cancellation_enabled',
       'OVERRIDE',
       'jumiaEG',
       'Flag that determines if cancellation enabled for credit memo',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_memo.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'OVERRIDE');

UPDATE settings
SET value = 'false', override_key = null
WHERE property = 'documents.sales_invoice.rejection_decline_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT';

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_invoice.rejection_decline_enabled',
       'OVERRIDE',
       'jumiaEG',
       'Flag that determines if rejection enabled for invoice, this can be overridden by a combination of ' ||
       'the shop name and the country code, eg shop jumia and country EG key is jumiaEG',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_invoice.rejection_decline_enabled' AND override_key = 'jumiaEG' AND type = 'OVERRIDE');

UPDATE settings
SET value = 'false', override_key = null
WHERE property = 'documents.sales_credit_note.rejection_decline_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT';

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_credit_note.rejection_decline_enabled',
       'OVERRIDE',
       'jumiaEG',
       'Flag that determines if rejection enabled for credit note, this can be overridden by a combination of ' ||
       'the shop name and the country code, eg shop jumia and country EG key is jumiaEG',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_note.rejection_decline_enabled' AND override_key = 'jumiaEG' AND type = 'OVERRIDE');

UPDATE settings
SET value = 'false', override_key = null
WHERE property = 'documents.sales_credit_memo.rejection_decline_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT';

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_credit_memo.rejection_decline_enabled',
       'OVERRIDE',
       'jumiaEG',
       'Flag that determines if rejection enabled for credit memo, this can be overridden by a combination of ' ||
       'the shop name and the country code, eg shop jumia and country EG key is jumiaEG',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_memo.rejection_decline_enabled' AND override_key = 'jumiaEG' AND type = 'OVERRIDE');

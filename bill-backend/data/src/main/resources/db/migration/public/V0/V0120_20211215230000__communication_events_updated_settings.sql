DELETE FROM settings WHERE property = 'communications.event';
INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'communications.event.sales_invoice',
    'DEFAULT',
    null,
    'Flag that determines the communications invoice event, this can be overridden by the country and shop codes jumiaEG',
    'invoice_created',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'communications.event.sales_invoice' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'communications.event.sales_credit_note',
    'DEFAULT',
    null,
    'Flag that determines the communications credit note event, this can be overridden by the country and shop codes jumiaEG',
    'credit_note_created',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'communications.event.sales_credit_note' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'communications.event.sales_credit_memo',
    'DEFAULT',
    null,
    'Flag that determines the communications credit memo event, this can be overridden by the country and shop codes jumiaEG',
    'credit_memo_created',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'communications.event.sales_credit_memo' AND type = 'DEFAULT');

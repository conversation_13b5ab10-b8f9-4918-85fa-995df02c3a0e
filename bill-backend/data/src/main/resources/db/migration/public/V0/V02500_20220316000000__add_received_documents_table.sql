CREATE TABLE IF NOT EXISTS received_documents
(
    id                  UUID         NOT NULL PRIMARY KEY,
    country             VARCHAR(4)   NOT NULL,
    shop                VARCHAR(50)    NOT NULL,
    public_url          VARCHAR(512),
    submission_id       VARCHAR(256),
    tax_document_number VA<PERSON>HAR(256),
    document_type       VARCHAR(100)   NOT NULL,
    receiver_id         VARCHAR(256),
    receiver_name       VA<PERSON>HA<PERSON>(256),
    issuer_id           VARCHAR(256),
    issuer_name         VA<PERSON>HAR(256),
    issued_date         TIMESTAMP,
    received_date       TIMESTAMP,
    total_amount        DECIMAL(15, 4),
    net_amount          DECIMAL(15, 4),
    total_sales         DECIMAL(15, 4),
    discount_amount     DECIMAL(15, 4),
    created_at          TIMESTAMP    NOT NULL,
    created_by          VA<PERSON>HAR(255) NOT NULL,
    updated_at          TIMESTAMP    NOT NULL,
    updated_by          VA<PERSON><PERSON><PERSON>(255) NOT NULL
);

-- Sequence used to generate revision ids for audit records
CREATE SEQUENCE IF NOT EXISTS hibernate_sequence INCREMENT 1 MINVALUE 1
    MAXVALUE 9223372036854775807
    START 1
    CACHE 1;

CREATE TABLE IF NOT EXISTS receivers
(
    -- internal fields
    id                             UUID         NOT NULL PRIMARY KEY,
    type                           VA<PERSON>HA<PERSON>(100) NOT NULL,
    -- identification
    legal_name                     VA<PERSON><PERSON><PERSON>(512),
    name                           VARCHAR(512),
    national_identification_number VARCHAR(512),
    tax_identification_number      VARCHAR(512),
    business_identification_number VARCHAR(512),
    -- contact
    email                          VARCHAR(512),
    mobile_phone                   VARCHAR(512),
    line_phone                     VARCHAR(512),
    -- address
    country                        VARCHAR(4),
    region                         VARCHAR(1024),
    city                           VARCHAR(1024),
    street                         VARCHAR(1024),
    building_number                VARCHAR(1024),
    floor                          VARCHAR(1024),
    postal_code                    VARCHAR(1024),
    address_additional_information VARCHAR(1024),
    -- audit information
    created_at                     TIMESTAMP    NOT NULL,
    created_by                     VARC<PERSON><PERSON>(256)
);

CREATE TABLE IF NOT EXISTS issuers
(
    -- internal fields
    id                             UUID         NOT NULL PRIMARY KEY,
    type                           VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    -- identification
    legal_name                     VARCHAR(512) NOT NULL,
    name                           VARCHAR(512),
    tax_identification_number      VARCHAR(512),
    business_identification_number VARCHAR(512),
    -- contact
    email                          VARCHAR(512) NOT NULL,
    mobile_phone                   VARCHAR(512),
    line_phone                     VARCHAR(512),
    -- address
    country                        VARCHAR(4),
    region                         VARCHAR(1024),
    city                           VARCHAR(1024),
    street                         VARCHAR(1024),
    building_number                VARCHAR(1024),
    floor                          VARCHAR(1024),
    postal_code                    VARCHAR(1024),
    address_additional_information VARCHAR(1024),
    -- audit information
    created_at                     TIMESTAMP    NOT NULL,
    created_by                     VARCHAR(256)
);

CREATE TABLE IF NOT EXISTS documents
(
    -- internal fields
    id                   UUID           NOT NULL PRIMARY KEY,
    status               VARCHAR(100)   NOT NULL,
    judge_sid            VARCHAR(36),
    -- general information
    country              VARCHAR(4)     NOT NULL,
    shop                 VARCHAR(50)    NOT NULL,
    type                 VARCHAR(100)   NOT NULL,
    sid                  VARCHAR(256)   NOT NULL,
    flow                 VARCHAR(256)   NOT NULL,
    generated_by         VARCHAR(256)   NOT NULL,
    reference_number     VARCHAR(512),
    issued_date          TIMESTAMP      NOT NULL,
    currency             VARCHAR(4)     NOT NULL,
    fk_receiver          UUID           NOT NULL,
    fk_issuer            UUID           NOT NULL,
    fk_original_document UUID,
    notes                VARCHAR,
    -- summary
    line_count           INT            NOT NULL,
    total_amount         DECIMAL(15, 4) NOT NULL,
    net_amount           DECIMAL(15, 4) NOT NULL,
    tax_amount           DECIMAL(15, 4) NOT NULL,
    discount_amount      DECIMAL(15, 4),
    -- audit information
    created_at           TIMESTAMP      NOT NULL,
    created_by           VARCHAR(256),
    updated_at           TIMESTAMP      NOT NULL,
    updated_by           VARCHAR(256),
    FOREIGN KEY (fk_receiver) REFERENCES receivers (id),
    FOREIGN KEY (fk_issuer) REFERENCES issuers (id),
    FOREIGN KEY (fk_original_document) REFERENCES documents (id)
);
CREATE UNIQUE INDEX IF NOT EXISTS document_sid_uq ON documents (sid) WHERE (status NOT IN ('TAX_ERROR_ACKED', 'TAX_ERROR_RETRIED'));
CREATE INDEX IF NOT EXISTS  document_sid_idx ON documents (sid);
CREATE INDEX IF NOT EXISTS  document_status_idx ON documents (status);
CREATE INDEX IF NOT EXISTS  document_type_idx ON documents (type);
CREATE INDEX IF NOT EXISTS  document_flow_idx ON documents (flow);
CREATE INDEX IF NOT EXISTS  document_created_at_idx ON documents (created_at DESC);
CREATE INDEX IF NOT EXISTS  document_fk_receiver_idx ON documents (fk_receiver);
CREATE INDEX IF NOT EXISTS  document_fk_issuer_idx ON documents (fk_issuer);

CREATE TABLE IF NOT EXISTS document_lines
(
    -- internal fields
    id                          UUID           NOT NULL PRIMARY KEY,
    fk_document                 UUID           NOT NULL,
    -- general information
    position                    INT            NOT NULL,
    quantity                    DECIMAL(15, 4) NOT NULL,
    unit_of_measure             VARCHAR(100)   NOT NULL,
    -- product information
    item_code                   VARCHAR(512)   NOT NULL,
    item_name                   VARCHAR(1024)  NOT NULL,
    category_sid                VARCHAR(256),
    category_name               VARCHAR(512),
    category_tax_authority_code VARCHAR(256),
    -- price information
    unit_price                  DECIMAL(15, 4) NOT NULL,
    total_amount                DECIMAL(15, 4) NOT NULL,
    net_amount                  DECIMAL(15, 4) NOT NULL,
    total_tax_amount            DECIMAL(15, 4) NOT NULL,
    applied_taxes               JSONB          NOT NULL,
    discount_amount             DECIMAL(15, 4),
    discount_rate               DECIMAL(15, 4),
    FOREIGN KEY (fk_document) REFERENCES documents (id),
    CONSTRAINT document_lines_document_position_unique UNIQUE (fk_document, position)
);

CREATE TABLE IF NOT EXISTS tax_category_totals
(
    -- internal fields
    id               UUID           NOT NULL PRIMARY KEY,
    fk_document      UUID           NOT NULL,
    -- tax information
    tax_category     VARCHAR(100)   NOT NULL,
    tax_rate         DECIMAL(15, 4),
    tax_fixed_amount DECIMAL(15, 4),
    -- summary
    total_amount     DECIMAL(15, 4) NOT NULL,
    net_amount       DECIMAL(15, 4) NOT NULL,
    tax_amount       DECIMAL(15, 4) NOT NULL,
    FOREIGN KEY (fk_document) REFERENCES documents (id),
    CONSTRAINT tax_category_totals_document_category_unique UNIQUE (fk_document, tax_category)
);

CREATE TABLE IF NOT EXISTS tax_authorities_details
(
    id                  UUID      NOT NULL PRIMARY KEY,
    fk_document         UUID      NOT NULL,
    submission_id       VARCHAR(512),
    tax_document_number VARCHAR(512),
    qr_code             VARCHAR,
    verification_code   VARCHAR,
    device_number       VARCHAR(512),
    status_code         VARCHAR,
    error_code          VARCHAR,
    exception_message   VARCHAR,
    -- audit information
    created_at          TIMESTAMP NOT NULL,
    created_by          VARCHAR(256),
    updated_at          TIMESTAMP NOT NULL,
    updated_by          VARCHAR(256),
    FOREIGN KEY (fk_document) REFERENCES documents (id)
);
CREATE UNIQUE INDEX IF NOT EXISTS tax_authorities_details_fk_document_unique ON tax_authorities_details (fk_document);

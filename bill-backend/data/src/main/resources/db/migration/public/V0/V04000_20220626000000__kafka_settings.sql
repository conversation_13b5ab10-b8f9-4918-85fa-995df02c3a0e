INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'kafka.document.push.enabled',
       'DEFAULT',
       'jumiaEG',
       'This setting allows using Kafka to push documents to TaxI, false to push by normal flow, and true to use Kafka cluster.',
       'false',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'kafka.document.push.enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT');
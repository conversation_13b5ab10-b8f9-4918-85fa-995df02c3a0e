INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.validation.ke.lines-package-unit',
       'DEFAULT',
       'jumiaKE',
       'Flag that determines if there is validation required enabled for debit note, this can be overridden by a combination of ' ||
       'the shop name and the country code, ke shop jumia and country KE key is jumiaKE',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.validation.ke.lines-package-unit' AND override_key = 'jumiaKE' AND type = 'DEFAULT');


INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.validation.ke.transaction-type',
       'DEFAULT',
       'jumiaKE',
       'Flag that determines if there is validation required enabled for debit note, this can be overridden by a combination of ' ||
       'the shop name and the country code, ke shop jumia and country KE key is jumia<PERSON>',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.validation.ke.transaction-type' AND override_key = 'jumiaKE' AND type = 'DEFAULT');
INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.validation.ke.transaction-progress',
       'DEFAULT',
       'jumiaKE',
       'Flag that determines if there is validation required enabled for debit note, this can be overridden by a combination of ' ||
       'the shop name and the country code, ke shop jumia and country KE key is jumiaKE',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.validation.ke.transaction-progress' AND override_key = 'jumiaKE' AND type = 'DEFAULT');
INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.validation.ke.document-code',
       'DEFAULT',
       'jumiaKE',
       'Flag that determines if there is validation required enabled for document code , this can be overridden by a combination of ' ||
       'the shop name and the country code, ke shop jumia and country KE key is jumiaKE',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.validation.ke.document-code' AND override_key = 'jumiaKE' AND type = 'DEFAULT');


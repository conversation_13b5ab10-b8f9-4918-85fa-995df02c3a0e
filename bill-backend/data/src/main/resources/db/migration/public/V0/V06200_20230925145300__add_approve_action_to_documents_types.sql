INSERT INTO settings(property, type, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_credit_note.approve_received_documents_enabled',
       'DEFAULT',
       'Flag that determines if approve enabled for received documents, this can be overridden by a combination of ' ||
       'the shop name jumia<PERSON> and the country code KE',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_note.approve_received_documents_enabled' AND type = 'DEFAULT');

INSERT INTO settings(property, type, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_credit_memo.approve_received_documents_enabled',
       'DEFAULT',
       'Flag that determines if approve enabled for received documents, this can be overridden by a combination of ' ||
       'the shop name jumiaKE and the country code KE',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_memo.approve_received_documents_enabled' AND type = 'DEFAULT');

INSERT INTO settings(property, type, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_debit_note.approve_received_documents_enabled',
       'DEFAULT',
       'Flag that determines if approve enabled for debit note',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_debit_note.approve_received_documents_enabled' AND type = 'DEFAULT');

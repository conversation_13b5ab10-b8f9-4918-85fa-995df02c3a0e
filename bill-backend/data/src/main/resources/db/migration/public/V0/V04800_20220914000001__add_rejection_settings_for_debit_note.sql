INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'documents.sales_debit_note.rejection_decline_enabled',
       'DEFAULT',
       'jumiaEG',
       'Flag that determines if rejection enabled for debit note, this can be overridden by a combination of ' ||
       'the shop name and the country code, eg shop jumia and country EG key is jumiaEG',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_debit_note.rejection_decline_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT');

-- Add new decimal columns with higher precision to document_lines table
ALTER TABLE document_lines
    ADD COLUMN IF NOT EXISTS quantity_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS unit_price_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS net_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS total_tax_amount_new DECIMAL(23, 10) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS discount_amount_new DECIMAL(23, 10) DEFAULT 0,
    ADD COLUMN IF NOT EXISTS discount_rate_new DECIMAL(23, 10) DEFAULT 0;

CREATE SEQUENCE IF NOT EXISTS document_api_logs_sequence_id_seq
    INCREMENT BY 1
    NO MAXVALUE
    NO MINVALUE
    CACHE 1;

CREATE TABLE IF NOT EXISTS document_api_logs
(
    id                  BIGINT DEFAULT nextval('document_api_logs_sequence_id_seq') PRIMARY KEY,
    document_sid        VARCHAR(256),
    original_request    VARCHAR      NOT NULL,
    issued_date         TIM<PERSON><PERSON><PERSON>,
    document_ids        JSONB,
    status              VARCHAR(100),
    errors              VARCHAR,
    created_at          TIMESTAMP    NOT NULL,
    created_by          VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    updated_at          TIMESTAMP    NOT NULL,
    updated_by          VA<PERSON><PERSON><PERSON>(255) NOT NULL
);
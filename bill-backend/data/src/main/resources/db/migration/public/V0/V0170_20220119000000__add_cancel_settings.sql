INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.sales_invoice.cancellation_enabled',
    'DEFAULT',
    'jumiaEG',
    'Flag that determines if cancellation enabled for invoice',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_invoice.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.sales_credit_note.cancellation_enabled',
    'DEFAULT',
    'jumiaEG',
    'Flag that determines if cancellation enabled for credit note',
    'true',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_note.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents.sales_credit_memo.cancellation_enabled',
    'DEFAULT',
    'jumiaEG',
    'Flag that determines if cancellation enabled for credit memo',
    'true',
    now(),
    'system',
    now(),
    'system'
WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents.sales_credit_memo.cancellation_enabled' AND override_key = 'jumiaEG' AND type = 'DEFAULT');


INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'exportation.documents.rows_limit',
    'DEFAULT',
    null,
    'Set limit for export documents number of rows.',
    '50000',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'exportation.documents.rows_limit' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'exportation.documents.exception_trace_lines_limit',
    'DEFAULT',
    null,
    'Set limit for export documents exception stack trace number of lines limit.',
    '3',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'exportation.documents.exception_trace_lines_limit' AND type = 'DEFAULT');

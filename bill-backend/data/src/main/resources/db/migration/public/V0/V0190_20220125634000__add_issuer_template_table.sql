CREATE SEQUENCE IF NOT EXISTS issuer_template_sequence_id_seq
    INCREMENT BY 1
    NO MAXVALUE
    NO MINVALUE
    CACHE 1;
CREATE TABLE IF NOT EXISTS issuer_template
(
    -- internal fields
    id                             BIGINT       DEFAULT nextval('issuer_template_sequence_id_seq') PRIMARY KEY,
    type                           VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    shop                           VARCHAR(10)  NOT NULL,
    -- identification
    legal_name                     VA<PERSON><PERSON><PERSON>(512) NOT NULL,
    name                           VA<PERSON>HA<PERSON>(512),
    tax_identification_number      VARCHAR(512),
    business_identification_number VARCHAR(512),
    -- contact
    email                          VARCHAR(512) NOT NULL,
    mobile_phone                   VARCHAR(512),
    line_phone                     VARCHAR(512),
    -- address
    country                        VARCHAR(4),
    region                         VARCHAR(1024),
    city                           VARCHAR(1024),
    street                         VARCHAR(1024),
    building_number                VARCHAR(1024),
    floor                          VARCHAR(1024),
    postal_code                    VA<PERSON>HAR(1024),
    branch                         VARCHAR NULL,
    address_additional_information VARCHAR(1024),
    -- audit information
    created_at                     TIMESTAMP    NOT NULL,
    created_by                     <PERSON><PERSON><PERSON><PERSON>(256),
    updated_at                     TIMESTAMP    NOT NULL,
    updated_by                     <PERSON><PERSON><PERSON><PERSON>(256),

    CONSTRAINT shop_country_idx UNIQUE (country, shop)
);
INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'issuer-override-enabled',
       'DEFAULT',
       null,
       'Flag that allows to override issuer in the document, this can be overridden by the country and shop codes jumiaEG',
       'false',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'issuer-override-enabled' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT 'issuer-override-enabled',
       'OVERRIDE',
       'jumiaEG',
       'Issuer override for Shop:jumia in Egypt, using the available templates',
       'true',
       now(),
       'system',
       now(),
       'system' WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'issuer-override-enabled' and override_key = 'jumiaEG' AND type = 'OVERRIDE');

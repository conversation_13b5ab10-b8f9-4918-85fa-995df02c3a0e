CREATE SEQUENCE IF NOT EXISTS document_transformations_sequence_id_seq
    INCREMENT BY 1
    NO MAXVALUE
    NO MINVALUE
    CACHE 1;

CREATE TABLE IF NOT EXISTS document_transformations
(
    id           BIGINT DEFAULT nextval('document_transformations_sequence_id_seq') PRIMARY KEY,
    fk_document  UUID         NOT NULL,
    original_value    VARCHAR(255) NOT NULL,
    new_value         VARCHAR(255) NOT NULL,
    type              VARCHAR(255) NOT NULL,
    created_at        TIMESTAMP    NOT NULL,
    updated_at        TIMESTAMP    NOT NULL,
    FOREIGN KEY (fk_document) REFERENCES documents (id)
    );

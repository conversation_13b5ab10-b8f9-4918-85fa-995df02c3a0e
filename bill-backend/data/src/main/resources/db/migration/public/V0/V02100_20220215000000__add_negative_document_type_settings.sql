INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents-types.negative-document-type',
    'DEFAULT',
    null,
    'Negative document type used in split invoice into two document,' ||
    ' this can be overridden by a combination of country and shop eg: jumiaEG',
    'SALES_CREDIT_NOTE',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents-types.negative-document-type' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents-types.negative-document-type',
    'OVERRIDE',
    'jumiaEG',
    'Shop: jumia, Country: EG, default negative doc type: Credit Memo',
    'SALES_CREDIT_MEMO',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents-types.negative-document-type' AND override_key = 'jumiaEG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'documents-types.negative-document-type',
    'OVERRIDE',
    'jumiaUG',
    'Shop: jumia, Country: UG, default negative doc type: Credit Note',
    'SALES_CREDIT_NOTE',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'documents-types.negative-document-type' AND override_key = 'jumiaUG' AND type = 'OVERRIDE');

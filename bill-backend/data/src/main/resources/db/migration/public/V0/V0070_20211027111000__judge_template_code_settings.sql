INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.invoice_codes',
    'DEFAULT',
    null,
    'Flag that determines the judge invoice codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.invoice_codes' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.invoice_codes',
    'OVERRIDE',
    'jumiaUG',
    'Flag that determines the judge invoice codes',
    'efris_invoice',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.invoice_codes' and override_key = 'jumiaUG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.invoice_codes',
    'OVERRIDE',
    'jumiaEG',
    'Flag that determines the judge invoice codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.invoice_codes' and override_key = 'jumiaEG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.invoice_codes',
    'OVERRIDE',
    'jumiaNG',
    'Flag that determines the judge invoice codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.invoice_codes' and override_key = 'jumiaNG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_note_codes',
    'DEFAULT',
    null,
    'Flag that determines the judge credit note codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_note_codes' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_note_codes',
    'OVERRIDE',
    'jumiaUG',
    'Flag that determines the judge credit note codes',
    'efris_credit_note',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_note_codes' and override_key = 'jumiaUG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_note_codes',
    'OVERRIDE',
    'jumiaEG',
    'Flag that determines the judge credit note codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_note_codes' and override_key = 'jumiaEG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_note_codes',
    'OVERRIDE',
    'jumiaNG',
    'Flag that determines the judge credit note codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_note_codes' and override_key = 'jumiaNG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_memos',
    'DEFAULT',
    null,
    'Flag that determines the judge credit memos codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_memos' AND type = 'DEFAULT');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_memos',
    'OVERRIDE',
    'jumiaUG',
    'Flag that determines the judge credit memos codes',
    'efris_credit_memos',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_memos' and override_key = 'jumiaUG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_memos',
    'OVERRIDE',
    'jumiaEG',
    'Flag that determines the judge credit memos codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_memos' and override_key = 'jumiaEG' AND type = 'OVERRIDE');

INSERT INTO settings(property, type, override_key, description, value, created_at, created_by, updated_at, updated_by)
SELECT
    'judge.credit_memos',
    'OVERRIDE',
    'jumiaNG',
    'Flag that determines the judge credit memos codes',
    'bill_code',
    now(),
    'system',
    now(),
    'system'
    WHERE NOT EXISTS(SELECT 1 FROM settings WHERE property = 'judge.credit_memos' and override_key = 'jumiaNG' AND type = 'OVERRIDE');

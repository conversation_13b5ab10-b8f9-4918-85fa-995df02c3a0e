package pt.jumia.services.bill.data.repository.events.channels;


import com.impossibl.postgres.jdbc.PGDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import pt.jumia.services.bill.data.repository.events.PsqlAbstractChannel;
import pt.jumia.services.bill.data.repository.psql.PsqlSettingRepository;
import pt.jumia.services.bill.domain.usecases.settings.ReloadSettingsUseCase;

import java.sql.SQLException;

@Slf4j
public class PsqlSettingsChangeChannel extends PsqlAbstractChannel {

    private static final String CHANNEL_NAME = "settings_changed_channel";

    private final PsqlSettingRepository psqlSettingRepository;
    private final ReloadSettingsUseCase reloadSettingsUseCase;

    PsqlSettingsChangeChannel(
            PGDataSource pgDataSource,
            PsqlSettingRepository psqlSettingRepository,
            ReloadSettingsUseCase reloadSettingsUseCase,
            int checkConnectionTimeout) throws SQLException {
        super(pgDataSource, CHANNEL_NAME, checkConnectionTimeout);
        this.psqlSettingRepository = psqlSettingRepository;
        this.reloadSettingsUseCase = reloadSettingsUseCase;
    }

    @Override
    protected String loadNotifyCommandPayload() {
        // No payload needed
        return null;
    }

    @Override
    public void notification(int processId, String channelName, String payload) {
        log.info("Received notification from connection with pid [{}], channel [{}], payload [{}]",
                processId, channelName, payload);

        try {
            reloadSettingsUseCase.execute(psqlSettingRepository.findAll());
        } catch (Exception e) {
            log.error("Error reloading settings: {}", ExceptionUtils.getStackTrace(e));
        }
    }
}

package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.repository.DocumentLineRepository;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;
import pt.jumia.services.bill.domain.repository.TaxAuthoritiesDetailsRepository;
import pt.jumia.services.bill.domain.repository.TaxCategoryTotalRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlDocumentAggregateRepository implements DocumentAggregateRepository {

    private final DocumentRepository documentRepository;
    private final DocumentLineRepository documentLineRepository;
    private final TaxCategoryTotalRepository taxCategoryTotalRepository;
    private final TaxAuthoritiesDetailsRepository taxAuthoritiesDetailsRepository;
    private final DocumentTransformationRepository documentTransformationRepository;

    @Override
    @Transactional
    public Optional<DocumentAggregate> findByDocumentId(UUID id) {
        return documentRepository.findById(id)
                .map(document -> DocumentAggregate.builder()
                        .document(document)
                        .lines(documentLineRepository.findByDocumentId(id))
                        .documentTransformations(documentTransformationRepository.findByDocumentId(id))
                        .taxCategoryTotals(taxCategoryTotalRepository.findByDocumentId(id))
                        .taxAuthoritiesDetails(taxAuthoritiesDetailsRepository.findByDocumentId(id).orElse(null))
                        .build()
                );
    }

    @Override
    @Transactional
    public List<DocumentAggregate> findAll(DocumentFilter documentAggregatesFilters) {
        return documentRepository.findAll(documentAggregatesFilters)
                .stream().map(document -> DocumentAggregate.builder()
                        .document(document)
                        .lines(documentLineRepository.findByDocumentId(document.getId()))
                        .taxCategoryTotals(taxCategoryTotalRepository.findByDocumentId(document.getId()))
                        .taxAuthoritiesDetails(taxAuthoritiesDetailsRepository.findByDocumentId(document.getId()).orElse(null))
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public List<DocumentAggregate> findAll(DocumentFilter documentAggregatesFilters, DocumentSortFilters sortFilters, PageFilters pageFilters) {
        return documentRepository.findAll(documentAggregatesFilters, sortFilters, pageFilters)
                .stream().map(document -> DocumentAggregate.builder()
                        .document(document)
                        .lines(documentLineRepository.findByDocumentId(document.getId()))
                        .taxCategoryTotals(taxCategoryTotalRepository.findByDocumentId(document.getId()))
                        .taxAuthoritiesDetails(taxAuthoritiesDetailsRepository.findByDocumentId(document.getId()).orElse(null))
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DocumentAggregate save(DocumentAggregate documentAggregate) {
        //TODO review if we should have bulk inserts for lines
        return DocumentAggregate.builder()
                .document(documentRepository.save(documentAggregate.getDocument()))
                .lines(documentAggregate.getLines().stream()
                        .map(documentLineRepository::save)
                        .collect(Collectors.toList()))
                .taxCategoryTotals(documentAggregate.getTaxCategoryTotals().stream()
                        .map(taxCategoryTotalRepository::save)
                        .collect(Collectors.toList()))
                .taxAuthoritiesDetails(documentAggregate.getTaxAuthoritiesDetails() != null
                        ? taxAuthoritiesDetailsRepository.save(documentAggregate.getTaxAuthoritiesDetails())
                        : null)
                .build();
    }

    @Override
    @Transactional
    public List<DocumentAggregate> saveAll(List<DocumentAggregate> documentAggregates) {
        return documentAggregates.stream().map(documentAggregate -> DocumentAggregate.builder()
                .document(documentRepository.save(documentAggregate.getDocument()))
                .lines(documentAggregate.getLines().stream()
                        .map(documentLineRepository::save)
                        .collect(Collectors.toList()))
                .taxCategoryTotals(documentAggregate.getTaxCategoryTotals().stream()
                        .map(taxCategoryTotalRepository::save)
                        .collect(Collectors.toList()))
                .taxAuthoritiesDetails(documentAggregate.getTaxAuthoritiesDetails() != null
                        ? taxAuthoritiesDetailsRepository.save(documentAggregate.getTaxAuthoritiesDetails())
                        : null)
                .build()).collect(Collectors.toList());
    }

}

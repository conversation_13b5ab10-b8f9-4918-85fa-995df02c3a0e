package pt.jumia.services.bill.data.repository.events;

import com.impossibl.postgres.jdbc.PGDataSource;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pt.jumia.services.bill.domain.properties.DataProperties;

import java.net.URI;

/**
 * Configurations responsible for creating a PGConnection for psql events management.
 */
@Configuration
@AllArgsConstructor
public class PsqlEventsConfiguration {

    private final DataProperties dataProperties;

    private static final String BACKSLASH = "/";
    private static final String EMPTY_STRING = "";
    private static final String JDBC_SCHEMA = "jdbc:";

    @Bean
    public PGDataSource pgDataSource() {
        // Create new url without schema from jdbcUrl
        String url = dataProperties.getDb().getUrl().replace(JDBC_SCHEMA, EMPTY_STRING);

        // Pass it to an URI to be parsed and used to extract parameters.
        URI uri = URI.create(url);

        PGDataSource pgDataSource = new PGDataSource();
        pgDataSource.setHost(uri.getHost());
        pgDataSource.setPort(uri.getPort());
        if (uri.getPath() != null) {
            pgDataSource.setDatabaseName(uri.getPath().replace(BACKSLASH, EMPTY_STRING));
        }
        pgDataSource.setUser(dataProperties.getDb().getUsername());
        pgDataSource.setPassword(dataProperties.getDb().getPassword());

        return pgDataSource;
    }
}

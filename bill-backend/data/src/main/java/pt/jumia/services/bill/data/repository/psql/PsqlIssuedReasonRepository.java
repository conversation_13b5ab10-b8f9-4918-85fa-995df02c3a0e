package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.IssuedReasonPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaIssuedReasonRepository;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.repository.IssuedReasonRepository;

import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class PsqlIssuedReasonRepository implements IssuedReasonRepository {

    private final JpaIssuedReasonRepository jpaIssuedReasonRepository;

    @Override
    @Transactional
    public Optional<IssuedReason> findById(UUID id) {
        return jpaIssuedReasonRepository.findById(id).map(IssuedReasonPsql::toEntity);
    }

    @Override
    public IssuedReason save(IssuedReason issuedReason) {
        Optional<IssuedReasonPsql> existingIssuedReason = jpaIssuedReasonRepository.findById(issuedReason.getId());

        if (existingIssuedReason.isPresent()) {
            return existingIssuedReason.get().toEntity();
        } else {
            return jpaIssuedReasonRepository.save(new IssuedReasonPsql(issuedReason)).toEntity();
        }
    }

}

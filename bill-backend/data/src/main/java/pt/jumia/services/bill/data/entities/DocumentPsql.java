package pt.jumia.services.bill.data.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.OtherSplitDocument;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Currency;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Entity
@Audited
@Table(name = "documents")
@Getter
@Setter
@NoArgsConstructor
public class DocumentPsql {

    public static final Map<Document.SortingFields, String> ENTITY_FIELDS;

    static {
        ENTITY_FIELDS = Map.ofEntries(
                Map.entry(Document.SortingFields.CREATED_AT, "createdAt"),
                Map.entry(Document.SortingFields.UPDATED_AT, "updatedAt"),
                Map.entry(Document.SortingFields.ISSUE_DATE, "issuedDate"),
                Map.entry(Document.SortingFields.RECEIVED_DATE, "receivedDate")

        );
    }

    //
    // Internal fields
    //

    @Id
    @Column(name = "id")
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private DocumentStatus status;

    @Column(name = "judge_sid")
    private String judgeSid;

    //
    // General information
    //

    @Column(name = "country", nullable = false)
    private String country;

    @Column(name = "shop", nullable = false)
    private String shop;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private DocumentType type;

    @Column(name = "sid", nullable = false)
    private String sid;

    @Enumerated(EnumType.STRING)
    @Column(name = "flow", nullable = false)
    private DocumentFlow flow;

    @Column(name = "generated_by", nullable = false)
    private String generatedBy;

    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "purchase_reference_number")
    private String purchaseReferenceNumber;

    @Column(name = "issued_date", nullable = false)
    private LocalDateTime issuedDate;

    @Column(name = "currency", nullable = false)
    private String currency;

    @Column(name = "issued_to_local_currency_exchange_rate")
    private BigDecimal issuedToLocalCurrencyExchangeRate;

    @Column(name = "issued_to_local_currency_exchange_rate_new")
    private BigDecimal issuedToLocalCurrencyExchangeRateNew;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "fk_receiver", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private ReceiverPsql receiver;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "fk_issued_reason")
    @ManyToOne(fetch = FetchType.LAZY)
    private IssuedReasonPsql issuedReason;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "fk_issuer", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private IssuerPsql issuer;

    @JoinColumn(name = "fk_original_document")
    @ManyToOne(fetch = FetchType.LAZY)
    private DocumentPsql originalDocument;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "fk_payment")
    @ManyToOne(fetch = FetchType.LAZY)
    private PaymentDetailsPsql payment;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "fk_delivery")
    @ManyToOne(fetch = FetchType.LAZY)
    private DeliveryDetailsPsql delivery;

    @JoinColumn(name = "fk_other_split_document")
    @ManyToOne(fetch = FetchType.LAZY)
    private DocumentPsql otherSplitDocument;

    @Column(name = "notes")
    private String notes;


    //
    // Summary
    //

    @Column(name = "line_count", nullable = false)
    private int lineCount;

    @Column(name = "total_amount", nullable = false)
    private BigDecimal totalAmount;

    @Column(name = "total_amount_new", nullable = false)
    private BigDecimal totalAmountNew;

    @Column(name = "net_amount", nullable = false)
    private BigDecimal netAmount;

    @Column(name = "net_amount_new", nullable = false)
    private BigDecimal netAmountNew;

    @Column(name = "tax_amount", nullable = false)
    private BigDecimal taxAmount;

    @Column(name = "tax_amount_new", nullable = false)
    private BigDecimal taxAmountNew;

    @Column(name = "discount_amount", nullable = false)
    private BigDecimal discountAmount;

    @Column(name = "discount_amount_new", nullable = false)
    private BigDecimal discountAmountNew;

    @Column(name = "total_items_discount_amount")
    private BigDecimal totalItemsDiscountAmount;

    @Column(name = "total_items_discount_amount_new")
    private BigDecimal totalItemsDiscountAmountNew;

    @Column(name = "extra_discount_amount")
    private BigDecimal extraDiscountAmount;

    @Column(name = "extra_discount_amount_new")
    private BigDecimal extraDiscountAmountNew;

    //
    // Audit information
    //

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by")
    private String updatedBy;

    @Column(name = "source_type")
    private String sourceType;

    @Column(name = "posting_group")
    private String postingGroup;

    @Column(name = "reviewed", nullable = false)
    private boolean reviewed;

    @Column(name = "reviewed_at")
    private LocalDateTime reviewedAt;

    @Column(name = "reviewed_by")
    private String reviewedBy;

    @NotAudited
    @Column(name = "transaction_type")
    private String transactionType;

    @NotAudited
    @Column(name = "transaction_progress")
    private String transactionProgress;

    @Column(name = "received_date")
    private LocalDateTime receivedDate;

    public DocumentPsql(Document document) {
        this.id = document.getId();
        this.createdAt = document.getCreatedAt();
        this.createdBy = document.getCreatedBy();
        this.updatedAt = document.getUpdatedAt();
        this.updatedBy = document.getUpdatedBy();
        this.updateFrom(document);
    }

    public DocumentPsql(DocumentId documentId) {
        this.id = documentId.getId();
        this.sid = documentId.getSid();
    }

    public DocumentPsql(UUID documentId) {
        this.id = documentId;
    }

    public DocumentPsql(OtherSplitDocument otherSplitDocument) {
        this.id = otherSplitDocument.getId();
        this.sid = otherSplitDocument.getSid();
        this.type = otherSplitDocument.getType();
        this.status = otherSplitDocument.getStatus();
    }

    public final DocumentPsql updateFrom(Document document) {
        this.status = document.getStatus();
        this.judgeSid = document.getJudgeSid();
        this.country = document.getCountry().getAlpha2();
        this.shop = document.getShop();
        this.type = document.getType();
        this.sid = document.getSid();
        this.flow = document.getFlow();
        this.generatedBy = document.getGeneratedBy();
        this.referenceNumber = document.getReferenceNumber();
        this.purchaseReferenceNumber = document.getPurchaseReferenceNumber();
        this.issuedDate = document.getIssuedDate();
        this.currency = document.getCurrency().getCurrencyCode();
        this.issuedToLocalCurrencyExchangeRate = document.getIssuedToLocalCurrencyExchangeRate();
        this.issuedToLocalCurrencyExchangeRateNew = document.getIssuedToLocalCurrencyExchangeRate();
        this.receiver = document.getReceiver() != null ? new ReceiverPsql(document.getReceiver()) : null;
        this.issuer = document.getIssuer() != null ? new IssuerPsql(document.getIssuer()) : null;
        this.issuedReason = document.getIssuedReason() != null ?
                new IssuedReasonPsql(document.getIssuedReason()) : null;
        this.lineCount = document.getLineCount();
        this.totalAmount = document.getTotalAmount();
        this.totalAmountNew = document.getTotalAmount();
        this.netAmount = document.getNetAmount();
        this.netAmountNew = document.getNetAmount();
        this.taxAmount = document.getTaxAmount();
        this.taxAmountNew = document.getTaxAmount();
        this.discountAmount = document.getDiscountAmount();
        this.discountAmountNew = document.getDiscountAmount();
        this.totalItemsDiscountAmount = document.getTotalItemsDiscountAmount();
        this.totalItemsDiscountAmountNew = document.getTotalItemsDiscountAmount();
        this.extraDiscountAmount = document.getExtraDiscountAmount();
        this.extraDiscountAmountNew = document.getExtraDiscountAmount();
        this.originalDocument = document.getOriginalDocument() != null
                ? new DocumentPsql(document.getOriginalDocument())
                : null;
        this.otherSplitDocument = document.getOtherSplitDocument() != null ?
                new DocumentPsql(document.getOtherSplitDocument()) : null;
        this.payment = document.getPayment() == null ? null
                : new PaymentDetailsPsql(document.getPayment());

        this.delivery = document.getDelivery() == null ? null
                : new DeliveryDetailsPsql(document.getDelivery());
        this.notes = document.getNotes();

        this.sourceType = document.getSourceType();
        this.postingGroup = document.getPostingGroup();

        this.reviewed = document.getReviewed();
        this.reviewedAt = document.getReviewedAt();
        this.reviewedBy = document.getReviewedBy();

        this.receivedDate = document.getReceivedDate();
        this.transactionType = document.getTransactionType();
        this.transactionProgress = document.getTransactionProgress();

        return this;
    }

    public void updateAuditInformation() {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);

        if (this.createdAt == null) {
            this.createdAt = now;
            this.createdBy = RequestContext.getUsername();
        }

        this.updatedAt = now;
        this.updatedBy = RequestContext.getUsername();
    }

    public Document toEntity() {
        return toEntity(List.of(Document.Details.values()));
    }

    public Document toEntity(List<Document.Details> include) {
        Document.DocumentBuilder builder = Document.builder()
                .id(id)
                .status(status)
                .judgeSid(judgeSid)
                .country(CountryCode.getByAlpha2Code(country))
                .shop(shop)
                .type(type)
                .sid(sid)
                .flow(flow)
                .generatedBy(generatedBy)
                .referenceNumber(referenceNumber)
                .purchaseReferenceNumber(purchaseReferenceNumber)
                .issuedDate(issuedDate)
                .currency(Currency.getInstance(currency))
                .issuedToLocalCurrencyExchangeRate(issuedToLocalCurrencyExchangeRate)
                .lineCount(lineCount)
                .totalAmount(totalAmount)
                .netAmount(netAmount)
                .taxAmount(taxAmount)
                .discountAmount(discountAmount)
                .totalItemsDiscountAmount(totalItemsDiscountAmount)
                .extraDiscountAmount(extraDiscountAmount)
                .notes(notes)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .sourceType(sourceType)
                .postingGroup(postingGroup)
                .reviewed(reviewed)
                .reviewedAt(reviewedAt)
                .reviewedBy(reviewedBy)
                .receivedDate(receivedDate)
                .transactionType(transactionType)
                .transactionProgress(transactionProgress);

        if (this.receiver != null && include.contains(Document.Details.RECEIVER)) {
            builder.receiver(this.receiver.toEntity());
        }

        if (this.issuer != null && include.contains(Document.Details.ISSUER)) {
            builder.issuer(this.issuer.toEntity());
        }

        if (this.issuedReason != null && include.contains(Document.Details.ISSUED_REASON)) {
            builder.issuedReason(this.issuedReason.toEntity());
        }

        if (this.originalDocument != null && include.contains(Document.Details.ORIGINAL_DOCUMENT)) {
            builder.originalDocument(this.originalDocument.toEntityId());
        }

        if (this.otherSplitDocument != null && include.contains(Document.Details.OTHER_SPLIT_DOCUMENT)) {
            builder.otherSplitDocument(this.otherSplitDocument.toEntityOtherSplitDocument());
        }

        if (this.payment != null && include.contains(Document.Details.PAYMENT)) {
            builder.payment(this.payment.toEntity());
        }

        if (this.delivery != null && include.contains(Document.Details.DELIVERY)) {
            builder.delivery(this.delivery.toEntity());
        }

        return builder.build();
    }

    public DocumentId toEntityId() {
        return DocumentId.builder()
                .id(id)
                .sid(sid)
                .build();
    }

    public OtherSplitDocument toEntityOtherSplitDocument() {
        return OtherSplitDocument.builder()
                .id(id)
                .sid(sid)
                .type(type)
                .status(status)
                .build();
    }
}

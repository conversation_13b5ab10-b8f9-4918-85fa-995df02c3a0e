package pt.jumia.services.bill.data.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.DeliveryDetails;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@Entity
@Table(name = "delivery_details")
@NoArgsConstructor
public class DeliveryDetailsPsql {

    @Id
    @Column(name = "id")
    private UUID id;

    @Column(name = "approach")
    private String approach;

    @Column(name = "packaging")
    private String packaging;

    @Column(name = "date_validity")
    private LocalDateTime dateValidity;

    @Column(name = "export_port")
    private String exportPort;

    @Column(name = "gross_weight")
    private BigDecimal grossWeight;

    @Column(name = "net_weight")
    private BigDecimal netWeight;

    @Column(name = "country_of_origin")
    private String countryOfOrigin;

    @Column(name = "terms")
    private String terms;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    public DeliveryDetailsPsql(DeliveryDetails deliveryDetails) {
        this.id = deliveryDetails.getId();
        this.createdAt = deliveryDetails.getCreatedAt();
        this.createdBy = deliveryDetails.getCreatedBy();

        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now(ZoneOffset.UTC);
            this.createdBy = RequestContext.getUsername();
        }

        this.updateFrom(deliveryDetails);
    }

    public final DeliveryDetailsPsql updateFrom(DeliveryDetails deliveryDetails) {
        this.approach = deliveryDetails.getApproach();
        this.packaging = deliveryDetails.getPackaging();
        this.dateValidity = deliveryDetails.getDateValidity();
        this.exportPort = deliveryDetails.getExportPort();
        this.grossWeight = deliveryDetails.getGrossWeight();
        this.netWeight = deliveryDetails.getNetWeight();
        this.countryOfOrigin = deliveryDetails.getCountryOfOrigin() == null ? null :
                deliveryDetails.getCountryOfOrigin().getAlpha2();
        this.terms = deliveryDetails.getTerms();
        return this;
    }

    public DeliveryDetails toEntity() {
        return DeliveryDetails.builder()
                .id(id)
                .approach(approach)
                .packaging(packaging)
                .dateValidity(dateValidity)
                .exportPort(exportPort)
                .grossWeight(grossWeight)
                .netWeight(netWeight)
                .countryOfOrigin(countryOfOrigin == null ? null : CountryCode.getByAlpha2Code(countryOfOrigin))
                .terms(terms)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .build();
    }

}

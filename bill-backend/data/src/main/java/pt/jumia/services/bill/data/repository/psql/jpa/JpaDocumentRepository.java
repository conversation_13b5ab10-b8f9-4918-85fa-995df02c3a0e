package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.DocumentPsql;
import pt.jumia.services.bill.data.entities.QDocumentPsql;

import java.util.UUID;

public interface JpaDocumentRepository extends JpaRepository<DocumentPsql, UUID>, QuerydslPredicateExecutor<QDocumentPsql> {

}

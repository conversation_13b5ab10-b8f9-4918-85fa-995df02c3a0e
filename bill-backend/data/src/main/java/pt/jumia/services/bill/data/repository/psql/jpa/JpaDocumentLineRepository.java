package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.DocumentLinePsql;
import pt.jumia.services.bill.data.entities.QDocumentLinePsql;

import java.util.List;
import java.util.UUID;

public interface JpaDocumentLineRepository extends JpaRepository<DocumentLinePsql, UUID>, QuerydslPredicateExecutor<QDocumentLinePsql> {

    List<DocumentLinePsql> findByDocumentId(UUID uuid);
}

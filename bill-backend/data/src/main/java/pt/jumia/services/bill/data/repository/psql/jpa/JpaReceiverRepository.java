package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.QReceiverPsql;
import pt.jumia.services.bill.data.entities.ReceiverPsql;

import java.util.UUID;

public interface JpaReceiverRepository extends JpaRepository<ReceiverPsql, UUID>, QuerydslPredicateExecutor<QReceiverPsql> {

}

package pt.jumia.services.bill.data.entities;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;
import pt.jumia.services.bill.data.audit.listener.UserRevisionListener;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "revision_info", schema = "\"audit\"")
@RevisionEntity(UserRevisionListener.class)
@EqualsAndHashCode(callSuper = true)
public class RevisionPsql extends DefaultRevisionEntity {

    private static final long serialVersionUID = -2621042342875858176L;

    private String email;

    private LocalDateTime time;

}

package pt.jumia.services.bill.data.entities;

import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import pt.jumia.services.bill.domain.entities.ConvertibleToDomain;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static pt.jumia.services.bill.data.entities.DocumentApiLogPsql.JSON_DATA_TYPE;

@Entity
@Setter
@NoArgsConstructor
@Table(name = "document_api_logs")
@TypeDef(
        name = JSON_DATA_TYPE,
        typeClass = JsonBinaryType.class
)
public class DocumentApiLogPsql implements ConvertibleToDomain<DocumentApiLog> {
    public static final String JSON_DATA_TYPE = "jsonb";
    @Id
    @SequenceGenerator(name = "document_api_logs_seq", sequenceName = "document_api_logs_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "document_api_logs_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "document_sid", nullable = false)
    private String documentSid;

    @Column(name = "original_request", nullable = false)
    private String originalRequest;

    @Column(name = "issued_date")
    private LocalDateTime issuedDate;

    @Type(type = JSON_DATA_TYPE)
    @Column(name = "document_ids")
    private List<UUID> resultingDocuments;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private DocumentApiLog.DocumentApiLogStatus status;

    @Column(name = "errors")
    private String errors;

    //
    // Audit information
    //

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by")
    private String updatedBy;

    public DocumentApiLogPsql(DocumentApiLog documentApiLog) {
        this.id = documentApiLog.getId();
        this.documentSid = documentApiLog.getDocumentSid();
        this.originalRequest = documentApiLog.getOriginalRequest();
        this.issuedDate = documentApiLog.getIssuedDate();
        this.resultingDocuments = documentApiLog.getResultingDocumentsIds();
        this.status = documentApiLog.getStatus();
        this.errors = documentApiLog.getErrors();
        this.createdAt = documentApiLog.getCreatedAt();
        this.createdBy = documentApiLog.getCreatedBy();
        this.updatedAt = documentApiLog.getUpdatedAt();
        this.updatedBy = documentApiLog.getUpdatedBy();
    }

    @Override
    public DocumentApiLog toEntity() {
        return DocumentApiLog.builder()
                .id(id)
                .documentSid(documentSid)
                .originalRequest(originalRequest)
                .resultingDocumentsIds(resultingDocuments)
                .issuedDate(issuedDate)
                .status(status)
                .errors(errors)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}

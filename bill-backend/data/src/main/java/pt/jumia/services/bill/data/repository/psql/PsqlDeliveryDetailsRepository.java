package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.DeliveryDetailsPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDeliveryDetailsRepository;
import pt.jumia.services.bill.domain.entities.DeliveryDetails;
import pt.jumia.services.bill.domain.repository.DeliveryDetailsRepository;

import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class PsqlDeliveryDetailsRepository implements DeliveryDetailsRepository {

    private final JpaDeliveryDetailsRepository jpaDeliveryDetailsRepository;

    @Override
    @Transactional
    public Optional<DeliveryDetails> findById(UUID id) {
        return jpaDeliveryDetailsRepository.findById(id).map(DeliveryDetailsPsql::toEntity);
    }

    @Override
    public DeliveryDetails save(DeliveryDetails deliveryDetails) {
        Optional<DeliveryDetailsPsql> existingDeliveryDetails = jpaDeliveryDetailsRepository.findById(deliveryDetails.getId());

        if (existingDeliveryDetails.isPresent()) {
            return existingDeliveryDetails.get().toEntity();
        } else {
            return jpaDeliveryDetailsRepository.save(new DeliveryDetailsPsql(deliveryDetails)).toEntity();
        }
    }

}

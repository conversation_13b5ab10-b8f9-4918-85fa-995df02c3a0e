package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.TransactionException;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.bill.domain.exceptions.DataIntegrityViolationException;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;

@RequiredArgsConstructor
public class PsqlRepository {

    private final TransactionTemplate transactionTemplate;

    public <T> T executeInTransactionOrThrow(TransactionCallback<T> action) throws TransactionException {
        try {
            return transactionTemplate.execute(action);
        } catch (org.springframework.dao.DataIntegrityViolationException e) {
            throw DataIntegrityViolationException.create(e);
        } catch (org.springframework.dao.EmptyResultDataAccessException e) {
            throw EntityNotFoundException.create(e);
        }
    }
}

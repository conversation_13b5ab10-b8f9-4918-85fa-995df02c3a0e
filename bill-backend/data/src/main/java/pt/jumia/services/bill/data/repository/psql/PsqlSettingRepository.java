package pt.jumia.services.bill.data.repository.psql;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.bill.data.entities.SettingPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaSettingRepository;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public class PsqlSettingRepository extends PsqlRepository implements SettingRepository {

    private final JpaSettingRepository jpaSettingRepository;

    @Autowired
    public PsqlSettingRepository(
            TransactionTemplate transactionTemplate,
            JpaSettingRepository jpaSettingRepository) {
        super(transactionTemplate);
        this.jpaSettingRepository = jpaSettingRepository;
    }

    @Override
    public Setting insert(Setting setting) {
        return executeInTransactionOrThrow(status -> jpaSettingRepository.save(new SettingPsql(setting.toBuilder()
                .createdAt(setting.getCreatedAt() == null ? LocalDateTime.now(ZoneOffset.UTC) : setting.getCreatedAt())
                .createdBy(setting.getCreatedBy() == null ? RequestContext.getUsername() : setting.getCreatedBy())
                .updatedAt(setting.getUpdatedAt() == null ? LocalDateTime.now(ZoneOffset.UTC) : setting.getUpdatedAt())
                .updatedBy(setting.getUpdatedBy() == null ? RequestContext.getUsername() : setting.getUpdatedBy())
                .build())).toEntity());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Setting> findById(long id) {
        return jpaSettingRepository.findById(id)
                .map(SettingPsql::toEntity);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Setting> findByProperty(String property) {
        List<SettingPsql> settingsPsql = jpaSettingRepository.findByProperty(property);
        return settingsPsql == null ? null : settingsPsql.stream()
                .map(SettingPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<Setting> findAll() {
        return jpaSettingRepository.findAll().stream()
                .map(SettingPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Setting update(long id, Setting setting) {
        return executeInTransactionOrThrow(status -> {
            Optional<SettingPsql> optSettingPsql = jpaSettingRepository.findById(id);

            if (optSettingPsql.isEmpty()) {
                throw EntityNotFoundException.createNotFound(Setting.class, id);
            }

            SettingPsql toUpdate = optSettingPsql.get();
            toUpdate.setProperty(setting.getProperty());
            toUpdate.setValue(setting.getValue());
            if (setting.getType() != null) {
                toUpdate.setType(setting.getType().name());
            }
            toUpdate.setDescription(setting.getDescription());
            toUpdate.setOverrideKey(setting.getOverrideKey());
            if (setting.getUpdatedBy() == null) {
                toUpdate.setUpdatedBy(RequestContext.getUsername());
            }
            if (setting.getUpdatedAt() == null) {
                toUpdate.setUpdatedAt(LocalDateTime.now(ZoneOffset.UTC));
            }

            return jpaSettingRepository.save(toUpdate).toEntity();
        });
    }

    @Override
    public void deleteById(long id) {
        executeInTransactionOrThrow(status -> {
            jpaSettingRepository.deleteById(id);
            return null;
        });
    }
}

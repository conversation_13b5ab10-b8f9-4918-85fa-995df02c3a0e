package pt.jumia.services.bill.data.utils;

import lombok.RequiredArgsConstructor;
import org.flywaydb.core.Flyway;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import pt.jumia.services.bill.domain.properties.DataProperties;
import pt.jumia.services.bill.domain.properties.SpringProperties;

import javax.sql.DataSource;

@Service
@RequiredArgsConstructor
public class MigrationService {

    private final SpringProperties springProperties;
    private final DataProperties dataProperties;

    public void migrateDatabaseSchema(DataSource dataSource, String schema) {
        Flyway flyway = createFlywayInstance(dataSource, schema);
        if (dataProperties.getDb().getFlyway().isRepair()) {
            flyway.repair();
        }
        flyway.migrate();
    }

    public void cleanDatabaseSchema(DataSource dataSource, String schema) {
        Flyway flyway = createFlywayInstance(dataSource, schema);
        if (dataProperties.getDb().getFlyway().isRepair()) {
            flyway.repair();
        }
        flyway.clean();
    }

    @NotNull
    private Flyway createFlywayInstance(DataSource dataSource, String schema) {
        String dir = springProperties.getFlyway().getLocations() + "/" + schema;
        return Flyway.configure()
                .dataSource(dataSource)
                .locations(dir)
                .schemas(schema)
                .baselineOnMigrate(true)
                .load();
    }
}

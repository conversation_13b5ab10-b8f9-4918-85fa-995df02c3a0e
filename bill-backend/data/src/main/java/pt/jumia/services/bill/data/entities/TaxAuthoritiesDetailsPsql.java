package pt.jumia.services.bill.data.entities;

import lombok.NoArgsConstructor;
import org.hibernate.envers.Audited;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@Entity
@Audited
@Table(name = "tax_authorities_details")
@NoArgsConstructor
public class TaxAuthoritiesDetailsPsql {

    @Id
    @Column(name = "id")
    private UUID id;

    @JoinColumn(name = "fk_document", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private DocumentPsql document;

    @Column(name = "submission_id")
    private String submissionId;

    @Column(name = "tax_document_number")
    private String taxDocumentNumber;

    @Column(name = "qr_code")
    private String qrCode;

    @Column(name = "verification_code")
    private String verificationCode;

    @Column(name = "device_number")
    private String deviceNumber;

    @Column(name = "internal_data")
    private String internalData;

    @Column(name = "status_code")
    private String statusCode;

    @Column(name = "error_code")
    private String errorCode;

    @Column(name = "exception_message")
    private String exception;

    //
    // Audit information
    //

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by")
    private String updatedBy;

    public TaxAuthoritiesDetailsPsql(TaxAuthoritiesDetails taxAuthoritiesDetails) {
        this.id = taxAuthoritiesDetails.getId();
        this.createdAt = taxAuthoritiesDetails.getCreatedAt();
        this.createdBy = taxAuthoritiesDetails.getCreatedBy();
        this.updatedAt = taxAuthoritiesDetails.getUpdatedAt();
        this.updatedBy = taxAuthoritiesDetails.getUpdatedBy();
        this.updateFrom(taxAuthoritiesDetails);
    }

    public final TaxAuthoritiesDetailsPsql updateFrom(TaxAuthoritiesDetails taxAuthoritiesDetails) {
        this.document = new DocumentPsql(taxAuthoritiesDetails.getDocument());
        return updateFromWithoutChangingDocument(taxAuthoritiesDetails);
    }

    public final TaxAuthoritiesDetailsPsql updateFromWithoutChangingDocument(TaxAuthoritiesDetails taxAuthoritiesDetails) {
        this.submissionId = taxAuthoritiesDetails.getSubmissionId();
        this.taxDocumentNumber = taxAuthoritiesDetails.getTaxDocumentNumber();
        this.qrCode = taxAuthoritiesDetails.getQrCode();
        this.verificationCode = taxAuthoritiesDetails.getVerificationCode();
        this.deviceNumber = taxAuthoritiesDetails.getDeviceNumber();
        this.internalData = taxAuthoritiesDetails.getInternalData();
        this.statusCode = taxAuthoritiesDetails.getStatusCode();
        this.errorCode = taxAuthoritiesDetails.getErrorCode();
        this.exception = taxAuthoritiesDetails.getException();

        return this;
    }

    public void updateAuditInformation() {
        LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);

        if (this.createdAt == null) {
            this.createdAt = now;
            this.createdBy = RequestContext.getUsername();
        }

        this.updatedAt = now;
        this.updatedBy = RequestContext.getUsername();
    }

    public TaxAuthoritiesDetails toEntity() {
        return TaxAuthoritiesDetails.builder()
                .id(id)
                .document(document.toEntity())
                .submissionId(submissionId)
                .taxDocumentNumber(taxDocumentNumber)
                .qrCode(qrCode)
                .verificationCode(verificationCode)
                .deviceNumber(deviceNumber)
                .internalData(internalData)
                .statusCode(statusCode)
                .errorCode(errorCode)
                .exception(exception)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }

}

package pt.jumia.services.bill.data.entities;

import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;
import pt.jumia.services.bill.domain.entities.Setting;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

@Entity
@Setter
@NoArgsConstructor
@Audited
@Table(name = "settings")
public class SettingPsql {

    @Id
    @SequenceGenerator(name = "settings_seq", sequenceName = "settings_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "settings_seq")
    @Column(name = "id")
    private Long id;

    @Column(name = "property", nullable = false, unique = true)
    private String property;

    @Column(name = "type", nullable = false)
    private String type;

    @Column(name = "override_key")
    private String overrideKey;

    @Column(name = "value", nullable = false)
    private String value;

    @Column(name = "description")
    private String description;

    @Column(name = "created_at", nullable = false, columnDefinition = "TIMESTAMP")
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_at", nullable = false, columnDefinition = "TIMESTAMP")
    private LocalDateTime updatedAt;

    @Column(name = "updated_by")
    private String updatedBy;

    public SettingPsql(Setting setting) {
        this.id = setting.getId();
        this.property = setting.getProperty();
        this.type = setting.getType().name();
        this.overrideKey = setting.getOverrideKey();
        this.value = setting.getValue();
        this.description = setting.getDescription();
        this.createdAt = Objects.isNull(setting.getCreatedAt()) ?
                LocalDateTime.now(ZoneOffset.UTC) : setting.getCreatedAt();
        this.createdBy = setting.getCreatedBy();
        this.updatedAt = Objects.isNull(setting.getUpdatedAt()) ?
                LocalDateTime.now(ZoneOffset.UTC) : setting.getUpdatedAt();
        this.updatedBy = setting.getUpdatedBy();
    }

    public Setting toEntity() {
        return Setting
                .builder()
                .id(id)
                .property(property)
                .type(Setting.Type.valueOf(type))
                .overrideKey(overrideKey)
                .value(value)
                .description(description)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}

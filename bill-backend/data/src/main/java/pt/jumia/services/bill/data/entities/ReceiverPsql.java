package pt.jumia.services.bill.data.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@Entity
@Table(name = "receivers")
@NoArgsConstructor
@Getter
@Setter
public class ReceiverPsql {

    //
    // Internal fields
    //

    @Id
    @Column(name = "id")
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private ReceiverType type;

    //
    // Identification
    //

    @Column(name = "legal_name")
    private String legalName;

    @Column(name = "name")
    private String name;

    @Column(name = "national_identification_number")
    private String nationalIdentificationNumber;

    @Column(name = "tax_identification_number")
    private String taxIdentificationNumber;

    @Column(name = "business_identification_number")
    private String businessRegistrationNumber;

    //
    // Contact
    //

    @Column(name = "email")
    private String email;

    @Column(name = "mobile_phone")
    private String mobilePhone;

    @Column(name = "line_phone")
    private String linePhone;

    //
    // Address
    //

    @Column(name = "country")
    private String country;

    @Column(name = "region")
    private String region;

    @Column(name = "city")
    private String city;

    @Column(name = "street")
    private String street;

    @Column(name = "building_number")
    private String buildingNumber;

    @Column(name = "floor")
    private String floor;

    @Column(name = "postal_code")
    private String postalCode;

    @Column(name = "address_additional_information")
    private String addressAdditionalInformation;

    //
    // Audit information
    //

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    public ReceiverPsql(Receiver receiver) {
        this.id = receiver.getId();
        this.createdAt = receiver.getCreatedAt();
        this.createdBy = receiver.getCreatedBy();

        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now(ZoneOffset.UTC);
            this.createdBy = RequestContext.getUsername();
        }

        this.updateFrom(receiver);

    }

    public final ReceiverPsql updateFrom(Receiver receiver) {
        this.type = receiver.getType();
        this.legalName = receiver.getLegalName();
        this.name = receiver.getName();
        this.nationalIdentificationNumber = receiver.getNationalIdentificationNumber();
        this.taxIdentificationNumber = receiver.getTaxIdentificationNumber();
        this.businessRegistrationNumber = receiver.getBusinessRegistrationNumber();
        this.email = receiver.getEmail();
        this.mobilePhone = receiver.getMobilePhone();
        this.linePhone = receiver.getLinePhone();

        Address address = receiver.getAddress();
        if (address != null) {
            this.country = address.getCountry() != null ? address.getCountry().getAlpha2() : null;
            this.region = address.getRegion();
            this.city = address.getCity();
            this.street = address.getStreet();
            this.buildingNumber = address.getBuildingNumber();
            this.floor = address.getFloor();
            this.postalCode = address.getPostalCode();
            this.addressAdditionalInformation = address.getAdditionalInformation();
        }

        return this;
    }

    public Receiver toEntity() {
        return Receiver.builder()
                .id(id)
                .type(type)
                .legalName(legalName)
                .name(name)
                .nationalIdentificationNumber(nationalIdentificationNumber)
                .taxIdentificationNumber(taxIdentificationNumber)
                .businessRegistrationNumber(businessRegistrationNumber)
                .address(noAddressFields()
                        ? null
                        : Address.builder()
                        .country(country != null ? CountryCode.getByAlpha2Code(country) : null)
                        .region(region)
                        .city(city)
                        .street(street)
                        .buildingNumber(buildingNumber)
                        .floor(floor)
                        .postalCode(postalCode)
                        .additionalInformation(addressAdditionalInformation)
                        .build())
                .email(email)
                .mobilePhone(mobilePhone)
                .linePhone(linePhone)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .build();
    }

    private boolean noAddressFields() {
        return country == null
                && region == null
                && city == null
                && street == null
                && buildingNumber == null
                && floor == null
                && postalCode == null
                && addressAdditionalInformation == null;
    }

}

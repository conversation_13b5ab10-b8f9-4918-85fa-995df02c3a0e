package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.TaxAuthoritiesDetailsPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaTaxAuthoritiesDetailsRepository;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.repository.TaxAuthoritiesDetailsRepository;

import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class PsqlTaxAuthoritiesDetailsRepository implements TaxAuthoritiesDetailsRepository {

    private final JpaTaxAuthoritiesDetailsRepository jpaTaxAuthoritiesDetailsRepository;

    @Override
    @Transactional
    public Optional<TaxAuthoritiesDetails> findById(UUID id) {
        return jpaTaxAuthoritiesDetailsRepository.findById(id)
                .map(TaxAuthoritiesDetailsPsql::toEntity);
    }

    @Override
    @Transactional
    public Optional<TaxAuthoritiesDetails> findByDocumentId(UUID id) {
        return jpaTaxAuthoritiesDetailsRepository.findByDocumentId(id)
                .map(TaxAuthoritiesDetailsPsql::toEntity);
    }

    @Override
    @Transactional
    public TaxAuthoritiesDetails save(TaxAuthoritiesDetails taxAuthoritiesDetails) {
        TaxAuthoritiesDetailsPsql taxAuthoritiesDetailsToSave = jpaTaxAuthoritiesDetailsRepository.findById(taxAuthoritiesDetails.getId())
                .map(taxAuthoritiesDetailsPsql -> taxAuthoritiesDetailsPsql.updateFrom(taxAuthoritiesDetails))
                .orElseGet(() -> new TaxAuthoritiesDetailsPsql(taxAuthoritiesDetails));

        taxAuthoritiesDetailsToSave.updateAuditInformation();

        return jpaTaxAuthoritiesDetailsRepository.save(taxAuthoritiesDetailsToSave).toEntity();
    }
}

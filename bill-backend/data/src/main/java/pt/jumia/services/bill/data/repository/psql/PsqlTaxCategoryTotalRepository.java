package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.TaxCategoryTotalPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaTaxCategoryTotalRepository;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.repository.TaxCategoryTotalRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlTaxCategoryTotalRepository implements TaxCategoryTotalRepository {

    private final JpaTaxCategoryTotalRepository jpaTaxCategoryTotalRepository;

    @Override
    @Transactional
    public Optional<TaxCategoryTotal> findById(UUID id) {
        return jpaTaxCategoryTotalRepository.findById(id)
                .map(TaxCategoryTotalPsql::toEntity);
    }

    @Override
    @Transactional
    public List<TaxCategoryTotal> findByDocumentId(UUID id) {
        return jpaTaxCategoryTotalRepository.findByDocumentId(id).stream()
                .map(TaxCategoryTotalPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TaxCategoryTotal save(TaxCategoryTotal taxCategoryTotal) {
        TaxCategoryTotalPsql savedEntity = jpaTaxCategoryTotalRepository.save(new TaxCategoryTotalPsql(taxCategoryTotal));
        return savedEntity.toEntity();
    }
}

package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.TaxCategoryTotalPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaTaxCategoryTotalRepository;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.repository.TaxCategoryTotalRepository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlTaxCategoryTotalRepository implements TaxCategoryTotalRepository {

    private final JpaTaxCategoryTotalRepository jpaTaxCategoryTotalRepository;
    private final EntityManager entityManager;

    @Override
    @Transactional
    public Optional<TaxCategoryTotal> findById(UUID id) {
        return jpaTaxCategoryTotalRepository.findById(id)
                .map(TaxCategoryTotalPsql::toEntity);
    }

    @Override
    @Transactional
    public List<TaxCategoryTotal> findByDocumentId(UUID id) {
        return jpaTaxCategoryTotalRepository.findByDocumentId(id).stream()
                .map(TaxCategoryTotalPsql::toEntity)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TaxCategoryTotal save(TaxCategoryTotal taxCategoryTotal) {
        TaxCategoryTotalPsql savedEntity = jpaTaxCategoryTotalRepository.save(new TaxCategoryTotalPsql(taxCategoryTotal));
        return savedEntity.toEntity();
    }

    @Override
    @Transactional
    public int migrateDecimalFields(Long startId, Long endId) {
        String taxCategoryTotalsQuery = "UPDATE tax_category_totals SET " +
                "tax_rate_new = tax_rate, " +
                "tax_fixed_amount_new = tax_fixed_amount, " +
                "total_amount_new = total_amount, " +
                "net_amount_new = net_amount, " +
                "tax_amount_new = tax_amount " +
                "WHERE fk_document IN (SELECT id FROM documents WHERE id BETWEEN :startId AND :endId)";

        Query query = entityManager.createNativeQuery(taxCategoryTotalsQuery);
        query.setParameter("startId", startId);
        query.setParameter("endId", endId);
        return query.executeUpdate();
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getDecimalFieldsMigrationStatus() {
        String taxCategoryTotalsStatusQuery = "SELECT " +
                "COUNT(*) as total, " +
                "SUM(CASE WHEN total_amount_new = total_amount AND net_amount_new = net_amount THEN 1 ELSE 0 END) as migrated " +
                "FROM tax_category_totals";

        Query query = entityManager.createNativeQuery(taxCategoryTotalsStatusQuery);
        Object[] taxCategoryTotalsResult = (Object[]) query.getSingleResult();
        Map<String, Object> taxCategoryTotalsStatus = new HashMap<>();
        taxCategoryTotalsStatus.put("total", taxCategoryTotalsResult[0]);
        taxCategoryTotalsStatus.put("migrated", taxCategoryTotalsResult[1]);
        taxCategoryTotalsStatus.put("percentage", calculatePercentage((Long) taxCategoryTotalsResult[1], (Long) taxCategoryTotalsResult[0]));
        return taxCategoryTotalsStatus;
    }

    private double calculatePercentage(Long migrated, Long total) {
        if (total == 0) {
            return 0.0;
        }
        return (double) migrated / total * 100.0;
    }
}

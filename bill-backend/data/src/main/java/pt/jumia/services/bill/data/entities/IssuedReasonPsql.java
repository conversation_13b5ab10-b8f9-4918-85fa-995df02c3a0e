package pt.jumia.services.bill.data.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.DeliveryDetails;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@Entity
@Table(name = "issued_reason")
@NoArgsConstructor
public class IssuedReasonPsql {

    @Id
    @Column(name = "id")
    private UUID id;

    @Column(name = "code")
    private String code;

    @Column(name = "notes")
    private String notes;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    public IssuedReasonPsql(IssuedReason issuedReason) {
        this.id = issuedReason.getId();
        this.createdAt = issuedReason.getCreatedAt();
        this.createdBy = issuedReason.getCreatedBy();

        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now(ZoneOffset.UTC);
            this.createdBy = RequestContext.getUsername();
        }

        this.updateFrom(issuedReason);
    }

    public final IssuedReasonPsql updateFrom(IssuedReason issuedReason) {
        this.code = issuedReason.getCode() == null ? null :
                issuedReason.getCode().name();
        this.notes = issuedReason.getNotes();
        return this;
    }

    public IssuedReason toEntity() {
        return IssuedReason.builder()
                .id(id)
                .code(code == null ? null : IssuedReasonCode.valueOf(code))
                .notes(notes)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .build();
    }

}

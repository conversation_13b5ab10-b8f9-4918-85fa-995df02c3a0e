package pt.jumia.services.bill.data.repository.events.channels;

import com.impossibl.postgres.jdbc.PGDataSource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.data.repository.psql.PsqlSettingRepository;
import pt.jumia.services.bill.domain.properties.DataProperties;
import pt.jumia.services.bill.domain.usecases.settings.ReloadSettingsUseCase;

import java.sql.SQLException;

@Component
@RequiredArgsConstructor
public class PsqlChannelFactory {

    private final PGDataSource pgDataSource;
    private final PsqlSettingRepository settingRepository;
    private final ReloadSettingsUseCase reloadSettingsUseCase;
    private final DataProperties dataProperties;

    public PsqlSettingsChangeChannel createSettingsChangeChannel() throws SQLException {
        return new PsqlSettingsChangeChannel(
                pgDataSource, settingRepository, reloadSettingsUseCase, (int) dataProperties.getEvents().getCheckConnectionTimeout().toMillis());
    }
}

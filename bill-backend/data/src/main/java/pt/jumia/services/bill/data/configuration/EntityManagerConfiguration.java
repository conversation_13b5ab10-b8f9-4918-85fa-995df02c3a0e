package pt.jumia.services.bill.data.configuration;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.RequiredArgsConstructor;
import org.hibernate.dialect.PostgreSQL9Dialect;
import org.hibernate.jpa.HibernatePersistenceProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import pt.jumia.services.bill.data.utils.MigrationService;
import pt.jumia.services.bill.domain.properties.DataProperties;
import pt.jumia.services.bill.domain.properties.SpringProperties;

import javax.persistence.EntityManagerFactory;
import javax.persistence.spi.PersistenceUnitTransactionType;
import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@RequiredArgsConstructor
@EnableTransactionManagement(proxyTargetClass = true)
public class EntityManagerConfiguration {

    private final SpringProperties springProperties;
    private final DataProperties dataProperties;
    private final MigrationService migrationService;

    @Bean
    public JpaTransactionManager transactionManager(EntityManagerFactory emf) {
        return new JpaTransactionManager(emf);
    }

    @Bean
    public LocalContainerEntityManagerFactoryBean entityManagerFactory() {
        // setup database
        migrateApplicationSchemas();

        LocalContainerEntityManagerFactoryBean fact = new LocalContainerEntityManagerFactoryBean();
        fact.setPackagesToScan("pt.jumia.services.bill.data.entities");
        fact.setDataSource(dataSource());

        HibernateJpaVendorAdapter vendor = new HibernateJpaVendorAdapter();
        vendor.setDatabase(Database.POSTGRESQL);

        fact.setJpaVendorAdapter(vendor);
        fact.setPersistenceProvider(new HibernatePersistenceProvider());

        final Properties properties = new Properties();
        additionalProperties(properties);
        fact.setJpaProperties(properties);

        return fact;
    }

    @Bean
    public DataSource dataSource() {

        HikariConfig config = new HikariConfig();
        config.setDriverClassName(dataProperties.getDb().getDriver());
        config.setJdbcUrl(dataProperties.getDb().getUrl());
        config.setUsername(dataProperties.getDb().getUsername());
        config.setPassword(dataProperties.getDb().getPassword());
        config.setMaximumPoolSize(dataProperties.getDb().getMaxPoolSize());

        return new HikariDataSource(config);
    }

    private void additionalProperties(Properties properties) {
        properties.put("hibernate.dialect", PostgreSQL9Dialect.class.getName());
        properties.put("hibernate.default_schema", dataProperties.getDb().getApplicationSchema());
        properties.put("hibernate.show_sql", "false");
        properties.put("hibernate.format_sql", "false");
        properties.put("hibernate.transaction.auto_close_session", "true");
        properties.put("hibernate.use_sql_comments", "false");
        properties.put("hibernate.jdbc.lob.non_contextual_creation", "true");
        properties.put("hibernate.connection.release_mode", "auto");
        properties.put("org.hibernate.envers.store_data_at_delete", "true");
        properties.put("org.hibernate.envers.default_schema", dataProperties.getDb().getAuditSchema());
        properties.put("javax.persistence.transactionType", PersistenceUnitTransactionType.RESOURCE_LOCAL.name());
    }

    private void migrateApplicationSchemas() {
        for (String schemaDir : springProperties.getFlyway().getSchemas()) {
            migrationService.migrateDatabaseSchema(dataSource(), schemaDir);
        }
    }
}

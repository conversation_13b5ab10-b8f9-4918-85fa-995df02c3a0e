package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.ReceiverPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaReceiverRepository;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.repository.ReceiverRepository;

import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class PsqlReceiverRepository implements ReceiverRepository {

    private final JpaReceiverRepository jpaReceiverRepository;

    @Override
    @Transactional
    public Optional<Receiver> findById(UUID id) {
        return jpaReceiverRepository.findById(id).map(ReceiverPsql::toEntity);
    }

    @Override
    @Transactional
    public Receiver save(Receiver receiver) {
        Optional<ReceiverPsql> existingReceiver = jpaReceiverRepository.findById(receiver.getId());

        if (existingReceiver.isPresent()) {
            return existingReceiver.get().toEntity();
        } else {
            return jpaReceiverRepository.save(new ReceiverPsql(receiver)).toEntity();
        }
    }

}

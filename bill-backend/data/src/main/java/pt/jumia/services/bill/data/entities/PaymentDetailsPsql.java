package pt.jumia.services.bill.data.entities;

import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.PaymentDetails;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@Entity
@Table(name = "payment_details")
@NoArgsConstructor
public class PaymentDetailsPsql {

    @Id
    @Column(name = "id")
    private UUID id;

    @Column(name = "bank_name")
    private String bankName;

    @Column(name = "bank_address")
    private String bankAddress;

    @Column(name = "bank_account_no")
    private String bankAccountNo;

    @Column(name = "bank_account_iban")
    private String bankAccountIBAN;

    @Column(name = "swift_code")
    private String swiftCode;

    @Column(name = "terms")
    private String terms;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    public PaymentDetailsPsql(PaymentDetails paymentDetails) {
        this.id = paymentDetails.getId();
        this.createdAt = paymentDetails.getCreatedAt();
        this.createdBy = paymentDetails.getCreatedBy();

        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now(ZoneOffset.UTC);
            this.createdBy = RequestContext.getUsername();
        }

        this.updateFrom(paymentDetails);
    }

    public final PaymentDetailsPsql updateFrom(PaymentDetails paymentDetails) {
        this.bankName = paymentDetails.getBankName();
        this.bankAddress = paymentDetails.getBankAddress();
        this.bankAccountNo = paymentDetails.getBankAccountNo();
        this.bankAccountIBAN = paymentDetails.getBankAccountIBAN();
        this.swiftCode = paymentDetails.getSwiftCode();
        this.terms = paymentDetails.getTerms();

        return this;
    }

    public PaymentDetails toEntity() {
        return PaymentDetails.builder()
                .id(id)
                .bankName(bankName)
                .bankAddress(bankAddress)
                .bankAccountNo(bankAccountNo)
                .bankAccountIBAN(bankAccountIBAN)
                .swiftCode(swiftCode)
                .terms(terms)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .build();
    }


}

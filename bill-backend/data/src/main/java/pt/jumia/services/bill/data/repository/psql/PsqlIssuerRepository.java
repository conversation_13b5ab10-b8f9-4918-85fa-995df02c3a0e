package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.IssuerPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaIssuerRepository;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.repository.IssuerRepository;

import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class PsqlIssuerRepository implements IssuerRepository {

    private final JpaIssuerRepository jpaIssuerRepository;

    @Override
    @Transactional
    public Optional<Issuer> findById(UUID id) {
        return jpaIssuerRepository.findById(id).map(IssuerPsql::toEntity);
    }

    @Override
    public Issuer save(Issuer issuer) {
        Optional<IssuerPsql> existingIssuer = jpaIssuerRepository.findById(issuer.getId());

        if (existingIssuer.isPresent()) {
            return existingIssuer.get().toEntity();
        } else {
            return jpaIssuerRepository.save(new IssuerPsql(issuer)).toEntity();
        }
    }

}

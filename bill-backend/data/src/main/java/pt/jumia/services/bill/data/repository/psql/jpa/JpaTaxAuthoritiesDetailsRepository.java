package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.QTaxAuthoritiesDetailsPsql;
import pt.jumia.services.bill.data.entities.TaxAuthoritiesDetailsPsql;

import java.util.Optional;
import java.util.UUID;

public interface JpaTaxAuthoritiesDetailsRepository extends JpaRepository<TaxAuthoritiesDetailsPsql, UUID>,
        QuerydslPredicateExecutor<QTaxAuthoritiesDetailsPsql> {

    Optional<TaxAuthoritiesDetailsPsql> findByDocumentId(UUID uuid);

}

package pt.jumia.services.bill.data.entities;

import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.math.BigDecimal;
import java.util.UUID;

@Entity
@Table(name = "tax_category_totals", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"fk_document", "tax_rate", "tax_fixed_amount"})
})
@NoArgsConstructor
public class TaxCategoryTotalPsql {

    //
    // Internal fields
    //

    @Id
    @Column(name = "id")
    private UUID id;

    @JoinColumn(name = "fk_document", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private DocumentPsql document;

    //
    // Tax information
    //

    @Enumerated(EnumType.STRING)
    @Column(name = "tax_category", nullable = false)
    private TaxCategory taxCategory;

    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @Column(name = "tax_rate_new")
    private BigDecimal taxRateNew;

    @Column(name = "tax_fixed_amount")
    private BigDecimal taxFixedAmount;

    @Column(name = "tax_fixed_amount_new")
    private BigDecimal taxFixedAmountNew;

    //
    // Summary
    //

    @Column(name = "total_amount", nullable = false)
    private BigDecimal totalAmount;

    @Column(name = "total_amount_new", nullable = false)
    private BigDecimal totalAmountNew;

    @Column(name = "net_amount", nullable = false)
    private BigDecimal netAmount;

    @Column(name = "net_amount_new", nullable = false)
    private BigDecimal netAmountNew;

    @Column(name = "tax_amount", nullable = false)
    private BigDecimal taxAmount;

    @Column(name = "tax_amount_new", nullable = false)
    private BigDecimal taxAmountNew;

    public TaxCategoryTotalPsql(TaxCategoryTotal taxCategoryTotal) {
        this.id = taxCategoryTotal.getId();
        this.document = new DocumentPsql(taxCategoryTotal.getDocument());
        this.taxCategory = taxCategoryTotal.getTaxCategory();
        this.taxRate = taxCategoryTotal.getTaxRate();
        this.taxRateNew = taxCategoryTotal.getTaxRate();
        this.taxFixedAmount = taxCategoryTotal.getTaxFixedAmount();
        this.taxFixedAmountNew = taxCategoryTotal.getTaxFixedAmount();
        this.totalAmount = taxCategoryTotal.getTotalAmount();
        this.totalAmountNew = taxCategoryTotal.getTotalAmount();
        this.netAmount = taxCategoryTotal.getNetAmount();
        this.netAmountNew = taxCategoryTotal.getNetAmount();
        this.taxAmount = taxCategoryTotal.getTaxAmount();
        this.taxAmountNew = taxCategoryTotal.getTaxAmount();
    }

    public TaxCategoryTotal toEntity() {
        return TaxCategoryTotal.builder()
                .id(id)
                .document(this.document.toEntity())
                .taxCategory(taxCategory)
                .taxRate(taxRateNew != null ? taxRateNew : taxRate)
                .taxFixedAmount(taxFixedAmountNew != null ? taxFixedAmountNew : taxFixedAmount)
                .totalAmount(totalAmountNew != null ? totalAmountNew : totalAmount)
                .netAmount(netAmountNew != null ? netAmountNew : netAmount)
                .taxAmount(taxAmountNew != null ? taxAmountNew : taxAmount)
                .build();
    }

}

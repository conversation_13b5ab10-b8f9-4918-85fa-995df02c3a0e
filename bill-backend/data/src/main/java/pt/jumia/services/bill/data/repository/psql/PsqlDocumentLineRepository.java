package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.DocumentLinePsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentLineRepository;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.repository.DocumentLineRepository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlDocumentLineRepository implements DocumentLineRepository {

    private final JpaDocumentLineRepository jpaDocumentLineRepository;
    private final EntityManager entityManager;

    @Override
    @Transactional
    public Optional<DocumentLine> findById(UUID id) {
        return jpaDocumentLineRepository.findById(id)
                .map(DocumentLinePsql::toEntity);
    }

    @Override
    @Transactional
    public List<DocumentLine> findByDocumentId(UUID id) {
        return jpaDocumentLineRepository.findByDocumentId(id).stream()
                .map(DocumentLinePsql::toEntity)
                .sorted(Comparator.comparingInt(DocumentLine::getPosition))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DocumentLine save(DocumentLine documentLine) {
        DocumentLinePsql savedEntity = jpaDocumentLineRepository.save(new DocumentLinePsql(documentLine));
        return savedEntity.toEntity();
    }

    @Override
    @Transactional
    public int migrateDecimalFields(Long startId, Long endId) {
        String documentLinesQuery = "UPDATE document_lines SET " +
                "quantity_new = quantity, " +
                "unit_price_new = unit_price, " +
                "total_amount_new = total_amount, " +
                "net_amount_new = net_amount, " +
                "total_tax_amount_new = total_tax_amount, " +
                "discount_amount_new = discount_amount, " +
                "discount_rate_new = discount_rate " +
                "WHERE fk_document IN (SELECT id FROM documents WHERE id BETWEEN :startId AND :endId)";

        Query query = entityManager.createNativeQuery(documentLinesQuery);
        query.setParameter("startId", startId);
        query.setParameter("endId", endId);
        return query.executeUpdate();
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getDecimalFieldsMigrationStatus() {
        String documentLinesStatusQuery = "SELECT " +
                "COUNT(*) as total, " +
                "SUM(CASE WHEN quantity_new = quantity AND unit_price_new = unit_price THEN 1 ELSE 0 END) as migrated " +
                "FROM document_lines";

        Query query = entityManager.createNativeQuery(documentLinesStatusQuery);
        Object[] documentLinesResult = (Object[]) query.getSingleResult();
        Map<String, Object> documentLinesStatus = new HashMap<>();
        documentLinesStatus.put("total", documentLinesResult[0]);
        documentLinesStatus.put("migrated", documentLinesResult[1]);
        documentLinesStatus.put("percentage", calculatePercentage((Long) documentLinesResult[1], (Long) documentLinesResult[0]));
        return documentLinesStatus;
    }

    private double calculatePercentage(Long migrated, Long total) {
        if (total == 0) {
            return 0.0;
        }
        return (double) migrated / total * 100.0;
    }
}

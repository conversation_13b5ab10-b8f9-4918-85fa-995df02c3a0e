package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.DocumentLinePsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentLineRepository;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.repository.DocumentLineRepository;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlDocumentLineRepository implements DocumentLineRepository {

    private final JpaDocumentLineRepository jpaDocumentLineRepository;

    @Override
    @Transactional
    public Optional<DocumentLine> findById(UUID id) {
        return jpaDocumentLineRepository.findById(id)
                .map(DocumentLinePsql::toEntity);
    }

    @Override
    @Transactional
    public List<DocumentLine> findByDocumentId(UUID id) {
        return jpaDocumentLineRepository.findByDocumentId(id).stream()
                .map(DocumentLinePsql::toEntity)
                .sorted(Comparator.comparingInt(DocumentLine::getPosition))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DocumentLine save(DocumentLine documentLine) {
        DocumentLinePsql savedEntity = jpaDocumentLineRepository.save(new DocumentLinePsql(documentLine));
        return savedEntity.toEntity();
    }
}

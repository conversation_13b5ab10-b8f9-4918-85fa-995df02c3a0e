package pt.jumia.services.bill.data.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.IssuerType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

@Entity
@Table(name = "issuer_template")
@NoArgsConstructor
public class IssuerTemplatePsql {

    //
    // Internal fields
    //

    @Id
    @SequenceGenerator(name = "issuer_template_seq", sequenceName = "issuer_template_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "issuer_template_seq")
    @Column(name = "id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private IssuerType type;

    @Column(name = "shop")
    private String shop;

    //
    // Identification
    //

    @Column(name = "legal_name")
    private String legalName;

    @Column(name = "name")
    private String name;

    @Column(name = "tax_identification_number")
    private String taxIdentificationNumber;

    @Column(name = "business_identification_number")
    private String businessRegistrationNumber;

    //
    // Contact
    //

    @Column(name = "email")
    private String email;

    @Column(name = "mobile_phone")
    private String mobilePhone;

    @Column(name = "line_phone")
    private String linePhone;

    //
    // Address
    //

    @Column(name = "country")
    private String country;

    @Column(name = "region")
    private String region;

    @Column(name = "city")
    private String city;

    @Column(name = "street")
    private String street;

    @Column(name = "building_number")
    private String buildingNumber;

    @Column(name = "floor")
    private String floor;

    @Column(name = "postal_code")
    private String postalCode;

    @Column(name = "address_additional_information")
    private String addressAdditionalInformation;

    @Column(name = "branch")
    private String branch;

    //
    // Audit information
    //

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "updated_by")
    private String updatedBy;

    public IssuerTemplatePsql(IssuerTemplate issuerTemplate) {
        this.id = issuerTemplate.getId();
        this.type = issuerTemplate.getType();
        this.shop = issuerTemplate.getShop();
        this.legalName = issuerTemplate.getLegalName();
        this.name = issuerTemplate.getName();
        this.taxIdentificationNumber = issuerTemplate.getTaxIdentificationNumber();
        this.businessRegistrationNumber = issuerTemplate.getBusinessRegistrationNumber();
        this.email = issuerTemplate.getEmail();
        this.mobilePhone = issuerTemplate.getMobilePhone();
        this.linePhone = issuerTemplate.getLinePhone();
        this.branch = issuerTemplate.getBranch();

        Address address = issuerTemplate.getAddress();
        if (address != null) {
            this.country = address.getCountry() != null ? address.getCountry().getAlpha2() : null;
            this.region = address.getRegion();
            this.city = address.getCity();
            this.street = address.getStreet();
            this.buildingNumber = address.getBuildingNumber();
            this.floor = address.getFloor();
            this.postalCode = address.getPostalCode();
            this.addressAdditionalInformation = address.getAdditionalInformation();
        }
        this.createdAt = Objects.isNull(issuerTemplate.getCreatedAt()) ?
                LocalDateTime.now(ZoneOffset.UTC) : issuerTemplate.getCreatedAt();
        this.createdBy = issuerTemplate.getCreatedBy() == null ? RequestContext.getUsername() : issuerTemplate.getCreatedBy();
        this.updatedAt = Objects.isNull(issuerTemplate.getUpdatedAt()) ?
                LocalDateTime.now(ZoneOffset.UTC) : issuerTemplate.getUpdatedAt();
        this.updatedBy = issuerTemplate.getUpdatedBy() == null ? RequestContext.getUsername() : issuerTemplate.getUpdatedBy();
    }

    public IssuerTemplate toEntity() {
        return IssuerTemplate.builder()
                .id(id)
                .type(type)
                .shop(shop)
                .legalName(legalName)
                .name(name)
                .taxIdentificationNumber(taxIdentificationNumber)
                .businessRegistrationNumber(businessRegistrationNumber)
                .address(noAddressFields()
                        ? null
                        : Address.builder()
                        .country(country != null ? CountryCode.getByAlpha2Code(country) : null)
                        .region(region)
                        .city(city)
                        .street(street)
                        .buildingNumber(buildingNumber)
                        .floor(floor)
                        .postalCode(postalCode)
                        .additionalInformation(addressAdditionalInformation)
                        .build())
                .email(email)
                .mobilePhone(mobilePhone)
                .linePhone(linePhone)
                .branch(branch)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }

    private boolean noAddressFields() {
        return country == null
                && region == null
                && city == null
                && street == null
                && buildingNumber == null
                && floor == null
                && postalCode == null
                && addressAdditionalInformation == null;
    }

}

package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.DeliveryDetailsPsql;
import pt.jumia.services.bill.data.entities.QDeliveryDetailsPsql;

import java.util.UUID;

public interface JpaDeliveryDetailsRepository extends JpaRepository<DeliveryDetailsPsql, UUID>, QuerydslPredicateExecutor<QDeliveryDetailsPsql> {

}

package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.QTaxCategoryTotalPsql;
import pt.jumia.services.bill.data.entities.TaxCategoryTotalPsql;

import java.util.List;
import java.util.UUID;

public interface JpaTaxCategoryTotalRepository extends JpaRepository<TaxCategoryTotalPsql, UUID>, QuerydslPredicateExecutor<QTaxCategoryTotalPsql> {

    List<TaxCategoryTotalPsql> findByDocumentId(UUID uuid);
}

package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.IssuerPsql;
import pt.jumia.services.bill.data.entities.QIssuerPsql;

import java.util.UUID;

public interface JpaIssuerRepository extends JpaRepository<IssuerPsql, UUID>, QuerydslPredicateExecutor<QIssuerPsql> {

}

package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.IssuedReasonPsql;
import pt.jumia.services.bill.data.entities.QIssuedReasonPsql;

import java.util.UUID;

public interface JpaIssuedReasonRepository extends JpaRepository<IssuedReasonPsql, UUID>, QuerydslPredicateExecutor<QIssuedReasonPsql> {

}

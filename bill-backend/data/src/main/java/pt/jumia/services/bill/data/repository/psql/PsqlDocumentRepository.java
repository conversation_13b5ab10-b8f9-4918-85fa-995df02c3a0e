package pt.jumia.services.bill.data.repository.psql;

import com.querydsl.core.types.ExpressionUtils;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.bill.data.entities.DocumentPsql;
import pt.jumia.services.bill.data.entities.QDeliveryDetailsPsql;
import pt.jumia.services.bill.data.entities.QDocumentPsql;
import pt.jumia.services.bill.data.entities.QDocumentTransformationPsql;
import pt.jumia.services.bill.data.entities.QIssuerPsql;
import pt.jumia.services.bill.data.entities.QPaymentDetailsPsql;
import pt.jumia.services.bill.data.entities.QReceiverPsql;
import pt.jumia.services.bill.data.entities.QTaxAuthoritiesDetailsPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentRepository;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.OrderDirection;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DeliveryDetailsRepository;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.IssuedReasonRepository;
import pt.jumia.services.bill.domain.repository.IssuerRepository;
import pt.jumia.services.bill.domain.repository.PaymentDetailsRepository;
import pt.jumia.services.bill.domain.repository.ReceiverRepository;

import javax.persistence.EntityManager;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class PsqlDocumentRepository implements DocumentRepository {

    private final ReceiverRepository receiverRepository;
    private final IssuerRepository issuerRepository;
    private final DeliveryDetailsRepository deliveryDetailsRepository;
    private final PaymentDetailsRepository paymentDetailsRepository;
    private final IssuedReasonRepository issuedReasonRepository;

    private final EntityManager entityManager;
    private final JpaDocumentRepository jpaDocumentRepository;

    private final QTaxAuthoritiesDetailsPsql qTaxAuthoritiesDetails = QTaxAuthoritiesDetailsPsql.taxAuthoritiesDetailsPsql;
    private final QDocumentPsql qDocument = QDocumentPsql.documentPsql;
    private final QIssuerPsql qIssuerPsql = QIssuerPsql.issuerPsql;
    private final QReceiverPsql qReceiverPsql = QReceiverPsql.receiverPsql;
    private final QDocumentTransformationPsql qDocumentTransformation =
            QDocumentTransformationPsql.documentTransformationPsql;

    @Override
    public List<Document> findAll(DocumentFilter filter) {
        return findAll(filter, null, null);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Document> findAll(DocumentFilter filter,
                                  DocumentSortFilters sortFilters,
                                  PageFilters pageFilters) {
        JPAQuery<DocumentPsql> query = new JPAQueryFactory(entityManager).selectFrom(qDocument);

        buildJoins(filter, query);
        buildWhereClauses(filter, query);

        if (sortFilters != null) {
            String field = DocumentPsql.ENTITY_FIELDS.get(sortFilters.getField());
            if (OrderDirection.ASC.equals(sortFilters.getDirection())) {
                query.orderBy(new OrderSpecifier(Order.ASC, ExpressionUtils.path(DocumentPsql.class, qDocument, field)));
            } else if (OrderDirection.DESC.equals(sortFilters.getDirection())) {
                query.orderBy(new OrderSpecifier(Order.DESC, ExpressionUtils.path(DocumentPsql.class, qDocument, field)));
            } else {
                throw new IllegalArgumentException("Order must be either asc or desc");
            }
        }

        if (pageFilters != null) {
            query.offset((pageFilters.getPage() - 1) * pageFilters.getSize())
                    .limit(pageFilters.getSize() + 1);
        }

        return query.fetch().stream().map(documentPsql -> documentPsql.toEntity(filter.getInclude())).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public long count(DocumentFilter filter) {
        JPAQuery<DocumentPsql> query = new JPAQueryFactory(entityManager).selectFrom(qDocument);

        buildJoins(filter, query);
        buildWhereClauses(filter, query);

        return query.fetchCount();
    }

    private void buildJoins(DocumentFilter filter, JPAQuery<DocumentPsql> query) {
        List<Document.Details> include = filter.getInclude();

        if (include.contains(Document.Details.RECEIVER)) {
            query.join(qDocument.receiver, QReceiverPsql.receiverPsql).fetchJoin();
        }

        if (include.contains(Document.Details.ISSUER)) {
            query.join(qDocument.issuer, QIssuerPsql.issuerPsql).fetchJoin();
        }

        if (include.contains(Document.Details.TAX_AUTHORITIES_DETAILS)) {
            query.leftJoin(qTaxAuthoritiesDetails).on(qDocument.id.eq(qTaxAuthoritiesDetails.document.id)).fetchJoin();
        }

        if (include.contains(Document.Details.ISSUED_REASON)) {
            query.leftJoin(qDocument.originalDocument).fetchJoin();
        }

        if (include.contains(Document.Details.ORIGINAL_DOCUMENT)) {
            query.leftJoin(qDocument.originalDocument).fetchJoin();
        }

        if (include.contains(Document.Details.OTHER_SPLIT_DOCUMENT)) {
            query.leftJoin(qDocument.otherSplitDocument).fetchJoin();
        }

        if (include.contains(Document.Details.PAYMENT)) {
            query.leftJoin(qDocument.payment, QPaymentDetailsPsql.paymentDetailsPsql).fetchJoin();
        }

        if (include.contains(Document.Details.DELIVERY)) {
            query.leftJoin(qDocument.delivery, QDeliveryDetailsPsql.deliveryDetailsPsql).fetchJoin();
        }

        if (include.contains(Document.Details.DOCUMENT_TRANSFORMATION)) {
            query.leftJoin(qDocumentTransformation).on(qDocument.id.eq(qDocumentTransformation.document.id))
                    .fetchJoin();
        }
    }

    private void buildWhereClauses(DocumentFilter filter, JPAQuery<DocumentPsql> query) {
        if (filter.getId() != null) {
            query.where(qDocument.id.eq(filter.getId()));
        }

        if (StringUtils.isNotBlank(filter.getSid())) {
            query.where(qDocument.sid.eq(filter.getSid()));
        }

        if (filter.getFlow() != null) {
            query.where(qDocument.flow.eq(filter.getFlow()));
        }

        List<DocumentStatus> filteringStatuses = new ArrayList<>();

        if (!CollectionUtils.isEmpty(filter.getStatuses())) {
            filteringStatuses.addAll(filter.getStatuses());
        }

        if (filter.getStatus() != null) {
            filteringStatuses.add(filter.getStatus());
        }

        if (filteringStatuses.size() > 0) {
            query.where(qDocument.status.in(filteringStatuses));
        }

        List<DocumentTransformation.EntityType> filteringTransformationTypes = new ArrayList<>();

        if (!CollectionUtils.isEmpty(filter.getDocumentTransformationTypes())) {
            filteringTransformationTypes.addAll(filter.getDocumentTransformationTypes());
        }

        if (filteringTransformationTypes.size() > 0) {
            query.where(qDocumentTransformation.type.in(filteringTransformationTypes));
            query.distinct();
        }

        if (filter.getReferenceNumber() != null) {
            query.where(qDocument.referenceNumber.eq(filter.getReferenceNumber()));
        }

        if (filter.getIssuerTin() != null) {
            query.where(qIssuerPsql.taxIdentificationNumber.equalsIgnoreCase(filter.getIssuerTin()));
        }

        if (filter.getIssuerLegalName() != null) {
            query.where(qIssuerPsql.legalName.equalsIgnoreCase(filter.getIssuerLegalName()));
        }

        if (filter.getReceiverTin() != null) {
            query.where(qReceiverPsql.taxIdentificationNumber.equalsIgnoreCase(filter.getReceiverTin()));
        }

        if (filter.getReceiverCountry() != null) {
            query.where(qReceiverPsql.country.equalsIgnoreCase(filter.getReceiverCountry().name()));
        }

        if (filter.getReceiverName() != null) {
            query.where(qReceiverPsql.name.likeIgnoreCase("%".concat(filter.getReceiverName()).concat("%")));
        }

        if (filter.getReceiverType() != null) {
            query.where(qReceiverPsql.type.eq(filter.getReceiverType()));
        }

        if (filter.getCurrency() != null) {
            query.where(qDocument.currency.equalsIgnoreCase(filter.getCurrency().getCurrencyCode()));
        }

        if (filter.getCreatedAtFrom() != null) {
            query.where(qDocument.createdAt.goe(filter.getCreatedAtFrom()));
        }

        if (filter.getCreatedAtTo() != null) {
            query.where(qDocument.createdAt.loe(filter.getCreatedAtTo()));
        }

        if (filter.getUpdatedAtFrom() != null) {
            query.where(qDocument.updatedAt.goe(filter.getUpdatedAtFrom()));
        }

        if (filter.getUpdatedAtTo() != null) {
            query.where(qDocument.updatedAt.loe(filter.getUpdatedAtTo()));
        }

        if (filter.getIssuedDateFrom() != null) {
            query.where(qDocument.issuedDate.goe(filter.getIssuedDateFrom()));
        }

        if (filter.getIssuedDateTo() != null) {
            query.where(qDocument.issuedDate.loe(filter.getIssuedDateTo()));
        }

        if (filter.getReceivedDateFrom() != null) {
            query.where(qDocument.receivedDate.goe(filter.getReceivedDateFrom()));
        }

        if (filter.getReceivedDateTo() != null) {
            query.where(qDocument.receivedDate.loe(filter.getReceivedDateTo()));
        }

        if (filter.getReviewed() != null) {
            query.where(qDocument.reviewed.eq(filter.getReviewed()));
        }

        if (filter.getCountryCode() != null) {
            query.where(qDocument.country.eq(filter.getCountryCode().name()));
        }

        if (filter.getShop() != null) {
            query.where(qDocument.shop.equalsIgnoreCase(filter.getShop()));
        }

        if (!CollectionUtils.isEmpty(filter.getCountryCodes())) {
            List<String> filteringCountries = filter.getCountryCodes()
                    .stream()
                    .map(Enum::name)
                    .collect(Collectors.toList());
            query.where(qDocument.country.in(filteringCountries));
        }

        if (filter.getType() != null) {
            query.where(qDocument.type.eq(filter.getType()));
        }

        if (filter.getTaxDocumentNumber() != null) {
            query.where(qTaxAuthoritiesDetails.taxDocumentNumber.eq(filter.getTaxDocumentNumber()));
        }
    }


    @Override
    public boolean existsById(UUID id) {
        return jpaDocumentRepository.existsById(id);
    }

    @Override
    @Transactional
    public Optional<Document> findById(UUID id) {
        QDocumentPsql qDocument = QDocumentPsql.documentPsql;
        JPAQuery<DocumentPsql> query = new JPAQueryFactory(entityManager).selectFrom(qDocument)
                .join(qDocument.issuer).fetchJoin()
                .join(qDocument.receiver).fetchJoin()
                .leftJoin(qDocument.delivery).fetchJoin()
                .leftJoin(qDocument.payment).fetchJoin()
                .leftJoin(qDocument.issuedReason).fetchJoin()
                .leftJoin(qDocument.originalDocument).fetchJoin()
                .leftJoin(qDocument.otherSplitDocument).fetchJoin()
                .where(qDocument.id.eq(id));

        return Optional.ofNullable(query.fetchOne()).map(DocumentPsql::toEntity);
    }

    @Override
    public boolean existsBySidInErrors(String sid) {
        QDocumentPsql qDocument = QDocumentPsql.documentPsql;

        JPAQuery<DocumentPsql> query = new JPAQueryFactory(entityManager).selectFrom(qDocument)
                .where(qDocument.sid.eq(sid).and(qDocument.status.notIn(
                        DocumentStatus.TAXI_INTERNAL_ERROR,
                        DocumentStatus.TAX_ERROR_RETRIED,
                        DocumentStatus.TAX_ERROR_ACKED,
                        DocumentStatus.TAXI_FAILED_MAPPING,
                        DocumentStatus.TAX_FAILED_REQUEST,
                        DocumentStatus.TAX_FAILED_RESPONSE
                )));

        return query.fetchFirst() != null;
    }

    @Override
    @Transactional
    public Optional<DocumentId> findDocumentIdBySidInSuccessStatus(String sid) {
        QDocumentPsql qDocument = QDocumentPsql.documentPsql;

        JPAQuery<DocumentPsql> query = new JPAQueryFactory(entityManager).selectFrom(qDocument)
                .where(qDocument.sid.eq(sid).and(qDocument.status.in(
                        DocumentStatus.TAX_SUCCESS,
                        DocumentStatus.TAX_SKIPPED)));

        return Optional.ofNullable(query.fetchOne()).map(DocumentPsql::toEntityId);
    }

    @Override
    @Transactional
    public Document save(Document document) {
        document.setReceiver(receiverRepository.save(document.getReceiver()));
        document.setIssuer(issuerRepository.save(document.getIssuer()));
        if (document.getDelivery() != null) {
            document.setDelivery(deliveryDetailsRepository.save(
                    document.getDelivery()));
        }
        if (document.getPayment() != null) {
            document.setPayment(paymentDetailsRepository.save(document.getPayment()));
        }
        if (document.getIssuedReason() != null) {
            document.setIssuedReason(issuedReasonRepository.save(document.getIssuedReason()));
        }

        DocumentPsql documentToSave = jpaDocumentRepository.findById(document.getId())
                .map(documentPsql -> documentPsql.updateFrom(document))
                .orElseGet(() -> new DocumentPsql(document));

        documentToSave.updateAuditInformation();

        return jpaDocumentRepository.save(documentToSave).toEntity();
    }

    @Override
    @Transactional
    public Document updateStatus(UUID documentId, DocumentStatus status) {

        DocumentPsql documentToSave = jpaDocumentRepository.findById(documentId)
                .orElseThrow(() -> EntityNotFoundException.createNotFound(Document.class, documentId));

        documentToSave.setStatus(status);

        return jpaDocumentRepository.save(documentToSave).toEntity();
    }

    @Override
    @Transactional
    public Document update(UUID id, Document document, boolean changeUpdateData) {
        Optional<DocumentPsql> optionalDocumentPsql = jpaDocumentRepository.findById(id);

        if (optionalDocumentPsql.isEmpty()) {
            throw EntityNotFoundException.createNotFound(Document.class, id);
        }

        DocumentPsql toBeUpdated = optionalDocumentPsql.get();
        toBeUpdated.updateFrom(document);

        if (changeUpdateData) {
            toBeUpdated.setUpdatedBy(RequestContext.getUsername());
            toBeUpdated.setUpdatedAt(LocalDateTime.now(ZoneOffset.UTC));
        }

        return jpaDocumentRepository.save(toBeUpdated).toEntity();
    }
}

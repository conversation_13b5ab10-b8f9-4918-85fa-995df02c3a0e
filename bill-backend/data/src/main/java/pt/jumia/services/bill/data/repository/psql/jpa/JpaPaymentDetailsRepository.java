package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.PaymentDetailsPsql;
import pt.jumia.services.bill.data.entities.QPaymentDetailsPsql;

import java.util.UUID;

public interface JpaPaymentDetailsRepository extends JpaRepository<PaymentDetailsPsql, UUID>, QuerydslPredicateExecutor<QPaymentDetailsPsql> {

}

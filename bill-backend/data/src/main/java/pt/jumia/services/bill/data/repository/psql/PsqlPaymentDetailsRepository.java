package pt.jumia.services.bill.data.repository.psql;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.PaymentDetailsPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaPaymentDetailsRepository;
import pt.jumia.services.bill.domain.entities.PaymentDetails;
import pt.jumia.services.bill.domain.repository.PaymentDetailsRepository;

import java.util.Optional;
import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class PsqlPaymentDetailsRepository implements PaymentDetailsRepository {

    private final JpaPaymentDetailsRepository jpaPaymentDetailsRepository;

    @Override
    @Transactional
    public Optional<PaymentDetails> findById(UUID id) {
        return jpaPaymentDetailsRepository.findById(id).map(PaymentDetailsPsql::toEntity);
    }

    @Override
    public PaymentDetails save(PaymentDetails paymentDetails) {
        Optional<PaymentDetailsPsql> existingPaymentDetails = jpaPaymentDetailsRepository.findById(paymentDetails.getId());

        if (existingPaymentDetails.isPresent()) {
            return existingPaymentDetails.get().toEntity();
        } else {
            return jpaPaymentDetailsRepository.save(new PaymentDetailsPsql(paymentDetails)).toEntity();
        }
    }

}

package pt.jumia.services.bill.data.entities;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import pt.jumia.services.bill.domain.BigDecimalSerializer;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.ItemType;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static pt.jumia.services.bill.data.entities.DocumentLinePsql.JSON_DATA_TYPE;

@Entity
@Table(name = "document_lines")
@NoArgsConstructor
@TypeDef(
        name = JSON_DATA_TYPE,
        typeClass = JsonBinaryType.class
)
public class DocumentLinePsql {

    public static final String JSON_DATA_TYPE = "jsonb";

    //
    // Internal fields
    //

    @Id
    @Column(name = "id")
    private UUID id;

    @JoinColumn(name = "fk_document", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private DocumentPsql document;

    //
    // General information
    //

    @Column(name = "position", nullable = false)
    private int position;

    @Column(name = "quantity", nullable = false)
    private BigDecimal quantity;

    @Column(name = "quantity_new", nullable = false)
    private BigDecimal quantityNew;

    @Enumerated(EnumType.STRING)
    @Column(name = "unit_of_measure", nullable = false)
    private UnitOfMeasure unitOfMeasure;

    //
    // Product information
    //

    @Column(name = "item_code", nullable = false)
    private String itemCode;

    @Column(name = "sku_variant")
    private String skuVariant;

    @Column(name = "item_name", nullable = false)
    private String itemName;

    @Enumerated(EnumType.STRING)
    @Column(name = "item_type")
    private ItemType itemType;

    @Column(name = "category_sid")
    private String categorySid;

    @Column(name = "category_name")
    private String categoryName;

    @Column(name = "category_tax_authority_code")
    private String categoryTaxAuthorityCode;

    //
    // Price information
    //

    @Column(name = "unit_price", nullable = false)
    private BigDecimal unitPrice;

    @Column(name = "unit_price_new", nullable = false)
    private BigDecimal unitPriceNew;

    @Column(name = "total_amount", nullable = false)
    private BigDecimal totalAmount;

    @Column(name = "total_amount_new", nullable = false)
    private BigDecimal totalAmountNew;

    @Column(name = "net_amount", nullable = false)
    private BigDecimal netAmount;

    @Column(name = "net_amount_new", nullable = false)
    private BigDecimal netAmountNew;

    @Column(name = "total_tax_amount", nullable = false)
    private BigDecimal totalTaxAmount;

    @Column(name = "total_tax_amount_new", nullable = false)
    private BigDecimal totalTaxAmountNew;

    @Type(type = JSON_DATA_TYPE)
    @Column(name = "applied_taxes")
    private List<AppliedTaxPsql> appliedTaxes;

    @Column(name = "discount_rate")
    private BigDecimal discountRate;

    @Column(name = "discount_rate_new")
    private BigDecimal discountRateNew;

    @Column(name = "discount_amount")
    private BigDecimal discountAmount;

    @Column(name = "discount_amount_new")
    private BigDecimal discountAmountNew;

    @Column(name = "unit_of_package")
    private String unitOfPackage;

    public DocumentLinePsql(DocumentLine line) {
        this.id = line.getId();
        this.document = new DocumentPsql(line.getDocument());
        this.position = line.getPosition();
        this.quantity = line.getQuantity();
        this.quantityNew = line.getQuantity();
        this.unitOfMeasure = line.getUnitOfMeasure();
        this.itemCode = line.getItemCode();
        this.skuVariant = line.getSkuVariant();
        this.itemName = line.getItemName();
        this.itemType = line.getItemType();
        this.categorySid = line.getCategory() == null ? null : line.getCategory().getSid();
        this.categoryName = line.getCategory() == null ? null : line.getCategory().getName();
        this.categoryTaxAuthorityCode = line.getCategory() == null ? null :
                line.getCategory().getTaxAuthorityCode();
        this.unitPrice = line.getUnitPrice();
        this.unitPriceNew = line.getUnitPrice();
        this.totalAmount = line.getTotalAmount();
        this.totalAmountNew = line.getTotalAmount();
        this.netAmount = line.getNetAmount();
        this.netAmountNew = line.getNetAmount();
        this.totalTaxAmount = line.getTotalTaxAmount();
        this.totalTaxAmountNew = line.getTotalTaxAmount();
        this.appliedTaxes = line.getAppliedTaxes().stream().map(AppliedTaxPsql::new).collect(Collectors.toList());
        this.discountAmount = line.getDiscount() == null ? null : line.getDiscount().getAmount();
        this.discountAmountNew = line.getDiscount() == null ? null : line.getDiscount().getAmount();
        this.discountRate = line.getDiscount() == null ? null : line.getDiscount().getRate();
        this.discountRateNew = line.getDiscount() == null ? null : line.getDiscount().getRate();
        this.unitOfPackage = line.getUnitOfPackage();
    }

    public DocumentLine toEntity() {
        return DocumentLine.builder()
                .id(id)
                .document(document.toEntity())
                .position(position)
                .quantity(quantity)
                .unitOfMeasure(unitOfMeasure)
                .itemCode(itemCode)
                .skuVariant(skuVariant)
                .itemName(itemName)
                .itemType(itemType)
                .category(Category.builder()
                        .sid(categorySid)
                        .name(categoryName)
                        .taxAuthorityCode(categoryTaxAuthorityCode)
                        .build())
                .unitPrice(unitPrice)
                .totalAmount(totalAmount)
                .netAmount(netAmount)
                .totalTaxAmount(totalTaxAmount)
                .appliedTaxes(this.appliedTaxes.stream().map(AppliedTaxPsql::toEntity).collect(Collectors.toList()))
                .discount(discountAmount == null && discountRate == null ? null : DocumentLine.Discount.builder()
                        .amount(discountAmount)
                        .rate(discountRate)
                        .build())
                .unitOfPackage(unitOfPackage)
                .build();
    }

    @Data
    @NoArgsConstructor(access = AccessLevel.PRIVATE)  // required by jackson for json serialization
    @AllArgsConstructor(access = AccessLevel.PRIVATE)  // required by builder
    @Builder(toBuilder = true)
    public static class AppliedTaxPsql {
        private TaxCategory taxCategory;

        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal taxRate;
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal taxFixedAmount;
        @JsonSerialize(using = BigDecimalSerializer.class)
        private BigDecimal taxAmount;

        public AppliedTaxPsql(DocumentLine.AppliedTax appliedTax) {
            this.taxCategory = appliedTax.getTaxCategory();
            this.taxRate = appliedTax.getTaxRate();
            this.taxFixedAmount = appliedTax.getTaxFixedAmount();
            this.taxAmount = appliedTax.getTaxAmount();
        }

        public DocumentLine.AppliedTax toEntity() {
            return DocumentLine.AppliedTax.builder()
                    .taxCategory(taxCategory)
                    .taxRate(taxRate)
                    .taxFixedAmount(taxFixedAmount)
                    .taxAmount(taxAmount)
                    .build();
        }
    }

}

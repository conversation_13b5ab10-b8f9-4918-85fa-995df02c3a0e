package pt.jumia.services.bill.data.configuration;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import pt.jumia.services.bill.domain.usecases.settings.ReloadSettingsUseCase;

@Configuration
@RequiredArgsConstructor
public class ApplicationPropertiesConfiguration {

    private final ReloadSettingsUseCase refreshSettingsUseCase;

    @EventListener
    public void handleContextRefreshedEvent(ContextRefreshedEvent event) {
        refreshSettingsUseCase.execute();
    }
}

package pt.jumia.services.bill.data.entities;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "document_transformations")
@Builder
public class DocumentTransformationPsql {
    @Id
    @SequenceGenerator(name = "document_transformations_seq", sequenceName = "document_transformations_sequence_id_seq", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "document_transformations_seq")
    @Column(name = "id")
    private Long id;

    @JoinColumn(name = "fk_document", nullable = false)
    @ManyToOne(fetch = FetchType.LAZY)
    private DocumentPsql document;

    @Column(name = "original_value", nullable = false)
    private String originalValue;

    @Column(name = "new_value", nullable = false)
    private String newValue;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private DocumentTransformation.EntityType type;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    public DocumentTransformationPsql(DocumentTransformation documentTransformation) {
        this.id = documentTransformation.getId();
        this.document = new DocumentPsql(documentTransformation.getDocument().getId());
        this.originalValue = documentTransformation.getOriginalValue();
        this.newValue = documentTransformation.getNewValue();
        this.type = documentTransformation.getType() == null ? null : documentTransformation.getType();
        this.createdAt = documentTransformation.getCreatedAt();
        this.updatedAt = documentTransformation.getUpdatedAt();
    }

    public DocumentTransformation toEntity() {
        return DocumentTransformation.builder()
                .id(id)
                .document(Document.builder().id(document.getId()).build())
                .newValue(newValue)
                .originalValue(originalValue)
                .type(type == null ? null : DocumentTransformation.EntityType.valueOf(String.valueOf(type)))
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();
    }
}

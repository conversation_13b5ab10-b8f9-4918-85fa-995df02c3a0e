package pt.jumia.services.bill.data.repository.psql;

import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.data.entities.IssuerTemplatePsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaIssuerTemplateRepository;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.repository.IssuerTemplateRepository;

import java.util.Optional;

@Repository
@RequiredArgsConstructor
public class PsqlIssuerTemplateRepository implements IssuerTemplateRepository {

    private final JpaIssuerTemplateRepository jpaIssuerTemplateRepository;

    @Override
    @Transactional
    public Optional<IssuerTemplate> findById(long id) {
        return jpaIssuerTemplateRepository.findById(id).map(IssuerTemplatePsql::toEntity);
    }

    @Override
    public IssuerTemplate save(IssuerTemplate issuerTemplate) {
        return jpaIssuerTemplateRepository.save(new IssuerTemplatePsql(issuerTemplate)).toEntity();
    }

    @Override
    public Optional<IssuerTemplate> findByShopAndCountry(String shop, CountryCode country) {
        return jpaIssuerTemplateRepository
                .findByShopAndCountry(shop, country.getAlpha2())
                .map(IssuerTemplatePsql::toEntity);
    }

}

package pt.jumia.services.bill.data.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@Entity
@Table(name = "issuers")
@NoArgsConstructor
public class IssuerPsql {

    //
    // Internal fields
    //

    @Id
    @Column(name = "id")
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(name = "type", nullable = false)
    private IssuerType type;

    //
    // Identification
    //

    @Column(name = "legal_name")
    private String legalName;

    @Column(name = "name")
    private String name;

    @Column(name = "tax_identification_number")
    private String taxIdentificationNumber;

    @Column(name = "business_identification_number")
    private String businessRegistrationNumber;

    //
    // Contact
    //

    @Column(name = "email")
    private String email;

    @Column(name = "mobile_phone")
    private String mobilePhone;

    @Column(name = "line_phone")
    private String linePhone;

    //
    // Address
    //

    @Column(name = "country")
    private String country;

    @Column(name = "region")
    private String region;

    @Column(name = "city")
    private String city;

    @Column(name = "street")
    private String street;

    @Column(name = "building_number")
    private String buildingNumber;

    @Column(name = "floor")
    private String floor;

    @Column(name = "postal_code")
    private String postalCode;

    @Column(name = "address_additional_information")
    private String addressAdditionalInformation;

    @Column(name = "branch")
    private String branch;

    //
    // Audit information
    //

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "created_by")
    private String createdBy;

    public IssuerPsql(Issuer issuer) {
        this.id = issuer.getId();
        this.createdAt = issuer.getCreatedAt();
        this.createdBy = issuer.getCreatedBy();

        if (this.createdAt == null) {
            this.createdAt = LocalDateTime.now(ZoneOffset.UTC);
            this.createdBy = RequestContext.getUsername();
        }

        this.updateFrom(issuer);
    }

    public final IssuerPsql updateFrom(Issuer issuer) {
        this.type = issuer.getType();
        this.legalName = issuer.getLegalName();
        this.name = issuer.getName();
        this.taxIdentificationNumber = issuer.getTaxIdentificationNumber();
        this.businessRegistrationNumber = issuer.getBusinessRegistrationNumber();
        this.email = issuer.getEmail();
        this.mobilePhone = issuer.getMobilePhone();
        this.linePhone = issuer.getLinePhone();
        this.branch = issuer.getBranch();

        Address address = issuer.getAddress();
        if (address != null) {
            this.country = address.getCountry() != null ? address.getCountry().getAlpha2() : null;
            this.region = address.getRegion();
            this.city = address.getCity();
            this.street = address.getStreet();
            this.buildingNumber = address.getBuildingNumber();
            this.floor = address.getFloor();
            this.postalCode = address.getPostalCode();
            this.addressAdditionalInformation = address.getAdditionalInformation();
        }

        return this;
    }

    public Issuer toEntity() {
        return Issuer.builder()
                .id(id)
                .type(type)
                .legalName(legalName)
                .name(name)
                .taxIdentificationNumber(taxIdentificationNumber)
                .businessRegistrationNumber(businessRegistrationNumber)
                .address(noAddressFields()
                        ? null
                        : Address.builder()
                        .country(country != null ? CountryCode.getByAlpha2Code(country) : null)
                        .region(region)
                        .city(city)
                        .street(street)
                        .buildingNumber(buildingNumber)
                        .floor(floor)
                        .postalCode(postalCode)
                        .additionalInformation(addressAdditionalInformation)
                        .build())
                .email(email)
                .mobilePhone(mobilePhone)
                .linePhone(linePhone)
                .branch(branch)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .build();
    }

    private boolean noAddressFields() {
        return country == null
                && region == null
                && city == null
                && street == null
                && buildingNumber == null
                && floor == null
                && postalCode == null
                && addressAdditionalInformation == null;
    }

}

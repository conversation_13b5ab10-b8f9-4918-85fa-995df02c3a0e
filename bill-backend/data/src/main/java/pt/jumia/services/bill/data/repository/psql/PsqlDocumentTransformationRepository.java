package pt.jumia.services.bill.data.repository.psql;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.bill.data.entities.DocumentTransformationPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentTransformationRepository;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Repository
public class PsqlDocumentTransformationRepository extends PsqlRepository implements DocumentTransformationRepository {

    private final JpaDocumentTransformationRepository jpaDocumentTransformationRepository;

    public PsqlDocumentTransformationRepository(TransactionTemplate transactionTemplate
            , JpaDocumentTransformationRepository jpaDocumentTransformationRepository) {
        super(transactionTemplate);
        this.jpaDocumentTransformationRepository = jpaDocumentTransformationRepository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DocumentTransformation> findById(long id) {
        return jpaDocumentTransformationRepository.findById(id)
                .map(DocumentTransformationPsql::toEntity);
    }

    @Override
    @Transactional
    public List<DocumentTransformation> findByDocumentId(UUID documentId) {
        return jpaDocumentTransformationRepository.findByDocumentId(documentId)
                .stream().map(DocumentTransformationPsql::toEntity).collect(Collectors.toList());
    }

    @Override
    public Optional<DocumentTransformation> findByDocumentIdAndTypeAndOriginalValue(UUID documentId,
                                                                                    DocumentTransformation.EntityType type,
                                                                                    String originalValue) {
        return jpaDocumentTransformationRepository.findByDocumentIdAndTypeAndOriginalValue(documentId, type, originalValue)
                .map(DocumentTransformationPsql::toEntity);
    }

    @Override
    @Transactional
    public DocumentTransformation insert(DocumentTransformation documentTransformation) {
        return executeInTransactionOrThrow(status -> jpaDocumentTransformationRepository
                .save(new DocumentTransformationPsql(documentTransformation.toBuilder()
                        .createdAt(documentTransformation.getCreatedAt() == null ? LocalDateTime.now(ZoneOffset.UTC)
                                : documentTransformation.getCreatedAt())
                        .updatedAt(documentTransformation.getUpdatedAt() == null ? LocalDateTime.now(ZoneOffset.UTC)
                                : documentTransformation.getUpdatedAt())
                        .build())).toEntity());
    }
    @Override
    public DocumentTransformation update(Long id, DocumentTransformation documentTransformation) {
        return executeInTransactionOrThrow(status -> {
            Optional<DocumentTransformationPsql> optionalDocumentTransformationPsql =
                    jpaDocumentTransformationRepository.findById(id);

            if (optionalDocumentTransformationPsql.isEmpty()) {
                throw EntityNotFoundException.createNotFound(DocumentTransformation.class, id);
            }
            DocumentTransformationPsql documentTransformationToUpdate = optionalDocumentTransformationPsql.get();

            if (documentTransformation.getNewValue() != null) {
                documentTransformationToUpdate.setNewValue(documentTransformation.getNewValue());
            }

            if (documentTransformation.getUpdatedAt() == null) {
                documentTransformation.setUpdatedAt(LocalDateTime.now(ZoneOffset.UTC));
            }


            return jpaDocumentTransformationRepository.save(documentTransformationToUpdate).toEntity();
        });
    }


}

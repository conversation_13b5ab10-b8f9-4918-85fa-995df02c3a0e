package pt.jumia.services.bill.data.repository.psql;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.support.TransactionTemplate;
import pt.jumia.services.bill.data.entities.DocumentApiLogPsql;
import pt.jumia.services.bill.data.repository.psql.jpa.JpaDocumentApiLogRepository;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentApiLogRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;

@Repository
public class PsqlDocumentApiLogRepository extends PsqlRepository implements DocumentApiLogRepository {

    private final JpaDocumentApiLogRepository jpaDocumentApiLogRepository;

    @Autowired
    public PsqlDocumentApiLogRepository(TransactionTemplate transactionTemplate,
                                        JpaDocumentApiLogRepository jpaDocumentApiLogRepository) {
        super(transactionTemplate);
        this.jpaDocumentApiLogRepository = jpaDocumentApiLogRepository;
    }

    @Override
    public DocumentApiLog insert(DocumentApiLog documentApiLog) {
        return jpaDocumentApiLogRepository
                .save(new DocumentApiLogPsql(documentApiLog.toBuilder()
                        .createdAt(documentApiLog.getCreatedAt() == null ?
                                LocalDateTime.now(ZoneOffset.UTC) : documentApiLog.getCreatedAt())
                        .createdBy(documentApiLog.getCreatedBy() == null ?
                                RequestContext.getUsername() : documentApiLog.getCreatedBy())
                        .updatedAt(documentApiLog.getUpdatedAt() == null ?
                                LocalDateTime.now(ZoneOffset.UTC) : documentApiLog.getUpdatedAt())
                        .updatedBy(documentApiLog.getUpdatedBy() == null ?
                                RequestContext.getUsername() : documentApiLog.getUpdatedBy())
                        .build())).toEntity();
    }

    @Override
    public Optional<DocumentApiLog> findById(long id) {
        return jpaDocumentApiLogRepository.findById(id).map(DocumentApiLogPsql::toEntity);
    }

    @Override
    public DocumentApiLog update(long id, DocumentApiLog documentApiLog) {
        return executeInTransactionOrThrow(status -> {
            Optional<DocumentApiLogPsql> optionalDocumentLogPsql = jpaDocumentApiLogRepository.findById(id);

            if (optionalDocumentLogPsql.isEmpty()) {
                throw EntityNotFoundException.createNotFound(DocumentApiLog.class, id);
            }
            DocumentApiLogPsql documentLogToUpdate = optionalDocumentLogPsql.get();

            if (documentApiLog.getDocumentSid() != null) {
                documentLogToUpdate.setDocumentSid(documentApiLog.getDocumentSid());
            }
            if (documentApiLog.getOriginalRequest() != null) {
                documentLogToUpdate.setOriginalRequest(documentApiLog.getOriginalRequest());
            }
            if (documentApiLog.getIssuedDate() != null) {
                documentLogToUpdate.setIssuedDate(documentApiLog.getIssuedDate());
            }
            if (documentApiLog.getResultingDocumentsIds() != null &&
                    !documentApiLog.getResultingDocumentsIds().isEmpty()) {
                documentLogToUpdate.setResultingDocuments(documentApiLog.getResultingDocumentsIds());
            }
            if (documentApiLog.getStatus() != null) {
                documentLogToUpdate.setStatus(documentApiLog.getStatus());
            }
            if (documentApiLog.getErrors() != null) {
                documentLogToUpdate.setErrors(documentApiLog.getErrors());
            }
            if (documentApiLog.getUpdatedBy() == null) {
                documentLogToUpdate.setUpdatedBy(RequestContext.getUsername());
            }
            if (documentApiLog.getUpdatedAt() == null) {
                documentLogToUpdate.setUpdatedAt(LocalDateTime.now(ZoneOffset.UTC));
            }
            return jpaDocumentApiLogRepository.save(documentLogToUpdate).toEntity();
        });
    }
}

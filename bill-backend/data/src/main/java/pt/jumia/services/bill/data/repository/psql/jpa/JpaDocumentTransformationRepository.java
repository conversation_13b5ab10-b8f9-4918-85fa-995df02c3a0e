package pt.jumia.services.bill.data.repository.psql.jpa;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import pt.jumia.services.bill.data.entities.DocumentTransformationPsql;
import pt.jumia.services.bill.data.entities.QDocumentTransformationPsql;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


public interface JpaDocumentTransformationRepository extends JpaRepository<DocumentTransformationPsql, Long>
        , QuerydslPredicateExecutor<QDocumentTransformationPsql> {

    public List<DocumentTransformationPsql> findByDocumentId(UUID documentId);

    public Optional<DocumentTransformationPsql> findByDocumentIdAndTypeAndOriginalValue(UUID documentId,
                                                                    DocumentTransformation.EntityType type, String originalValue);
}

apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'

idea {
    module {
        sourceDirs += file('build/generated-sources')
        generatedSourceDirs += file('build/generated-sources')
    }
}

dependencies {

    implementation project(':domain')
    testImplementation project(':network')

    // Spring context
    implementation "org.springframework:spring-context-support:${springVersion}"
    implementation group: 'org.postgresql', name: 'postgresql', version: '42.2.20'

    // hibernate
    implementation "org.springframework:spring-orm:${springVersion}"
    implementation group: 'org.hibernate', name: 'hibernate-entitymanager', version: '5.4.30.Final'
    implementation group: 'org.hibernate', name: 'hibernate-envers', version: '5.4.30.Final'
    compile group: 'com.zaxxer', name: 'Hikari<PERSON>', version: '4.0.3'
    // Used to map a java object to a jsonb column in postgres
    implementation group: 'com.vladmihalcea', name: 'hibernate-types-52', version: '2.9.11'

    // QueryDSL
    implementation "com.querydsl:querydsl-jpa:4.4.0"
    annotationProcessor "com.querydsl:querydsl-apt:4.4.0:jpa"
    annotationProcessor "org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}"
    compile "javax.annotation:javax.annotation-api:1.3.2"

    //JPA
    api group: 'org.springframework.data', name: 'spring-data-jpa', version: '2.2.13.RELEASE'

    // Advanced jdbc driver implementation for postgresql event listening
    implementation group: 'com.impossibl.pgjdbc-ng', name: 'pgjdbc-ng', version: '0.8.9'

    // flyway db migrations
    implementation "org.flywaydb:flyway-core:7.8.1"
    implementation "org.springframework.boot:spring-boot-autoconfigure:${springBootVersion}"

    testImplementation ("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
}


// configure the folder for the generated sources (Query DSL entities)
compileJava {
    options.compilerArgs << "-s"
    options.compilerArgs << "$buildDir/generated-sources/java"

    doFirst {
        // make sure that directory exists
        file(new File(buildDir, "/generated-sources/java")).mkdirs()
    }
}

dockerCompose.isRequiredBy(test)
dockerCompose {
    useComposeFiles = ['../dockers/docker-compose.test.yml']
    startedServices = ['bill-database-test']
}

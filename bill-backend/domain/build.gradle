apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'

dependencies {

    api "org.jetbrains:annotations-java5:15.0"

    // Acl lib
    api group: 'com.jumia.services', name: 'acl-lib', version: "${aclLibVersion}"

    api group: 'com.github.spotbugs', name: 'spotbugs-annotations', version: "${spotbugsVersion}"

    //so we can use spring DI
    implementation ("org.springframework:spring-context:${springVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }
    testImplementation "org.springframework:spring-test:${springVersion}"
    //and load properties
    implementation "org.springframework.boot:spring-boot:${springBootVersion}"

    // needed to initialize defaultReader (for the tests to not fail in intellij)
    implementation group: 'com.jayway.jsonpath', name: 'json-path', version: '2.4.0'

    // Prometheus client
    // https://mvnrepository.com/artifact/io.micrometer/micrometer-registry-prometheus
    api group: 'io.micrometer', name: 'micrometer-registry-prometheus', version: '1.6.6'

    //logs
    api group: 'org.slf4j', name: 'slf4j-api', version: '1.7.30'

    // json converter
    api "com.fasterxml.jackson.core:jackson-databind:2.11.4"

    // Country codes
    api "com.neovisionaries:nv-i18n:1.28"

    //new relic, for monitoring
    implementation group: 'com.newrelic.agent.java', name: 'newrelic-api', version: '3.43.0'

    // Force caffeine version to prevent it from downgrading to a lower version.
    compile 'com.github.ben-manes.caffeine:caffeine:3.0.5'

    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-autoconfigure
    implementation group: 'org.springframework.boot', name: 'spring-boot-autoconfigure', version: "${springBootVersion}"

    //kafka
    compile group: 'org.springframework.kafka', name: 'spring-kafka', version: "${kafkaVersion}"
}


idea {
    module {
        sourceDirs += file("$buildDir/classes/java")
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class OriginalDocumentExistValidatorTest extends BaseDomainTest {

    @Mock
    private DocumentRepository documentRepository;

    private OriginalDocumentExistsValidator originalDocumentExistsValidator;

    @BeforeEach
    void setUp() {
        originalDocumentExistsValidator = new OriginalDocumentExistsValidator(documentRepository);
    }

    @Test
    void testValidate_withNoOriginalDocument_returnsNoErrors() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .originalDocument(null)
                        .type(DocumentType.SALES_INVOICE)
                        .build())
                .build();

        List<ValidationError> result = originalDocumentExistsValidator.validate(documentAggregate);

        assertThat(result).isEmpty();
        verifyNoInteractions(documentRepository);
    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.entities.filters.DocumentRetryFilters;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

public class DocumentRetryUseCaseTest extends BaseDomainTest {

    @Mock
    DocumentAggregateRepository documentAggregateRepository;

    @Mock
    private TaxiRequester taxiRequester;

    private DocumentRetryUseCase documentRetryUseCase;

    @BeforeEach
    void setUp() {
        documentRetryUseCase = new DocumentRetryUseCase(
                documentAggregateRepository,
                taxiRequester
        );
    }

    @Test
    void execute_retry_success() {
        Document document = Document.builder()
                .id(UUID.fromString("096724bc-2c85-4606-94cd-a81e3f7a3f7b"))
                .status(DocumentStatus.TAXI_FAILED_MAPPING)
                .type(DocumentType.SALES_INVOICE)
                .build();

        DocumentRetryFilters documentRetryFilters = DocumentRetryFilters.builder()
                .relatedEntityId(document.getId())
                .build();

        documentRetryUseCase.execute(document);

        verify(taxiRequester).bulkRetryDocuments(documentRetryFilters);
        verifyNoMoreInteractions(taxiRequester);
    }

    @Test
    void execute_resubmit_successInvoice() {
        Document document = Document.builder()
                .id(UUID.fromString("096724bc-2c85-4606-94cd-a81e3f7a3f7b"))
                .status(DocumentStatus.TAXI_INTERNAL_ERROR)
                .type(DocumentType.SALES_INVOICE)
                .build();

        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .build();

        when(documentAggregateRepository.findByDocumentId(document.getId())).thenReturn(Optional.of(documentAggregate));

        documentRetryUseCase.execute(document);

        verify(taxiRequester).pushInvoice(documentAggregate);
        verifyNoMoreInteractions(taxiRequester);
    }

    @Test
    void execute_resubmit_successCreditNote() {
        Document document = Document.builder()
                .id(UUID.fromString("096724bc-2c85-4606-94cd-a81e3f7a3f7b"))
                .status(DocumentStatus.TAXI_INTERNAL_ERROR)
                .type(DocumentType.SALES_CREDIT_NOTE)
                .originalDocument(DocumentId.builder().sid("096724bc-2c85-4606-85cd-a81e3f7a3f7b").build())
                .build();

        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .build();

        when(documentAggregateRepository.findByDocumentId(document.getId())).thenReturn(Optional.of(documentAggregate));

        documentRetryUseCase.execute(document);

        verify(taxiRequester).pushCreditNote(documentAggregate.getDocument().getOriginalDocument().getSid(), documentAggregate);
        verifyNoMoreInteractions(taxiRequester);
    }

    @Test
    void execute_resubmit_successSalesCreditMemo() {
        Document document = Document.builder()
                .id(UUID.fromString("096724bc-2c85-4606-94cd-a81e3f7a3f7b"))
                .status(DocumentStatus.TAXI_INTERNAL_ERROR)
                .type(DocumentType.SALES_CREDIT_MEMO)
                .build();

        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .build();

        when(documentAggregateRepository.findByDocumentId(document.getId())).thenReturn(Optional.of(documentAggregate));

        documentRetryUseCase.execute(document);

        verify(taxiRequester).pushInvoice(documentAggregate);
        verifyNoMoreInteractions(taxiRequester);
    }

    @Test
    void execute_resubmit_successSalesDebitNote() {
        Document document = Document.builder()
                .id(UUID.fromString("096724bc-2c85-4606-94cd-a81e3f7a3f7b"))
                .status(DocumentStatus.TAXI_INTERNAL_ERROR)
                .type(DocumentType.SALES_DEBIT_NOTE)
                .build();

        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .build();

        when(documentAggregateRepository.findByDocumentId(document.getId())).thenReturn(Optional.of(documentAggregate));

        documentRetryUseCase.execute(document);

        verify(taxiRequester).pushInvoice(documentAggregate);
        verifyNoMoreInteractions(taxiRequester);
    }

    @Test
    void execute_throwConflictException() {
        Document document = Document.builder()
                .id(UUID.fromString("096724bc-2c85-4606-94cd-a81e3f7a3f7b"))
                .status(DocumentStatus.TAX_PENDING)
                .type(DocumentType.SALES_INVOICE)
                .build();

        assertThatThrownBy(() -> documentRetryUseCase.execute(document))
                .isInstanceOf(ConflictOperationException.class);
        verifyNoInteractions(taxiRequester);
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class AtLeastOneLineValidatorTest extends BaseDomainTest {

    private final AtLeastOneLineValidator atLeastOneLineValidator = new AtLeastOneLineValidator();

    @Test
    void testValidate_documentAggregateWithoutAnyLine_returnsExpectedError() {
        List<ValidationError> emptyLinesResult = atLeastOneLineValidator.validate(DocumentAggregate.builder()
                .lines(List.of())
                .build());
        List<ValidationError> nullLinesResult = atLeastOneLineValidator.validate(DocumentAggregate.builder()
                .lines(null)
                .build());

        assertThat(emptyLinesResult).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.MISSING_REQUIRED_FIELD);
        assertThat(nullLinesResult).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.MISSING_REQUIRED_FIELD);
    }

    @Test
    void testValidate_documentAggregateWithoutSomeLine_returnsNoErrors() {
        List<ValidationError> result = atLeastOneLineValidator.validate(DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder().build()))
                .build());
        assertThat(result).isEmpty();
    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentPossibleOperations;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.settings.ApprovalSettings;
import pt.jumia.services.bill.domain.settings.CancellationSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.settings.RejectionSettings;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;


public class DocumentAllowedOperationsUseCaseTest extends BaseDomainTest {

    private static final RequestUser REQUEST_USER = RequestUser
            .builder()
            .username("<EMAIL>")
            .build();

    @Mock
    private OverallSettings overallSettings;

    @Mock
    private CancellationSettings cancellationSettings;

    @Mock
    private RejectionSettings rejectionSettings;

    @Mock
    private ApprovalSettings approvalSettings;

    @Mock
    private UserPermissionValidationUseCase userPermissionValidationUseCase;

    @InjectMocks
    private DocumentAllowedOperationsUseCase documentAllowedOperationsUseCase;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    void execute_acknowledgementAllowed() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_FAILED_REQUEST)
                .type(DocumentType.SALES_INVOICE)
                .build();
        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(false)
                                .canAcknowledge(true)
                                .canRetryDocuments(true)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());

        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);

        verifyNoInteractions(overallSettings);
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }

    @Test
    void execute_acknowledgementPermissionDenied() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_FAILED_REQUEST)
                .type(DocumentType.SALES_INVOICE)
                .build();
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase)
                .checkCanAcknowledgeDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase)
                .checkCanRetryDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);
        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(false)
                                .canAcknowledge(false)
                                .canRetryDocuments(false)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());

        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verifyNoInteractions(overallSettings);
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }

    @Test
    void execute_cancellationPermissionDenied() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_SUCCESS)
                .type(DocumentType.SALES_INVOICE)
                .build();

        when(overallSettings.getCancellationSettings()).thenReturn(cancellationSettings);
        when(cancellationSettings.getInvoiceCancellationEnabled(document.getShop(),
                document.getCountry().getAlpha2())).thenReturn(true);
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase)
                .checkCanCancelDocumentByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);

        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(false)
                                .canAcknowledge(false)
                                .canRetryDocuments(false)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());

        verify(userPermissionValidationUseCase).checkCanCancelDocumentByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);

        verify(cancellationSettings).getInvoiceCancellationEnabled("jumia", "EG");
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }

    @Test
    void execute_cancellationSettingsDisabled() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_SUCCESS)
                .type(DocumentType.SALES_CREDIT_MEMO)
                .build();

        when(overallSettings.getCancellationSettings()).thenReturn(cancellationSettings);
        when(cancellationSettings.getCreditMemoCancellationEnabled(document.getShop(),
                document.getCountry().getAlpha2())).thenReturn(false);

        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(false)
                                .canAcknowledge(false)
                                .canRetryDocuments(false)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());


        verify(cancellationSettings).getCreditMemoCancellationEnabled("jumia", "EG");
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }

    @Test
    void execute_cancellationAllowed() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_SUCCESS)
                .type(DocumentType.SALES_CREDIT_NOTE)
                .build();

        when(overallSettings.getCancellationSettings()).thenReturn(cancellationSettings);
        when(cancellationSettings.getCreditNoteCancellationEnabled(document.getShop(),
                document.getCountry().getAlpha2())).thenReturn(true);

        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(true)
                                .canAcknowledge(false)
                                .canRetryDocuments(false)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());

        verify(userPermissionValidationUseCase).checkCanCancelDocumentByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);

        verify(cancellationSettings).getCreditNoteCancellationEnabled("jumia", "EG");
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }

    @Test
    void execute_declineRejectionPermissionDenied() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_REJECTED)
                .type(DocumentType.SALES_INVOICE)
                .build();

        when(overallSettings.getRejectionSettings()).thenReturn(rejectionSettings);
        when(rejectionSettings.getInvoiceRejectionDeclineEnabled(document.getShop(),
                document.getCountry().getAlpha2())).thenReturn(true);
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase)
                .checkCanDeclineDocumentRejectionByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase)
                .checkCanRetryDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);
        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(false)
                                .canAcknowledge(true)
                                .canRetryDocuments(false)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());

        verify(userPermissionValidationUseCase).checkCanDeclineDocumentRejectionByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);

        verify(rejectionSettings).getInvoiceRejectionDeclineEnabled("jumia", "EG");
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }

    @Test
    void execute_declineRejectionSettingsDisabled_retryAllowed() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_REJECTED)
                .type(DocumentType.SALES_CREDIT_MEMO)
                .build();

        when(overallSettings.getRejectionSettings()).thenReturn(rejectionSettings);
        when(rejectionSettings.getCreditMemoRejectionDeclineEnabled(document.getShop(),
                document.getCountry().getAlpha2())).thenReturn(false);

        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(false)
                                .canAcknowledge(true)
                                .canRetryDocuments(true)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());


        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(userPermissionValidationUseCase).checkCanAcknowledgeDocumentByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(rejectionSettings).getCreditMemoRejectionDeclineEnabled("jumia", "EG");
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }

    @Test
    void execute_declineRejectionAllowed_retryNotAllowedByPermission() {
        Document document = Document.builder()
                .country(CountryCode.EG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_REJECTED)
                .type(DocumentType.SALES_CREDIT_NOTE)
                .build();

        when(overallSettings.getRejectionSettings()).thenReturn(rejectionSettings);
        when(rejectionSettings.getCreditNoteRejectionDeclineEnabled(document.getShop(),
                document.getCountry().getAlpha2())).thenReturn(true);
        doThrow(UserForbiddenException.create(""))
                .when(userPermissionValidationUseCase)
                .checkCanRetryDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG);

        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canCancel(false)
                                .canAcknowledge(true)
                                .canRetryDocuments(false)
                                .canDeclineDocumentRejection(true)
                                .build())
                        .build().withoutDbField());

        verify(userPermissionValidationUseCase).checkCanDeclineDocumentRejectionByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(userPermissionValidationUseCase).checkCanRetryDocumentsByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.EG);
        verify(rejectionSettings).getCreditNoteRejectionDeclineEnabled("jumia", "EG");
        verifyNoMoreInteractions(cancellationSettings);
    }

    @Test
    void execute_approvePermission() {
        Document document = Document.builder()
                .country(CountryCode.NG)
                .shop("jumia")
                .flow(DocumentFlow.RETAIL)
                .status(DocumentStatus.TAX_PENDING)
                .type(DocumentType.SALES_INVOICE)
                .build();

        when(overallSettings.getApprovalSettings()).thenReturn(approvalSettings);
        when(approvalSettings.getInvoiceApprovalEnabled(document.getShop(),
                document.getCountry().getAlpha2())).thenReturn(true);
        Document updatedDocument = documentAllowedOperationsUseCase.appendDocumentPossibleOperations(document);

        assertThat(updatedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(document
                        .toBuilder()
                        .documentPossibleOperations(DocumentPossibleOperations.builder()
                                .canApprove(true)
                                .canCancel(false)
                                .canAcknowledge(false)
                                .canRetryDocuments(false)
                                .canDeclineDocumentRejection(false)
                                .build())
                        .build().withoutDbField());


        verify(userPermissionValidationUseCase).checkCanApprovedReceivedDocumentsByCountryCodeOrThrow(
                REQUEST_USER,
                CountryCode.NG);

        verify(approvalSettings).getInvoiceApprovalEnabled("jumia", "NG");
        verifyNoMoreInteractions(userPermissionValidationUseCase);
    }
}

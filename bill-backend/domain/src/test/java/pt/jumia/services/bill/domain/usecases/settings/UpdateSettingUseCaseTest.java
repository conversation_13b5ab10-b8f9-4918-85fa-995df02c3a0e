package pt.jumia.services.bill.domain.usecases.settings;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.DataEventsNotificator;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UpdateSettingUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder().username("<EMAIL>").build();
    private static final Setting SETTING = Setting.builder()
            .id(124L)
            .type(Setting.Type.OVERRIDE)
            .property("some.property")
            .value("some-value")
            .overrideKey("some-override-key")
            .description("some-description")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC).minusDays(1))
            .createdBy("unit-test-user")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC).minusHours(12))
            .updatedBy("unit-test-user")
            .build();
    private static final Setting SETTING_UPDATE = SETTING.toBuilder()
            .overrideKey("override-update")
            .description("some-new-description")
            .value("value-update")
            .build();

    @Mock
    private SettingRepository settingRepository;
    @Mock
    private DataEventsNotificator dataEventsNotificator;
    @InjectMocks
    private UpdateSettingUseCase updateSettingUseCase;
    @Captor
    private ArgumentCaptor<Setting> settingCaptor;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void update_overrideSetting() {
        when(settingRepository.findById(SETTING.getId())).thenReturn(Optional.of(SETTING));
        when(settingRepository.update(eq(SETTING.getId()), any())).thenReturn(SETTING_UPDATE);
        LocalDateTime timeBeforeUpdate = LocalDateTime.now(ZoneOffset.UTC);

        Setting updatedSetting = updateSettingUseCase.execute(SETTING.getId(), SETTING_UPDATE);

        verify(settingRepository).findById(SETTING.getId());
        verify(settingRepository).update(eq(SETTING.getId()), settingCaptor.capture());
        Setting setting = settingCaptor.getValue();
        assertThat(setting.getCreatedBy()).isEqualTo(SETTING.getCreatedBy());
        assertThat(setting.getCreatedAt()).isEqualTo(SETTING.getCreatedAt());
        assertThat(setting.getUpdatedBy()).isEqualTo(REQUEST_USER.getUsername());
        assertThat(setting.getUpdatedAt()).isAfter(timeBeforeUpdate);
        verify(dataEventsNotificator).notifySettingChanges();
        assertThat(updatedSetting).isEqualTo(SETTING_UPDATE);
    }

    @Test
    public void update_defaultSetting() {
        Setting setting = SETTING.toBuilder().type(Setting.Type.DEFAULT).build();
        Setting expectedUpdatedSetting = setting.toBuilder().description("foobar").value("barfoo").build();
        when(settingRepository.findById(eq(setting.getId()))).thenReturn(Optional.of(setting));
        when(settingRepository.update(eq(SETTING.getId()), any())).thenReturn(expectedUpdatedSetting);
        LocalDateTime timeBeforeUpdate = LocalDateTime.now(ZoneOffset.UTC);

        Setting updatedSetting = updateSettingUseCase.execute(setting.getId(), expectedUpdatedSetting);

        verify(settingRepository).findById(eq(expectedUpdatedSetting.getId()));
        verify(settingRepository).update(eq(SETTING.getId()), settingCaptor.capture());
        Setting settingValue = settingCaptor.getValue();
        assertThat(settingValue.getCreatedBy()).isEqualTo(SETTING.getCreatedBy());
        assertThat(settingValue.getCreatedAt()).isEqualTo(SETTING.getCreatedAt());
        assertThat(settingValue.getUpdatedBy()).isEqualTo(REQUEST_USER.getUsername());
        assertThat(settingValue.getUpdatedAt()).isAfter(timeBeforeUpdate);
        verify(dataEventsNotificator).notifySettingChanges();
        assertThat(updatedSetting).isEqualTo(expectedUpdatedSetting);
    }

    @Test
    public void update_notFound() {
        when(settingRepository.findById(SETTING.getId())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> updateSettingUseCase.execute(SETTING.getId(), SETTING))
                .isInstanceOf(EntityNotFoundException.class);

        verify(settingRepository).findById(SETTING.getId());
        verify(settingRepository, never()).update(anyLong(), any());
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void update_defaultSetting_cantUpdateFields() {
        Setting setting = SETTING.toBuilder().type(Setting.Type.DEFAULT).build();
        Setting settingUpdates = setting.toBuilder()
                .property("property-rename").description("foobar").value("barfoo").build();

        when(settingRepository.findById(setting.getId())).thenReturn(Optional.of(setting));

        assertThatThrownBy(() -> updateSettingUseCase.execute(setting.getId(), settingUpdates))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("You can only update the value and description of a default setting.");

        verify(settingRepository).findById(setting.getId());
        verify(settingRepository, never()).update(anyLong(), any());
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void update_defaultSetting_nullValue() {
        Setting setting = SETTING.toBuilder().type(Setting.Type.DEFAULT).build();
        Setting settingUpdates = setting.toBuilder().description("foobar").value(null).build();

        when(settingRepository.findById(setting.getId())).thenReturn(Optional.of(setting));

        assertThatThrownBy(() -> updateSettingUseCase.execute(setting.getId(), settingUpdates))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Setting values cannot be null.");

        verify(settingRepository).findById(setting.getId());
        verify(settingRepository, never()).update(anyLong(), any());
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void update_overrideSetting_cantUpdateFields() {
        Setting settingUpdate = SETTING_UPDATE.toBuilder().property("attempt-to-rename-property").build();

        when(settingRepository.findById(SETTING.getId())).thenReturn(Optional.of(SETTING));

        assertThatThrownBy(() -> updateSettingUseCase.execute(SETTING.getId(), settingUpdate))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("You can only update the value, description and overrideKey of a override setting.");

        verify(settingRepository).findById(SETTING.getId());
        verify(settingRepository, never()).update(anyLong(), any());
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void update_overrideSetting_emptyOverride() {
        Setting settingUpdate = SETTING_UPDATE.toBuilder().overrideKey(" ").build();

        when(settingRepository.findById(SETTING.getId())).thenReturn(Optional.of(SETTING));

        assertThatThrownBy(() -> updateSettingUseCase.execute(settingUpdate.getId(), settingUpdate))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Override setting key cannot be empty.");

        verify(settingRepository).findById(settingUpdate.getId());
        verify(settingRepository, never()).update(anyLong(), any());
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void update_overrideSetting_nullValue() {
        Setting settingUpdate = SETTING_UPDATE.toBuilder().value(null).build();

        when(settingRepository.findById(SETTING.getId())).thenReturn(Optional.of(SETTING));

        assertThatThrownBy(() -> updateSettingUseCase.execute(SETTING.getId(), settingUpdate))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Setting values cannot be null.");

        verify(settingRepository).findById(SETTING.getId());
        verify(settingRepository, never()).update(anyLong(), any());
        verifyNoInteractions(dataEventsNotificator);
    }
}

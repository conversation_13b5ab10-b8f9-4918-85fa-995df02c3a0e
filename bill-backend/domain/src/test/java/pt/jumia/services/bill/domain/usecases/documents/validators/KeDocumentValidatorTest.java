package pt.jumia.services.bill.domain.usecases.documents.validators;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.settings.OverallSettings;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class KeDocumentValidatorTest {

    private KeDocumentValidator keDocumentValidator;

    @BeforeEach
    void setUp() {
        keDocumentValidator = new KeDocumentValidator(new OverallSettings());
    }

    DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .document(Document.builder()
                    .receiver(Receiver.builder()
                            .name("Reiever")
                            .legalName("reciever Legal name ")
                            .taxIdentificationNumber("12345654")
                            .type(ReceiverType.CUSTOMER)
                            .email("<EMAIL>")
                            .address(Address.builder().country(CountryCode.KE).build()).build())
                    .country(CountryCode.KE)
                    .shop("jumia")
                    .transactionType("type")
                    .transactionProgress("progress")
                    .type(DocumentType.SALES_INVOICE)
                    .flow(DocumentFlow.RETAIL)
                    .referenceNumber("LegalEntity")
                    .issuedDate(LocalDateTime.of(2023, 6, 12, 12, 0))
                    .generatedBy("JUMIA")
                    .sid("KE_test_1")
                    .build())
            .lines(List.of(DocumentLine.builder()
                    .position(0)
                    .quantity(BigDecimal.valueOf(1))
                    .itemCode("abc")
                    .unitOfPackage("NOT_APPLICABLE")
                    .totalAmount(BigDecimal.valueOf(100))
                    .netAmount(BigDecimal.valueOf(100))
                    .totalTaxAmount(BigDecimal.valueOf(18))
                    .unitOfPackage("NOT_APPLICABLE")
                    .build()))
            .build();

    @Test
    public void test_keValidation_success() {
        List<ValidationError> validationErrors = keDocumentValidator.validate(DOCUMENT_AGGREGATE);
        assertThat(validationErrors).isEqualTo(List.of());
    }


}

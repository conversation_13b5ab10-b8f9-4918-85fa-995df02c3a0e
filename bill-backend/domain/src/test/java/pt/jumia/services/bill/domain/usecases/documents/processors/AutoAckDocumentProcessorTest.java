package pt.jumia.services.bill.domain.usecases.documents.processors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.usecases.documents.AckDocumentUseCase;

import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class AutoAckDocumentProcessorTest extends BaseDomainTest {

    private static final DocumentAggregate AGGREGATE = DocumentAggregate.builder()
            .document(Document.builder()
                    .sid("sid-test")
                    .status(DocumentStatus.TAX_SUBMITTED_INVALID)
                    .build()
            )
            .build();

    @Mock
    private DocumentAggregateRepository documentAggregateRepository;

    @Mock
    private AckDocumentUseCase ackDocumentUseCase;

    private AutoAckDocumentProcessor autoAckDocumentProcessor;

    @BeforeEach
    void setUp() {
        autoAckDocumentProcessor = new AutoAckDocumentProcessor(documentAggregateRepository, ackDocumentUseCase);
    }

    @Test
    public void testAutoAckDocumentProcessor_withNoDocument() {

        DocumentFilter filter = DocumentFilter.builder()
                .sid(AGGREGATE.getDocument().getSid())
                .statuses(DocumentStatus.getErrorStatuses())
                .include(List.of(Document.Details.values()))
                .build();

        when(documentAggregateRepository.findAll(eq(filter))).thenReturn(List.of());

        autoAckDocumentProcessor.process(AGGREGATE);

        verifyNoInteractions(ackDocumentUseCase);
    }

    @Test
    public void testAutoAckDocumentProcessor_document() {

        DocumentFilter filter = DocumentFilter.builder()
                .sid(AGGREGATE.getDocument().getSid())
                .statuses(DocumentStatus.getErrorStatuses())
                .include(List.of(Document.Details.values()))
                .build();

        when(documentAggregateRepository.findAll(eq(filter))).thenReturn(List.of(DocumentAggregate.builder()
                .document(Document.builder().sid("sid-test")
                        .id(UUID.fromString("096724bc-2c85-4606-94cd-a81e3f7a3f7b"))
                        .status(DocumentStatus.TAX_SUBMITTED_INVALID)
                        .build())
                .build()));

        autoAckDocumentProcessor.process(AGGREGATE);

        verify(ackDocumentUseCase).execute(any(Document.class));
    }
}

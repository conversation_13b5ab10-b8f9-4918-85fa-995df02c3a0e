package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class TaxSummaryValidationTest extends BaseDomainTest {

    private final TaxSummaryValidation taxSummaryValidation = new TaxSummaryValidation();

    @Test
    public void tax_summary_is_valid() {
        List<ValidationError> result = taxSummaryValidation.validate(DocumentAggregate.builder()
                .taxCategoryTotals(List.of(
                        TaxCategoryTotal.builder()
                                .taxAmount(BigDecimal.valueOf(300))
                                .netAmount(BigDecimal.valueOf(200))
                                .totalAmount(BigDecimal.valueOf(500))
                                .build()
                ))
                .build());

        assertThat(result).isEmpty();
    }

    @Test
    public void tax_summary_is_not_valid() {
        List<ValidationError> taxSummaryNotValid = taxSummaryValidation.validate(DocumentAggregate.builder()
                .taxCategoryTotals(List.of(
                        TaxCategoryTotal.builder()
                                .taxAmount(BigDecimal.valueOf(200))
                                .netAmount(BigDecimal.valueOf(200))
                                .totalAmount(BigDecimal.valueOf(500))
                                .build()
                ))
                .build());

        assertThat(taxSummaryNotValid).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.TAX_SUMMARY_NOT_VALID);
    }

}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class DocumentIssuedReasonValidatorTest extends BaseDomainTest {

    private DocumentIssuedReasonValidator documentIssuedReasonValidator;

    @BeforeEach
    void setUp() {
        documentIssuedReasonValidator = new DocumentIssuedReasonValidator();
    }

    @Test
    public void testValidate_validNoIssuedReasonRequired() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .type(DocumentType.SALES_INVOICE)
                        .issuedReason(null)
                        .build())
                .build();

        List<ValidationError> result = documentIssuedReasonValidator.validate(documentAggregate);

        assertThat(result).isEmpty();
    }

    @Test
    public void testValidate_invalidIssuedReason_issuedReasonNotSupported() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .type(DocumentType.SALES_INVOICE)
                        .issuedReason(IssuedReason.builder()
                                .code(IssuedReasonCode.DAMAGED_PRODUCTS)
                                .build())
                        .build())
                .build();

        List<ValidationError> result = documentIssuedReasonValidator.validate(documentAggregate);

        assertThat(result).extracting(ValidationError::getCode).containsExactly(ErrorCode.INVALID_ISSUED_REASON);
    }



}

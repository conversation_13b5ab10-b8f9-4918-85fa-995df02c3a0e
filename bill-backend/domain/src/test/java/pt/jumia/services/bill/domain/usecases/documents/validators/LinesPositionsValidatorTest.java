package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class LinesPositionsValidatorTest {
    private final LinesPositionsValidator linesPositionsValidator = new LinesPositionsValidator();

    @Test
    void testValidate_documentAggregateWithCorrectLinesPositions_returnsNoErrors() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder().build())
                .lines(List.of(DocumentLine.builder()
                                .position(0).build(),
                        DocumentLine.builder()
                                .position(1).build(),
                        DocumentLine.builder()
                                .position(2).build()))
                .build();

        List<ValidationError> result = linesPositionsValidator.validate(documentAggregate);

        assertThat(result).isEmpty();
    }

    @Test
    void testValidate_documentAggregateWithOneRepeatedLinePositions() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder().build())
                .lines(List.of(DocumentLine.builder()
                                .position(1).build(),
                        DocumentLine.builder()
                                .position(1).build(),
                        DocumentLine.builder()
                                .position(2).build()))
                .build();

        List<ValidationError> result = linesPositionsValidator.validate(documentAggregate);

        assertThat(result).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.INCORRECT_LINE_POSITION_VALUE);
        assertThat(result).extracting(ValidationError::getErrorMessage)
                .contains("Line Position (1) is repeated");
    }

    @Test
    void testValidate_documentAggregateWithMultipleRepeatedLinesPositions() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder().build())
                .lines(List.of(DocumentLine.builder()
                                .position(1).build(),
                        DocumentLine.builder()
                                .position(1).build(),
                        DocumentLine.builder()
                                .position(2).build(),
                        DocumentLine.builder()
                                .position(2).build()))
                .build();

        List<ValidationError> results = linesPositionsValidator.validate(documentAggregate);

        assertThat(results).extracting(ValidationError::getCode)
                .containsAll(List.of(ErrorCode.INCORRECT_LINE_POSITION_VALUE,
                        ErrorCode.INCORRECT_LINE_POSITION_VALUE));
        assertThat(results).extracting(ValidationError::getErrorMessage)
                .containsAll(List.of("Line Position (1) is repeated",
                        "Line Position (2) is repeated"));

    }
}

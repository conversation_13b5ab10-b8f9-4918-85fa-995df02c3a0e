package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class LinesValuesValidatorTest extends BaseDomainTest {

    private final LinesValuesValidator linesValuesValidator = new LinesValuesValidator();

    @Test
    public void lines_value_is_valid() {
        List<ValidationError> result = linesValuesValidator.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .unitPrice(BigDecimal.valueOf(1000))
                                .quantity(BigDecimal.valueOf(2))
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .build()
                ))
                .build());

        assertThat(result).isEmpty();
    }

    @Test
    public void lines_value_with_discount_is_valid() {
        List<ValidationError> result = linesValuesValidator.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .unitPrice(BigDecimal.valueOf(1000))
                                .quantity(BigDecimal.valueOf(2))
                                .netAmount(BigDecimal.valueOf(2000))
                                .discount(DocumentLine.Discount.builder()
                                        .amount(BigDecimal.valueOf(50))
                                        .build())
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .totalAmount(BigDecimal.valueOf(2450))
                                .build()
                ))
                .build());

        assertThat(result).isEmpty();
    }

    @Test
    public void net_amount_lines_is_not_equal_unit_price_by_quantity() {
        List<ValidationError> netAmountNotEqualUnitPriceByQuantity = linesValuesValidator.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .unitPrice(BigDecimal.valueOf(1000))
                                .quantity(BigDecimal.valueOf(1))
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .build()
                ))
                .build());

        assertThat(netAmountNotEqualUnitPriceByQuantity).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.NET_AMOUNT_NOT_VALID_LINE);
    }

    @Test
    public void total_amount_lines_is_not_equal_tax_and_net_amount() {
        List<ValidationError> netAmountNotEqualUnitPriceByQuantity = linesValuesValidator.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .unitPrice(BigDecimal.valueOf(1000))
                                .quantity(BigDecimal.valueOf(2))
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalTaxAmount(BigDecimal.valueOf(100))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .build()
                ))
                .build());

        assertThat(netAmountNotEqualUnitPriceByQuantity).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.TOTAL_AMOUNT_NOT_VALID_LINE);
    }
}

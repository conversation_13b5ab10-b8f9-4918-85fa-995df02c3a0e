package pt.jumia.services.bill.domain.usecases.documents;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.test.util.ReflectionTestUtils;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.CommunicationsRequester;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.exceptions.document.MissingDocumentPdfException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.domain.settings.CommunicationsSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.notification.ReadNotificationUseCase;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

public class SendEmailDocumentToReceiverUseCaseTest extends BaseDomainTest {

    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid(null)
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .receiver(Receiver.builder()
                    .email("<EMAIL>")
                    .mobilePhone("012222222222")
                    .build())
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("even_more_dummy")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .build();

    @Mock
    private CommunicationsRequester communicationsRequester;

    @Spy
    private NetworkProperties networkProperties;

    @Mock
    private GenerateJudgeDocumentUseCase generateJudgeDocumentUseCase;

    private SendEmailDocumentToReceiverUseCase sendEmailDocumentToReceiverUseCase;

    @Mock
    private OverallSettings overallSettings;

    @Mock
    private CommunicationsSettings communicationsSettings;

    @Mock
    private ReadNotificationUseCase readNotificationUseCase;

    @BeforeEach
    void setUp() {
        sendEmailDocumentToReceiverUseCase = new SendEmailDocumentToReceiverUseCase(
                communicationsRequester, overallSettings, readNotificationUseCase,
                generateJudgeDocumentUseCase);

        ReflectionTestUtils.setField(networkProperties.getCommunications(), "enabled", true);
    }

    @Test
    void executeSendEmailDocumentToReceiverTest() {
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder().document(
                DOCUMENT.toBuilder().judgeSid("fake_judge_sid").build())
                .build();

        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        when(communicationsSettings.isEnabled(anyString(), anyString())).thenReturn(true);
        sendEmailDocumentToReceiverUseCase.execute(documentAggregate);

        verify(communicationsRequester).sendEmailReceivedDocument(documentAggregate);
    }

    @Test
    void executeSendEmailReceivedDocumentTest() throws JsonProcessingException {
        String email = "<EMAIL>";
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder().document(
                        DOCUMENT.toBuilder().judgeSid("fake_judge_sid").build())
                .build();

        sendEmailDocumentToReceiverUseCase.execute(documentAggregate, new ArrayList<>(List.of(email)));

        verify(communicationsRequester).sendEmailReceivedDocument(documentAggregate, email);
    }

    @Test
    void executeSendEmailReceivedDocumentTests() throws JsonProcessingException {
        List<String> emails = new ArrayList<>();
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder()
                .document(DOCUMENT.toBuilder().judgeSid("fake_judge_sid").build())
                .build();

        when(readNotificationUseCase.getPurchasePortal()).thenReturn(Collections.emptyList());

        sendEmailDocumentToReceiverUseCase.execute(documentAggregate, emails);

        verify(communicationsRequester, never()).sendEmailReceivedDocument(documentAggregate, "<EMAIL>");
    }

    @Test
    void executeSendEmailDocumentToReceiverJudgeSidNullTest() {

        assertThatThrownBy(() -> sendEmailDocumentToReceiverUseCase.execute(DOCUMENT_AGGREGATE))
                .isInstanceOf(MissingDocumentPdfException.class);
    }

    @Test
    void executeSendEmailDocumentToReceiverWhenEnabledFalseTest() {
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder().document(
                DOCUMENT.toBuilder().judgeSid("fake_judge_sid").build())
                .build();

        ReflectionTestUtils.setField(networkProperties.getCommunications(), "enabled", false);

        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        when(communicationsSettings.isEnabled(anyString(), anyString())).thenReturn(false);
        sendEmailDocumentToReceiverUseCase.execute(documentAggregate);

        verifyNoInteractions(communicationsRequester);
    }

    @Test
    void executeSendEmailDocumentToReceiverEmailIsNullTest() {
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder().document(
                DOCUMENT.toBuilder()
                        .judgeSid("fake_judge_sid")
                        .issuer(Issuer.builder()
                                .email(null)
                                .mobilePhone("012222222222")
                                .build())
                        .build())
                .build();

        verifyNoInteractions(communicationsRequester);
    }

    @Test
    void executeSendEmailDocumentToReceiverMobilePhoneIsNullTest() {
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder().document(
                DOCUMENT.toBuilder()
                        .judgeSid("fake_judge_sid")
                        .issuer(Issuer.builder()
                                .email("<EMAIL>")
                                .mobilePhone(null)
                                .build())
                        .build())
                .build();

        verifyNoInteractions(communicationsRequester);
    }
}

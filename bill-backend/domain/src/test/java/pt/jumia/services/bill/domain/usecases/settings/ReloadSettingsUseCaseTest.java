package pt.jumia.services.bill.domain.usecases.settings;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.repository.SettingRepository;
import pt.jumia.services.bill.domain.settings.OverallSettings;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ReloadSettingsUseCaseTest {
    private static final List<Setting> SETTINGS = List.of(Setting.builder()
            .id(1L)
            .property("dummy")
            .type(Setting.Type.DEFAULT)
            .description("dummy")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .createdBy("dummy")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedBy("dummy")
            .build());

    @Mock
    private SettingRepository settingRepository;
    @Mock
    private OverallSettings overallSettings;
    @InjectMocks
    private ReloadSettingsUseCase reloadSettingsUseCase;

    @Test
    public void reloadSettings() {
        when(settingRepository.findAll()).thenReturn(SETTINGS);

        reloadSettingsUseCase.execute();

        verify(settingRepository).findAll();
        verify(overallSettings).refreshAllSettings(SETTINGS);
    }

    @Test
    public void reloadSettings_givenSettings() {
        reloadSettingsUseCase.execute(SETTINGS);

        verify(overallSettings).refreshAllSettings(SETTINGS);
        verifyNoInteractions(settingRepository);
    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.JudgeRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.exceptions.document.CannotPreviewDocumentException;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

public class DownloadDocumentUseCaseTest extends BaseDomainTest {

    @Mock
    private JudgeRequester judgeRequester;

    @Mock
    private ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    @Mock
    private GenerateJudgeDocumentUseCase generateJudgeDocumentUseCase;

    private DownloadDocumentUseCase downloadDocumentUseCase;

    private static final String DOCUMENT_JUDGE_SID = "JUDGE-SID-123";

    @BeforeEach
    void setUp() {
        downloadDocumentUseCase = new DownloadDocumentUseCase(
                judgeRequester,
                readDocumentAggregateUseCase,
                generateJudgeDocumentUseCase
        );
    }

    @Test
    void executeByDocument_documentWithJudgeSid() {
        // prepare
        String attachmentContent = "Once upon a time, there was ...";
        String contentType = "application/pdf";

        Document document = Document.builder()
                .sid("dummy document")
                .judgeSid(DOCUMENT_JUDGE_SID)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        when(judgeRequester.downloadDocumentBySid(document.getJudgeSid())).thenReturn(
                DocumentFile.builder()
                        .content(attachmentContent.getBytes())
                        .mediaType(contentType)
                        .build()
        );

        // execute
        DocumentFile documentFile = downloadDocumentUseCase.execute(document);

        // verify
        assertThat(documentFile).isEqualTo(DocumentFile.builder()
                .content(attachmentContent.getBytes())
                .mediaType(contentType)
                .build());

        verify(judgeRequester).downloadDocumentBySid(document.getJudgeSid());
        verifyNoInteractions(readDocumentAggregateUseCase);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoMoreInteractions(judgeRequester);
    }

    @Test
    void executeByDocument_documentWithoutJudgeSidInvalidStatus() {
        // prepare
        Document document = Document.builder()
                .sid("dummy document")
                .judgeSid(null)
                .status(DocumentStatus.TAX_ERROR_RETRIED)
                .build();

        // execute & verify
        assertThatThrownBy(() -> downloadDocumentUseCase.execute(document))
                .isInstanceOf(CannotPreviewDocumentException.class)
                .hasMessage(CannotPreviewDocumentException.createInvalidDocumentStatusForPrinting(document).getMessage());

        verifyNoInteractions(judgeRequester);
        verifyNoInteractions(readDocumentAggregateUseCase);
        verifyNoInteractions(generateJudgeDocumentUseCase);
    }

    @Test
    void executeByDocument_documentWithoutJudgeSid() throws Exception{
        // prepare
        String attachmentContent = "Once upon a time, there was ...";
        String contentType = "application/pdf";

        Document document = Document.builder()
                .id(UUID.randomUUID())
                .sid("dummy document")
                .judgeSid(null)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .build();
        when(readDocumentAggregateUseCase.execute(document.getId())).thenReturn(documentAggregate);
        DocumentAggregate documentAggregateWithJudgeSid = documentAggregate.toBuilder()
                .document(document.toBuilder()
                        .judgeSid(DOCUMENT_JUDGE_SID)
                        .build())
                .build();
        when(generateJudgeDocumentUseCase.execute(documentAggregate))
                .thenReturn(documentAggregateWithJudgeSid);
        when(judgeRequester.downloadDocumentBySid(DOCUMENT_JUDGE_SID)).thenReturn(
                DocumentFile.builder()
                        .content(attachmentContent.getBytes())
                        .mediaType(contentType)
                        .build());

        // execute
        DocumentFile documentFile = downloadDocumentUseCase.execute(document);

        // verify
        assertThat(documentFile).isEqualTo(DocumentFile.builder()
                .content(attachmentContent.getBytes())
                .mediaType(contentType)
                .build());
        verify(readDocumentAggregateUseCase).execute(document.getId());
        verify(generateJudgeDocumentUseCase).execute(documentAggregate);
        verify(judgeRequester).downloadDocumentBySid(DOCUMENT_JUDGE_SID);
    }
}

package pt.jumia.services.bill.domain.usecases.receiveddocuments;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.usecases.documents.UpdateDocumentUseCase;

import java.util.Currency;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DeclineReceivedDocumentCancellationUseCaseTest {

    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_CANCELLED)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("even_more_dummy")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .generateId()
                    .category(Category.builder().sid("fake_category").build())
                    .appliedTaxes(List.of(DocumentLine.AppliedTax.builder().build()))
                    .build()))
            .taxCategoryTotals(List.of(TaxCategoryTotal.builder().generateId().build()))
            .build();

    @Mock
    private TaxiRequester taxiRequester;
    @Mock
    private UpdateDocumentUseCase updateDocumentUseCase;
    @InjectMocks
    private DeclineReceivedDocumentCancellationUseCase declineCancellationReceivedDocumentUseCase;

    @Test
    public void declineReceivedDocumentCancellation_taxiRequesterException() {
        DOCUMENT.setStatus(DocumentStatus.TAX_CANCELLED);
        doThrow(TaxiNetworkException.buildResponseNotOk(500, "Some taxi error"))
                .when(taxiRequester)
                .declineReceivedDocumentCancellation(DOCUMENT_AGGREGATE);

        assertThrows(TaxiNetworkException.class, () -> declineCancellationReceivedDocumentUseCase
                .execute(DOCUMENT_AGGREGATE));

        verify(taxiRequester).declineReceivedDocumentCancellation(DOCUMENT_AGGREGATE);
        verifyNoInteractions(updateDocumentUseCase);
    }

    @Test
    public void declineReceivedDocumentCancellation_success() {
        when(updateDocumentUseCase.execute(DOCUMENT.getId(),
                DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build(), true))
                .thenReturn(DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build());

        Document receivedDocument = declineCancellationReceivedDocumentUseCase
                .execute(DOCUMENT_AGGREGATE);

        assertThat(receivedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build()
                        .withoutDbField());

        verify(taxiRequester).declineReceivedDocumentCancellation(DOCUMENT_AGGREGATE);
        verify(updateDocumentUseCase).execute(DOCUMENT.getId(),
                DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build(), true
        );
    }
}

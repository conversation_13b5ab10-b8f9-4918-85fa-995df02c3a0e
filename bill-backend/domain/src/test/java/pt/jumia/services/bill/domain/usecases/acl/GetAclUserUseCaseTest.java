package pt.jumia.services.bill.domain.usecases.acl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.AccessController;
import pt.jumia.services.bill.domain.Permissions;
import pt.jumia.services.bill.domain.entities.User;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.properties.AclProperties;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class GetAclUserUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder()
            .username("username")
            .build();

    @Mock
    private AccessController accessController;

    @Mock
    private AclProperties aclProperties;

    private GetAclUserUseCase getAclUserUseCase;

    @BeforeEach
    public void setUp() {
        when(aclProperties.isSkip()).thenReturn(false);
        when(aclProperties.getAppName()).thenReturn("judge");
        getAclUserUseCase = new GetAclUserUseCase(accessController, aclProperties);
    }

    @Test
    public void skipAcl() {
        when(aclProperties.isSkip()).thenReturn(true);
        getAclUserUseCase = new GetAclUserUseCase(accessController, aclProperties);

        User user = getAclUserUseCase.execute(REQUEST_USER);

        assertThat(user.isCanAccess()).isTrue();
        verifyNoInteractions(accessController);
    }

    @Test
    public void executeUserWithAccess() {
        Map<String, Map<String, List<String>>> permissionsMap = Map.of(
                "APPLICATION", Map.of(
                        "judge", List.of(
                                Permissions.CAN_ACCESS
                        )
                )
        );
        when(accessController.getPermissions(REQUEST_USER)).thenReturn(permissionsMap);

        User user = getAclUserUseCase.execute(REQUEST_USER);

        assertThat(user.isCanAccess()).isTrue();
        verify(accessController).getPermissions(REQUEST_USER);
    }

    @Test
    public void executeUserWithoutAccess() {
        Map<String, Map<String, List<String>>> permissionsMap = Map.of(
                "APPLICATION", Map.of(
                        "bill", List.of(
                                "another-permission-code"
                        )
                )
        );
        when(accessController.getPermissions(REQUEST_USER)).thenReturn(permissionsMap);

        User user = getAclUserUseCase.execute(REQUEST_USER);

        assertThat(user.isCanAccess()).isFalse();
        verify(accessController).getPermissions(REQUEST_USER);
    }

    @Test
    public void executeErrorGettingUserPermissions() {
        doThrow(AclErrorException.build("")).when(accessController).getPermissions(REQUEST_USER);

        assertThrows(UserForbiddenException.class, () -> getAclUserUseCase.execute(REQUEST_USER));

        verify(accessController).getPermissions(REQUEST_USER);
    }

    @Test
    public void executeNullPermissions() {
        when(accessController.getPermissions(REQUEST_USER)).thenReturn(null);

        User user = getAclUserUseCase.execute(REQUEST_USER);

        assertThat(user.isCanAccess()).isFalse();
        verify(accessController).getPermissions(REQUEST_USER);
    }
}

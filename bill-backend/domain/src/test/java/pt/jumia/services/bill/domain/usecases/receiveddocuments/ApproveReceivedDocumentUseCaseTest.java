package pt.jumia.services.bill.domain.usecases.receiveddocuments;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.exceptions.document.InvalidDocumentTransitionException;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.usecases.documents.UpdateDocumentUseCase;

import java.util.Currency;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class ApproveReceivedDocumentUseCaseTest {

    private static final Document DOCUMENT = Document.builder()
        .status(DocumentStatus.TAX_SUCCESS)
        .judgeSid("dummy_judge_sid")
        .country(CountryCode.KE)
        .shop("jumia")
        .type(DocumentType.SALES_INVOICE)
        .sid("abdsabdasbsasfasf")
        .currency(Currency.getInstance("NGN"))
        .issuer(Issuer.builder().generateId().build())
        .receiver(Receiver.builder().generateId()
                .email("<EMAIL>")
                .mobilePhone("+01011122222")
                .build())
        .generateId()
        .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("even_more_dummy")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .generateId()
                    .category(Category.builder().sid("fake_category").build())
                    .appliedTaxes(List.of(DocumentLine.AppliedTax.builder().build()))
                    .build()))
            .taxCategoryTotals(List.of(TaxCategoryTotal.builder().generateId().build()))
            .build();

    @Mock
    private TaxiRequester taxiRequester;
    @Mock
    private UpdateDocumentUseCase updateDocumentUseCase;
    @InjectMocks
    private ApproveReceivedDocumentUseCase approveReceivedDocumentUseCase;

    @Test
    public void approveReceivedDocument_invalidStatusTransition() {

        assertThrows(InvalidDocumentTransitionException.class, () -> approveReceivedDocumentUseCase
                .execute(DOCUMENT_AGGREGATE.toBuilder()
                                .document(DOCUMENT.toBuilder().status(DocumentStatus.TAX_SKIPPED).build())
                                .build()));

        verifyNoInteractions(taxiRequester);
        verifyNoInteractions(updateDocumentUseCase);
    }

    @Test
    public void approveReceivedDocument_taxiRequesterException() {
        DOCUMENT.setStatus(DocumentStatus.TAX_PENDING);
        doThrow(TaxiNetworkException.buildResponseNotOk(500, "Some taxi error"))
                .when(taxiRequester)
                .approveReceivedDocument(DOCUMENT_AGGREGATE);

        assertThrows(TaxiNetworkException.class, () -> approveReceivedDocumentUseCase
                .execute(DOCUMENT_AGGREGATE));

        verify(taxiRequester).approveReceivedDocument(DOCUMENT_AGGREGATE);
        verifyNoInteractions(updateDocumentUseCase);
    }

    @Test
    public void approveReceivedDocument_success() {
        DOCUMENT.setStatus(DocumentStatus.TAX_PENDING);
        when(updateDocumentUseCase.execute(DOCUMENT.getId(),
                DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build(), true))
                .thenReturn(DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build());
        Document receivedDocument = approveReceivedDocumentUseCase.execute(DOCUMENT_AGGREGATE);

        assertThat(receivedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build()
                        .withoutDbField());

        verify(taxiRequester).approveReceivedDocument(DOCUMENT_AGGREGATE);
        verify(updateDocumentUseCase).execute(DOCUMENT.getId(),
                DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_SUCCESS)
                        .build(), true);
    }

}

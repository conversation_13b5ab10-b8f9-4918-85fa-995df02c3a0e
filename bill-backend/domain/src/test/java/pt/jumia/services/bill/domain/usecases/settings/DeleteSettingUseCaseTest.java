package pt.jumia.services.bill.domain.usecases.settings;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.DataEventsNotificator;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DeleteSettingUseCaseTest {

    private static final Setting SETTING = Setting.builder()
            .id(1L)
            .property("some.property")
            .type(Setting.Type.OVERRIDE)
            .value("some-value")
            .overrideKey("some-override-key")
            .description("some-description")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .createdBy("unit-test-user")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedBy("unit-test-user")
            .build();
    @Mock
    private SettingRepository settingRepository;
    @Mock
    private DataEventsNotificator dataEventsNotificator;
    @Mock
    private ReadSettingUseCase readSettingUseCase;
    @InjectMocks
    private DeleteSettingUseCase deleteSettingUseCase;

    @Test
    public void deleteSetting() {
        when(readSettingUseCase.fetchById(SETTING.getId())).thenReturn(SETTING);

        Setting setting = deleteSettingUseCase.execute(SETTING.getId());

        assertThat(setting).isEqualTo(SETTING);
        verify(readSettingUseCase).fetchById(SETTING.getId());
        verify(settingRepository).deleteById(SETTING.getId());
        verify(dataEventsNotificator).notifySettingChanges();
    }

    @Test
    public void deleteSetting_settingNotFound() {
        when(readSettingUseCase.fetchById(SETTING.getId()))
                .thenThrow(EntityNotFoundException.createNotFound(Setting.class, SETTING.getId()));

        assertThatThrownBy(() -> deleteSettingUseCase.execute(SETTING.getId()))
                .isInstanceOf(EntityNotFoundException.class);

        verify(readSettingUseCase).fetchById(SETTING.getId());
        verifyNoInteractions(settingRepository);
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void deleteSetting_defaultSetting() {
        Setting setting = SETTING.toBuilder().type(Setting.Type.DEFAULT).build();

        when(readSettingUseCase.fetchById(setting.getId())).thenReturn(setting);

        assertThatThrownBy(() -> deleteSettingUseCase.execute(SETTING.getId()))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Only settings of type OVERRIDE can be deleted.");
    }
}

package pt.jumia.services.bill.domain.usecases.documentTransformation;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;
import pt.jumia.services.bill.domain.usecases.documenettransformation.CreateDocumentTransformationUseCase;
import pt.jumia.services.bill.domain.usecases.documenettransformation.UpsertDocumentTransformationUseCase;

import java.time.LocalDateTime;
import java.util.Currency;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith( MockitoExtension.class )
public class CreateDocumentTransformationUseCaseTest {

    @Mock
    private DocumentTransformationRepository documentTransformationRepository;

    @InjectMocks
    private CreateDocumentTransformationUseCase createDocumentTransformationUseCase;

    private final Document document = Document.builder()
            .status(DocumentStatus.NEW)
            .judgeSid(null)
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("1234")
            .flow(DocumentFlow.RETAIL)
            .generatedBy("NAV")
            .issuedDate(LocalDateTime.now())
            .currency(Currency.getInstance("UGX"))
            .receiver(Receiver.builder()
                    .type(ReceiverType.CUSTOMER)
                    .legalName("Some receiver")
                    .address(Address.builder()
                            .street("Testing street")
                            .build())
                    .generateId()
                    .build())
            .issuer(Issuer.builder()
                    .type(IssuerType.BUSINESS)
                    .legalName("Some issuer")
                    .email("<EMAIL>")
                    .address(Address.builder()
                            .street("Testing street")
                            .build())
                    .generateId()
                    .build())
            .lineCount(1)
            .originalDocument(null)
            .notes(null)
            .generateId()
            .build();

    private final DocumentTransformation documentTransformation = DocumentTransformation.builder()
            .id(1L)
            .document(document)
            .originalValue("oldValue")
            .newValue("newValue")
            .type(DocumentTransformation.EntityType.ISSUE_DATE_CHANGE)
            .build();


    @Test
    public void executeCreateNewDocumentTransformation() {
        when(documentTransformationRepository.insert(any(DocumentTransformation.class)))
                .thenReturn(documentTransformation);
        DocumentTransformation documentTransformationToBeSaved =
                createDocumentTransformationUseCase.execute(documentTransformation);
        assertThat(documentTransformationToBeSaved.withoutDbFields())
                .usingRecursiveComparison()
                .isEqualTo(documentTransformation.withoutDbFields());

    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ReadDocumentUseCaseTest extends BaseDomainTest {
    private static final UUID DOCUMENT_UUID = UUID.randomUUID();
    private static final String DOCUMENT_SID = "document-sid";
    private static final Document DOCUMENT = Document.builder()
            .id(DOCUMENT_UUID)
            .sid(DOCUMENT_SID)
            .build();

    @Mock
    private DocumentRepository documentRepository;

    @InjectMocks
    private ReadDocumentsUseCase readDocumentsUseCase;

    @Test
    public void readAll_success() {
        DocumentFilter filters = DocumentFilter.builder().build();
        DocumentSortFilters sortFilters = DocumentSortFilters.builder().build();
        PageFilters pageFilters = PageFilters.builder().build();
        readDocumentsUseCase.execute(filters, sortFilters, pageFilters);

        verify(documentRepository).findAll(filters, sortFilters, pageFilters);
    }

    @Test
    public void getCount_success() {
        DocumentFilter filters = DocumentFilter.builder().build();
        when(readDocumentsUseCase.executeCountAll(filters)).thenReturn(5L);

        long result = readDocumentsUseCase.executeCountAll(filters);

        assertEquals(5L, result);

        verify(documentRepository).count(filters);
    }

    @Test
    public void readDocumentById_success() throws EntityNotFoundException {
        when(documentRepository.findById(DOCUMENT_UUID))
                .thenReturn(Optional.of(DOCUMENT));

        Document document = readDocumentsUseCase.readDocumentById(DOCUMENT_UUID);
        assertEquals(DOCUMENT, document);
    }

    @Test
    public void readDocumentById_EntityNotFound() throws EntityNotFoundException {
        when(documentRepository.findById(DOCUMENT_UUID))
                .thenReturn(Optional.empty());

        assertThrows(EntityNotFoundException.class, () ->
                readDocumentsUseCase.readDocumentById(DOCUMENT_UUID));
    }

    @Test
    public void readDocumentBySidInNonFinalErrorStatuses_success() throws EntityNotFoundException {

        List<DocumentStatus> nonFinalErrorStatuses = new ArrayList<>();

        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            if (!DocumentStatus.TAX_ERROR_RETRIED.equals(documentStatus)
                    && !DocumentStatus.TAX_ERROR_ACKED.equals(documentStatus)) {
                nonFinalErrorStatuses.add(documentStatus);
            }
        }

        DocumentFilter documentFilter = DocumentFilter.builder()
                .sid(DOCUMENT_SID)
                .statuses(nonFinalErrorStatuses)
                .build();

        when(documentRepository.findAll(documentFilter))
                .thenReturn(List.of(DOCUMENT));

        Document document = readDocumentsUseCase.readDocumentInNonFinalErrorStatusesBySid(DOCUMENT_SID);
        assertEquals(DOCUMENT, document);
    }

    @Test
    public void readDocumentBySidInNonFinalErrorStatuses_EntityNotFound() throws EntityNotFoundException {
        List<DocumentStatus> nonFinalErrorStatuses = new ArrayList<>();

        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            if (!DocumentStatus.TAX_ERROR_RETRIED.equals(documentStatus)
                    && !DocumentStatus.TAX_ERROR_ACKED.equals(documentStatus)) {
                nonFinalErrorStatuses.add(documentStatus);
            }
        }

        DocumentFilter documentFilter = DocumentFilter.builder()
                .sid(DOCUMENT_SID)
                .statuses(nonFinalErrorStatuses)
                .build();

        when(documentRepository.findAll(documentFilter))
                .thenReturn(List.of());

        assertThrows(EntityNotFoundException.class, () ->
                readDocumentsUseCase.readDocumentInNonFinalErrorStatusesBySid(DOCUMENT_SID));
    }

    @Test
    public void readDocumentBySidAndSuccessStatus_success() throws EntityNotFoundException {
        List<DocumentStatus> successfulStatuses = new ArrayList<>();

        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            if (documentStatus.isSuccessful()) {
                successfulStatuses.add(documentStatus);
            }
        }

        DocumentFilter documentFilter = DocumentFilter.builder()
                .sid(DOCUMENT_SID)
                .statuses(successfulStatuses)
                .build();

        when(documentRepository.findAll(documentFilter))
                .thenReturn(List.of(DOCUMENT));

        Document document = readDocumentsUseCase.readDocumentBySidAndSuccessStatus(DOCUMENT_SID);
        assertEquals(DOCUMENT, document);
    }

    @Test
    public void readDocumentBySidAndSuccessStatus_EntityNotFound() throws EntityNotFoundException {
        List<DocumentStatus> successfulStatuses = new ArrayList<>();

        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            if (documentStatus.isSuccessful()) {
                successfulStatuses.add(documentStatus);
            }
        }
        DocumentFilter documentFilter = DocumentFilter.builder()
                .sid(DOCUMENT_SID)
                .statuses(successfulStatuses)
                .build();

        when(documentRepository.findAll(documentFilter))
                .thenReturn(List.of());

        assertThrows(EntityNotFoundException.class, () ->
                readDocumentsUseCase.readDocumentBySidAndSuccessStatus(DOCUMENT_SID));
    }

}

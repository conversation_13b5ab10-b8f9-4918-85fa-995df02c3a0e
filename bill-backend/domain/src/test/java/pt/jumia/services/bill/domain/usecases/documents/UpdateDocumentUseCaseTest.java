package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Currency;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UpdateDocumentUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder().username("<EMAIL>").build();
    LocalDateTime now = LocalDateTime.now(ZoneOffset.UTC);

    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    @Mock
    private DocumentRepository documentRepository;

    @InjectMocks
    private UpdateDocumentUseCase updateDocumentUseCase;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void executeUpdateDocument_success() {

        when(documentRepository.update(eq(DOCUMENT.getId()), any(Document.class), eq(true))).thenReturn(DOCUMENT);
        Document updatedDoc = updateDocumentUseCase.execute(DOCUMENT.getId(), DOCUMENT, true);
        assertThat(updatedDoc.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(DOCUMENT.withoutDbField());

        verify(documentRepository).update(eq(DOCUMENT.getId()), any(Document.class), eq(true) );
    }

    @Test
    public void executeUpdateDocument_notFound() {

        when(documentRepository.update(eq(DOCUMENT.getId()),
                any(Document.class), eq(true)))
                .thenThrow(EntityNotFoundException.createNotFound(Document.class, DOCUMENT.getId()));
        assertThrows(EntityNotFoundException.class, () ->
                updateDocumentUseCase.execute(DOCUMENT.getId(), DOCUMENT,true));

        verify(documentRepository).update(eq(DOCUMENT.getId()), any(Document.class), eq(true));
    }
}

package pt.jumia.services.bill.domain.usecases.settings;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ReadSettingUseCaseTest {

    private static final Setting SETTING = Setting.builder()
            .id(1L)
            .property("some.property")
            .value("some-value")
            .overrideKey("some-override-key")
            .description("some-description")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .createdBy("user")
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedBy("user")
            .build();

    @Mock
    private SettingRepository settingRepository;
    @InjectMocks
    private ReadSettingUseCase readSettingUseCase;

    @Test
    public void fetchById() {
        when(settingRepository.findById(SETTING.getId())).thenReturn(Optional.of(SETTING));

        Setting setting = readSettingUseCase.fetchById(SETTING.getId());

        verify(settingRepository).findById(SETTING.getId());
        assertThat(setting).isEqualTo(SETTING);
    }

    @Test
    public void fetchById_notFound() {
        when(settingRepository.findById(1L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> readSettingUseCase.fetchById(1L))
                .isInstanceOf(EntityNotFoundException.class);

        verify(settingRepository).findById(1L);
    }

    @Test
    public void fetchAll() {
        when(settingRepository.findAll()).thenReturn(List.of(SETTING));

        List<Setting> settings = readSettingUseCase.fetchAll();

        verify(settingRepository).findAll();
        assertThat(settings).isEqualTo(List.of(SETTING));
    }
}

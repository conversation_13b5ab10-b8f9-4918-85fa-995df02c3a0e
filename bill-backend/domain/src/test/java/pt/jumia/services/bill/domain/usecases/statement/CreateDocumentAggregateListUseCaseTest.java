package pt.jumia.services.bill.domain.usecases.statement;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.ItemType;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;
import pt.jumia.services.bill.domain.settings.DocumentTypeSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.settings.OverrideIssuerSettings;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationChain;
import pt.jumia.services.bill.domain.usecases.statements.CreateDocumentAggregateListUseCase;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CreateDocumentAggregateListUseCaseTest extends BaseDomainTest {

    private final static DocumentLine FIRST_POSITIVE_LINE = DocumentLine.builder()
            .position(0)
            .quantity(new BigDecimal("1.0"))
            .unitOfMeasure(UnitOfMeasure.NOT_APPLICABLE)
            .itemCode("itemCode")
            .itemName("itemName")
            .itemType(ItemType.PRODUCT)
            .category(Category.builder()
                    .sid("sid")
                    .name("name")
                    .build())
            .unitPrice(new BigDecimal("100.0"))
            .totalAmount(new BigDecimal("113.0"))
            .netAmount(new BigDecimal("99.0"))
            .totalTaxAmount(new BigDecimal("14.0"))
            .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                    .taxCategory(TaxCategory.VAT_GENERAL)
                    .taxRate(new BigDecimal("0.14"))
                    .taxAmount(new BigDecimal("14.0"))
                    .build()))
            .discount(DocumentLine.Discount.builder()
                    .amount(new BigDecimal("1.0"))
                    .rate(new BigDecimal("0.10"))
                    .build())
            .build();
    private final static DocumentLine FIRST_NEGATIVE_LINE = DocumentLine.builder()
            .position(0)
            .quantity(new BigDecimal("1.0"))
            .unitOfMeasure(UnitOfMeasure.NOT_APPLICABLE)
            .itemCode("itemCode")
            .itemName("itemName")
            .itemType(ItemType.PRODUCT)
            .category(Category.builder()
                    .sid("sid")
                    .name("name")
                    .build())
            .unitPrice(new BigDecimal("50.0").negate())
            .totalAmount(new BigDecimal("57.0").negate())
            .netAmount(new BigDecimal("50.0").negate())
            .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                    .taxCategory(TaxCategory.VAT_GENERAL)
                    .taxRate(new BigDecimal("0.14"))
                    .taxAmount(new BigDecimal("7.0").negate())
                    .build()))
            .totalTaxAmount(new BigDecimal("7.0").negate())
            .build();
    private final static DocumentLine NEGATIVE_LINE_AS_POSITIVE_VALUE = DocumentLine.builder()
            .position(0)
            .quantity(new BigDecimal("1.0"))
            .unitOfMeasure(UnitOfMeasure.NOT_APPLICABLE)
            .itemCode("itemCode")
            .itemName("itemName")
            .itemType(ItemType.PRODUCT)
            .category(Category.builder()
                    .sid("sid")
                    .name("name")
                    .build())
            .unitPrice(new BigDecimal("50.0"))
            .totalAmount(new BigDecimal("57.0"))
            .netAmount(new BigDecimal("50.0"))
            .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                    .taxCategory(TaxCategory.VAT_GENERAL)
                    .taxRate(new BigDecimal("0.14"))
                    .taxAmount(new BigDecimal("7.0"))
                    .build()))
            .totalTaxAmount(new BigDecimal("7.0"))
            .build();
    private final static DocumentLine SECOND_POSITIVE_LINE = FIRST_POSITIVE_LINE.toBuilder().position(1).build();
    private final static DocumentLine SECOND_NEGATIVE_LINE = FIRST_NEGATIVE_LINE.toBuilder().position(1).build();

    @Mock
    private DocumentValidationChain documentValidationChain;
    @Mock
    private OverallSettings overallSettings;
    @Mock
    private OverrideIssuerSettings overrideIssuerSettings;
    @Mock
    private DocumentTypeSettings documentTypeSettings;

    private CreateDocumentAggregateListUseCase createDocumentAggregateListUseCase;

    @BeforeEach
    void setUp() {
        createDocumentAggregateListUseCase = new CreateDocumentAggregateListUseCase(
                documentValidationChain,
                overallSettings);
    }

    @Test
    void testExecute_positiveDocument() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .totalAmount(new BigDecimal("224"))
                        .netAmount(new BigDecimal("198"))
                        .taxAmount(new BigDecimal("28"))
                        .discountAmount(new BigDecimal("2"))
                        .build())
                .lines(List.of(FIRST_POSITIVE_LINE, SECOND_POSITIVE_LINE))
                .build();
        List<DocumentAggregate> expected = List.of(initialDocument);

        // Execute
        List<DocumentAggregate> actual = createDocumentAggregateListUseCase.execute(initialDocument);

        // Verify
        verify(documentValidationChain).runPreSplitValidations(initialDocument);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void testExecute_negativeDocument() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .totalAmount(new BigDecimal("114").negate())
                        .netAmount(new BigDecimal("100").negate())
                        .taxAmount(new BigDecimal("14").negate())
                        .build())
                .lines(List.of(FIRST_NEGATIVE_LINE, SECOND_NEGATIVE_LINE))
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(new BigDecimal(".14"))
                        .taxAmount(new BigDecimal("7").negate()).build()))
                .build();

        List<DocumentAggregate> expected = List.of(initialDocument);

        when(overallSettings.getDocumentTypeSettings()).thenReturn(documentTypeSettings);
        when(documentTypeSettings.getNegativeDocumentType(any(), anyString())).thenReturn(DocumentType.SALES_CREDIT_NOTE);

        // Execute
        List<DocumentAggregate> actual = createDocumentAggregateListUseCase.execute(initialDocument);

        // Verify
        verify(documentValidationChain).runPreSplitValidations(initialDocument);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void testExecute_hybridDocument() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .totalAmount(new BigDecimal("56"))
                        .netAmount(new BigDecimal("49"))
                        .taxAmount(new BigDecimal("7"))
                        .discountAmount(new BigDecimal("1"))
                        .build())
                .lines(List.of(FIRST_POSITIVE_LINE, FIRST_NEGATIVE_LINE))
                .build();

        DocumentAggregate positiveDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("I-dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .lineCount(1)
                        .totalAmount(new BigDecimal("113"))
                        .netAmount(new BigDecimal("99"))
                        .taxAmount(new BigDecimal("14"))
                        .discountAmount(new BigDecimal("1"))
                        .build())
                .lines(List.of(FIRST_POSITIVE_LINE))
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(new BigDecimal(".14"))
                        .taxAmount(new BigDecimal("14"))
                        .totalAmount(new BigDecimal("113"))
                        .netAmount(new BigDecimal("99"))
                        .build()))
                .build();

        DocumentAggregate negativeDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("C-dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .lineCount(1)
                        .totalAmount(new BigDecimal("57"))
                        .netAmount(new BigDecimal("50"))
                        .taxAmount(new BigDecimal("7"))
                        .build())
                .lines(List.of(NEGATIVE_LINE_AS_POSITIVE_VALUE))
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(new BigDecimal(".14"))
                        .taxAmount(new BigDecimal("7"))
                        .totalAmount(new BigDecimal("57"))
                        .netAmount(new BigDecimal("50"))
                        .build()))
                .build();

        List<DocumentAggregate> expected = List.of(positiveDocument, negativeDocument);

        when(overallSettings.getDocumentTypeSettings()).thenReturn(documentTypeSettings);
        when(documentTypeSettings.getNegativeDocumentType(any(), anyString())).thenReturn(DocumentType.SALES_CREDIT_NOTE);

        // Execute
        List<DocumentAggregate> actual = createDocumentAggregateListUseCase.execute(initialDocument);

        // Verify
        verify(documentValidationChain).runPreSplitValidations(initialDocument);
        verify(documentValidationChain).runPostSplitValidations(actual.get(0));
        verify(documentValidationChain).runPostSplitValidations(actual.get(1));
        assertThat(actual.get(0).withoutDbField()).isEqualTo(expected.get(0).withoutDbField());
        assertThat(actual.get(1).withoutDbField()).isEqualTo(expected.get(1).withoutDbField());
    }

    @Test
    void testExecute_hybridDocument_moreThanTwoLines() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .totalAmount(new BigDecimal("112"))
                        .netAmount(new BigDecimal("98"))
                        .taxAmount(new BigDecimal("14"))
                        .discountAmount(new BigDecimal("2"))
                        .build())
                .lines(List.of(FIRST_POSITIVE_LINE, FIRST_NEGATIVE_LINE, SECOND_POSITIVE_LINE, SECOND_NEGATIVE_LINE))
                .build();

        DocumentAggregate positiveDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("I-dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .lineCount(2)
                        .totalAmount(new BigDecimal("226"))
                        .netAmount(new BigDecimal("198"))
                        .taxAmount(new BigDecimal("28"))
                        .discountAmount(new BigDecimal("2"))
                        .build())
                .lines(List.of(FIRST_POSITIVE_LINE, SECOND_POSITIVE_LINE))
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(new BigDecimal(".14"))
                        .taxAmount(new BigDecimal("28"))
                        .totalAmount(new BigDecimal("226"))
                        .netAmount(new BigDecimal("198"))
                        .build()))
                .build();

        DocumentLine secondNegativeLineAsPositiveValue = NEGATIVE_LINE_AS_POSITIVE_VALUE.toBuilder()
                .position(1).build();
        DocumentAggregate negativeDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("C-dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .lineCount(2)
                        .totalAmount(new BigDecimal("114"))
                        .netAmount(new BigDecimal("100"))
                        .taxAmount(new BigDecimal("14"))
                        .build())
                .lines(List.of(NEGATIVE_LINE_AS_POSITIVE_VALUE, secondNegativeLineAsPositiveValue))
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(new BigDecimal(".14"))
                        .taxAmount(new BigDecimal("14"))
                        .totalAmount(new BigDecimal("114"))
                        .netAmount(new BigDecimal("100"))
                        .build()))
                .build();

        List<DocumentAggregate> expected = List.of(positiveDocument, negativeDocument);

        when(overallSettings.getDocumentTypeSettings()).thenReturn(documentTypeSettings);
        when(documentTypeSettings.getNegativeDocumentType(any(), anyString())).thenReturn(DocumentType.SALES_CREDIT_NOTE);

        // Execute
        List<DocumentAggregate> actual = createDocumentAggregateListUseCase.execute(initialDocument);

        // Verify
        verify(documentValidationChain).runPreSplitValidations(initialDocument);
        verify(documentValidationChain).runPostSplitValidations(actual.get(0));
        verify(documentValidationChain).runPostSplitValidations(actual.get(1));
        assertThat(actual.get(0).withoutDbField()).isEqualTo(expected.get(0).withoutDbField());
        assertThat(actual.get(1).withoutDbField()).isEqualTo(expected.get(1).withoutDbField());
    }

    @Test
    void testExecute_hybridDocument_multipleTaxCategories() {
        // Prepare
        DocumentLine firstPositiveLine = FIRST_POSITIVE_LINE.toBuilder()
                .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .taxRate(new BigDecimal("0.14"))
                                .taxAmount(new BigDecimal("14.0"))
                                .build(),
                        DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.OTHER_AMOUNT)
                                .taxRate(new BigDecimal("0.1"))
                                .taxAmount(new BigDecimal("10.0"))
                                .build(),
                        DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.OTHER_RATE)
                                .taxRate(new BigDecimal("0.01"))
                                .taxAmount(new BigDecimal("1.0"))
                                .build())).build();
        DocumentLine secondPositiveLine = SECOND_POSITIVE_LINE.toBuilder()
                .appliedTaxes(List.of(DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .taxRate(new BigDecimal("0.14"))
                                .taxAmount(new BigDecimal("14.0"))
                                .build(),
                        DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.OTHER_AMOUNT)
                                .taxRate(new BigDecimal("0.1"))
                                .taxAmount(new BigDecimal("10.0"))
                                .build(),
                        DocumentLine.AppliedTax.builder()
                                .taxCategory(TaxCategory.EXEMPT)
                                .taxRate(new BigDecimal("0.01"))
                                .taxAmount(new BigDecimal("1.0"))
                                .build())).build();
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .totalAmount(new BigDecimal("112"))
                        .netAmount(new BigDecimal("98"))
                        .taxAmount(new BigDecimal("14"))
                        .build())
                .lines(List.of(firstPositiveLine, FIRST_NEGATIVE_LINE, secondPositiveLine, SECOND_NEGATIVE_LINE))
                .build();

        DocumentAggregate positiveDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("I-dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .lineCount(2)
                        .totalAmount(new BigDecimal("226"))
                        .netAmount(new BigDecimal("198"))
                        .taxAmount(new BigDecimal("28"))
                        .discountAmount(new BigDecimal("2"))
                        .build())
                .lines(List.of(firstPositiveLine, secondPositiveLine))
                .taxCategoryTotals(List.of(
                        TaxCategoryTotal.builder()
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .taxRate(new BigDecimal(".14"))
                                .taxAmount(new BigDecimal("28"))
                                .totalAmount(new BigDecimal("226"))
                                .netAmount(new BigDecimal("198"))
                                .build(),
                        TaxCategoryTotal.builder().taxCategory(TaxCategory.OTHER_AMOUNT)
                                .taxRate(new BigDecimal("0.1"))
                                .taxAmount(new BigDecimal("20.0"))
                                .totalAmount(new BigDecimal("226"))
                                .netAmount(new BigDecimal("198"))
                                .build(),
                        TaxCategoryTotal.builder().taxCategory(TaxCategory.OTHER_RATE)
                                .taxRate(new BigDecimal("0.01"))
                                .taxAmount(new BigDecimal("1.0"))
                                .totalAmount(new BigDecimal("226"))
                                .netAmount(new BigDecimal("198"))
                                .build(),
                        TaxCategoryTotal.builder().taxCategory(TaxCategory.EXEMPT)
                                .taxRate(new BigDecimal("0.01"))
                                .taxAmount(new BigDecimal("1.0"))
                                .totalAmount(new BigDecimal("226"))
                                .netAmount(new BigDecimal("198"))
                                .build()
                )).build();

        DocumentLine secondNegativeLineAsPositiveValue = NEGATIVE_LINE_AS_POSITIVE_VALUE.toBuilder()
                .position(1).build();
        DocumentAggregate negativeDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("C-dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .lineCount(2)
                        .totalAmount(new BigDecimal("114"))
                        .netAmount(new BigDecimal("100"))
                        .taxAmount(new BigDecimal("14"))
                        .build())
                .lines(List.of(NEGATIVE_LINE_AS_POSITIVE_VALUE, secondNegativeLineAsPositiveValue))
                .taxCategoryTotals(List.of(TaxCategoryTotal.builder()
                        .taxCategory(TaxCategory.VAT_GENERAL)
                        .taxRate(new BigDecimal(".14"))
                        .taxAmount(new BigDecimal("14"))
                        .totalAmount(new BigDecimal("114"))
                        .netAmount(new BigDecimal("100"))
                        .build()))
                .build();

        List<DocumentAggregate> expected = List.of(positiveDocument, negativeDocument);

        when(overallSettings.getDocumentTypeSettings()).thenReturn(documentTypeSettings);
        when(documentTypeSettings.getNegativeDocumentType(any(), anyString())).thenReturn(DocumentType.SALES_CREDIT_NOTE);

        // Execute
        List<DocumentAggregate> actual = createDocumentAggregateListUseCase.execute(initialDocument);

        // Verify
        verify(documentValidationChain).runPreSplitValidations(initialDocument);
        verify(documentValidationChain).runPostSplitValidations(actual.get(0));
        verify(documentValidationChain).runPostSplitValidations(actual.get(1));
        assertThat(actual.get(0).withoutDbField()).isEqualTo(expected.get(0).withoutDbField());
        assertThat(actual.get(1).withoutDbField()).isEqualTo(expected.get(1).withoutDbField());
    }
}

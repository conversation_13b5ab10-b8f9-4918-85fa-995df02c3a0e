package pt.jumia.services.bill.domain.usecases.receiveddocuments;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.exceptions.document.InvalidDocumentTransitionException;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.usecases.documents.UpdateDocumentUseCase;

import java.util.Currency;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RejectReceivedDocumentUseCaseTest {
    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("even_more_dummy")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .generateId()
                    .category(Category.builder().sid("fake_category").build())
                    .appliedTaxes(List.of(DocumentLine.AppliedTax.builder().build()))
                    .build()))
            .taxCategoryTotals(List.of(TaxCategoryTotal.builder().generateId().build()))
            .build();

    @Mock
    private TaxiRequester taxiRequester;
    @Mock
    private UpdateDocumentUseCase updateDocumentUseCase;
    @InjectMocks
    private RejectReceivedDocumentUseCase rejectReceivedDocumentUseCase;

    @Test
    public void rejectDocument_invalidStatusTransition() {
        DOCUMENT.setStatus(DocumentStatus.TAX_SUCCESS);
        
        assertThrows(InvalidDocumentTransitionException.class, () -> rejectReceivedDocumentUseCase
                .execute(DOCUMENT_AGGREGATE.toBuilder()
                                .document(DOCUMENT.toBuilder().status(DocumentStatus.TAX_CANCELLED).build())
                                .build(),
                        "reason"));

        verifyNoInteractions(taxiRequester);
        verifyNoInteractions(updateDocumentUseCase);
    }

    @Test
    public void rejectDocument_taxiRequesterException() {
        DOCUMENT.setStatus(DocumentStatus.TAX_SUCCESS);

        doThrow(TaxiNetworkException.buildResponseNotOk(500, "Some taxi error"))
                .when(taxiRequester)
                .rejectReceivedDocument(DOCUMENT_AGGREGATE,
                        "reason");

        assertThrows(TaxiNetworkException.class, () -> rejectReceivedDocumentUseCase
                .execute(DOCUMENT_AGGREGATE,
                        "reason"));

        verify(taxiRequester).rejectReceivedDocument(DOCUMENT_AGGREGATE,
                "reason");
        verifyNoInteractions(updateDocumentUseCase);
    }

    @Test
    public void rejectDocument_success() {
        when(updateDocumentUseCase.execute(DOCUMENT.getId(),
                DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_REJECTED)
                        .build(), true))
                .thenReturn(DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_REJECTED)
                        .build());
        Document receivedDocument = rejectReceivedDocumentUseCase.execute(DOCUMENT_AGGREGATE, "reason");

        assertThat(receivedDocument.withoutDbField())
                .usingRecursiveComparison()
                .isEqualTo(DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_REJECTED)
                        .build()
                        .withoutDbField());

        verify(taxiRequester).rejectReceivedDocument(DOCUMENT_AGGREGATE,
                "reason");
        verify(updateDocumentUseCase).execute(DOCUMENT.getId(),
                DOCUMENT.toBuilder()
                        .reviewed(false)
                        .status(DocumentStatus.TAX_REJECTED)
                        .build(), true);
    }
}

package pt.jumia.services.bill.domain.usecases.documents.processors;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.caches.AfromsCache;
import pt.jumia.services.bill.domain.entities.AfromsProductDetails;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.ItemType;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AfromsProcessorTest extends BaseDomainTest {

    private static final Document DOCUMENT = Document.builder()
            .sid("sid-test")
            .country(CountryCode.EG)
            .shop("jumia")
            .status(DocumentStatus.TAX_SUBMITTED_INVALID)
            .build();

    private static final List<DocumentLine> DOCUMENT_LINES_CATEGORY_NOT_EMPTY = List.of(DocumentLine.builder()
            .itemType(ItemType.PRODUCT)
            .category(Category.builder()
                    .name("Test-Category-Line")
                    .sid("29ab610d-313f-4d06-8e44-d2bf894f7199")
                    .taxAuthorityCode("tax0-authority-code")
                    .build())
            .itemCode("test-item-code")
            .skuVariant("variant")
            .document(DOCUMENT)
            .build());

    private static final List<DocumentLine> DOCUMENT_LINES_CATEGORY_EMPTY = List.of(DocumentLine.builder()
            .itemType(ItemType.PRODUCT)
            .category(Category.builder().build())
            .itemCode("test-item-code")
            .skuVariant("variant")
            .document(DOCUMENT)
            .build());

    private static final DocumentAggregate AGGREGATE = DocumentAggregate.builder()
            .document(DOCUMENT)
            .build();

    private static final AfromsProductDetails AFR_OMS_PRODUCT_DETAILS = AfromsProductDetails.builder()
            .ucrId("UCR-id-test")
            .ucrPath("UCR-path-test")
            .build();

    @Mock
    private AfromsCache afromsCache;

    private AfromsProcessor afromsProcessor;

    @BeforeEach
    void setUp() {
        afromsProcessor = new AfromsProcessor(afromsCache);
    }

    @Test
    public void testOmsProcessorTest_withCategory() {
        DocumentAggregate documentAggregate = AGGREGATE.toBuilder().lines(DOCUMENT_LINES_CATEGORY_NOT_EMPTY).build();
        afromsProcessor.process(documentAggregate);

        assertThat(documentAggregate.getLines().get(0).getCategory().getName()).isEqualTo("Test-Category-Line");
    }

    @Test
    public void testOmsProcessorTest_withOutCategory() {
        DocumentAggregate documentAggregate = AGGREGATE.toBuilder().lines(DOCUMENT_LINES_CATEGORY_EMPTY).build();

        when(afromsCache.getAfromsCacheProductDetails(any(CountryCode.class), anyString(), anyString(), anyString()))
                .thenReturn(AFR_OMS_PRODUCT_DETAILS);

        afromsProcessor.process(documentAggregate);

        verify(afromsCache, atLeastOnce())
                .getAfromsCacheProductDetails(any(CountryCode.class), anyString(), anyString(), anyString());
        assertThat(documentAggregate.getLines().get(0).getCategory().getName()).isEqualTo(AFR_OMS_PRODUCT_DETAILS.getUcrPath());
        assertThat(documentAggregate.getLines().get(0).getCategory().getSid()).isEqualTo(AFR_OMS_PRODUCT_DETAILS.getUcrId());
    }

}

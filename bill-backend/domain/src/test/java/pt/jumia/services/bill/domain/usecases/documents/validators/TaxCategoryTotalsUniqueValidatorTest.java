package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

public class TaxCategoryTotalsUniqueValidatorTest extends BaseDomainTest {

    UUID DOCUMENT_ID = UUID.randomUUID();

    private final TaxCategoryTotalsUniqueValidator
            taxCategoryTotalsUniqueValidator = new TaxCategoryTotalsUniqueValidator();

    @Test
    public void testValidate_taxCategoryTotals_is_unique() {
        Document document = Document.builder().id(DOCUMENT_ID).build();
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .taxCategoryTotals(List.of(
                        TaxCategoryTotal.builder()
                                .document(document)
                                .taxFixedAmount(BigDecimal.valueOf(20.15))
                                .taxRate(BigDecimal.valueOf(0.13))
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .build(),
                        TaxCategoryTotal.builder()
                                .document(document)
                                .taxFixedAmount(BigDecimal.valueOf(10.15))
                                .taxRate(BigDecimal.valueOf(0.1))
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .build()
                ))
                .build();

        List<ValidationError> result = taxCategoryTotalsUniqueValidator.validate(documentAggregate);

        assertThat(result).isEmpty();
    }

    @Test
    public void testValidate_taxCategoryTotals_is_not_unique_from_payload() {
        Document document = Document.builder().id(DOCUMENT_ID).build();
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .taxCategoryTotals(List.of(
                        TaxCategoryTotal.builder()
                                .document(document)
                                .taxFixedAmount(BigDecimal.valueOf(20.15))
                                .taxRate(BigDecimal.valueOf(0.13))
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .build(),
                        TaxCategoryTotal.builder()
                                .document(document)
                                .taxFixedAmount(BigDecimal.valueOf(20.15))
                                .taxRate(BigDecimal.valueOf(0.13))
                                .taxCategory(TaxCategory.VAT_GENERAL)
                                .build()
                ))
                .build();

        List<ValidationError> result = taxCategoryTotalsUniqueValidator.validate(documentAggregate);

        assertThat(result).extracting(ValidationError::getCode).containsExactly(ErrorCode.DUPLICATE_ENTRY);
    }
}

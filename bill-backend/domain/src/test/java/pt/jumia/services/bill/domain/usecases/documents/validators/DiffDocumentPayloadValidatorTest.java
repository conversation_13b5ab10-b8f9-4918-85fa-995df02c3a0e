package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

public class DiffDocumentPayloadValidatorTest extends BaseDomainTest {

    Document DOCUMENT = Document.builder()
            .sid("1234")
            .status(DocumentStatus.TAX_SUBMITTED_INVALID)
            .issuer(Issuer.builder()
                    .branch("Some branch")
                    .build())
            .build();

    DocumentLine DOCUMENT_LINE = DocumentLine.builder()
            .itemCode("Item Code")
            .totalTaxAmount(BigDecimal.valueOf(120))
            .netAmount(BigDecimal.valueOf(100))
            .build();

    DocumentAggregate CURRENT_AGGREGATE = DocumentAggregate.builder()
            .document(DOCUMENT)
            .lines(List.of())
            .build();

    DocumentAggregate INCOME_AGGREGATE = DocumentAggregate.builder()
            .document(DOCUMENT)
            .lines(List.of())
            .build();

    @Mock
    private DocumentAggregateRepository documentAggregateRepository;

    private DiffDocumentPayloadValidator diffDocumentPayloadValidator;

    @BeforeEach
    void setUp() {
        diffDocumentPayloadValidator = new DiffDocumentPayloadValidator(documentAggregateRepository);
    }

    @Test
    public void testValidate_withDuplicateData_returnsExpectedError() {
        DocumentFilter filter = DocumentFilter.builder()
                .sid(INCOME_AGGREGATE.getDocument().getSid())
                .statuses(DocumentStatus.getErrorStatuses())
                .include(List.of(Document.Details.values()))
                .build();

        DocumentLine documentLine = DOCUMENT_LINE.toBuilder().quantity(BigDecimal.valueOf(1)).build();

        DocumentAggregate incomeAggregate = CURRENT_AGGREGATE.toBuilder().lines(List.of(documentLine)).build();

        when(documentAggregateRepository.findAll(filter)).thenReturn(
                List.of(CURRENT_AGGREGATE.toBuilder().lines(List.of(documentLine)).build())
        );

        List<ValidationError> result = diffDocumentPayloadValidator.validate(incomeAggregate);

        assertThat(result).extracting(ValidationError::getCode).containsExactly(ErrorCode.DUPLICATE_DOCUMENT_PAYLOAD);
    }

    @Test
    public void testValidate_withDifferentData() {
        DocumentFilter filter = DocumentFilter.builder()
                .sid(INCOME_AGGREGATE.getDocument().getSid())
                .statuses(DocumentStatus.getErrorStatuses())
                .include(List.of(Document.Details.values()))
                .build();

        DocumentLine documentLine = DOCUMENT_LINE.toBuilder().quantity(BigDecimal.valueOf(1)).build();

        DocumentLine differentDocumentLine = DOCUMENT_LINE.toBuilder().quantity(BigDecimal.valueOf(2)).build();

        DocumentAggregate incomeAggregate = CURRENT_AGGREGATE.toBuilder().lines(List.of(differentDocumentLine)).build();

        when(documentAggregateRepository.findAll(filter)).thenReturn(
                List.of(CURRENT_AGGREGATE.toBuilder().lines(List.of(documentLine)).build())
        );

        List<ValidationError> result = diffDocumentPayloadValidator.validate(incomeAggregate);

        assertThat(result).isEmpty();
    }

    @Test
    public void testValidate_withDifferentIssuerBranchId() {
        // Prepare
        DocumentFilter filter = DocumentFilter.builder()
                .sid(INCOME_AGGREGATE.getDocument().getSid())
                .statuses(DocumentStatus.getErrorStatuses())
                .include(List.of(Document.Details.values()))
                .build();

        DocumentAggregate incomeAggregate = CURRENT_AGGREGATE.toBuilder()
                .document(CURRENT_AGGREGATE.getDocument().toBuilder()
                        .issuer(CURRENT_AGGREGATE.getDocument().getIssuer().toBuilder()
                                .branch("Other branch")
                                .build())
                        .build())
                .build();

        when(documentAggregateRepository.findAll(filter)).thenReturn(List.of(CURRENT_AGGREGATE));

        // Execute
        List<ValidationError> result = diffDocumentPayloadValidator.validate(incomeAggregate);

        // Verify
        assertThat(result).isEmpty();
    }
}

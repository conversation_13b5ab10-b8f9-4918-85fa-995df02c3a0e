package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class SummaryEqualLinesValidatorTest extends BaseDomainTest {

    private final SummaryEqualLinesValidator summaryEqualLinesValidator = new SummaryEqualLinesValidator();

    @Test
    public void validate_summary_is_equals_in_lines() {

        List<ValidationError> result = summaryEqualLinesValidator.validate(DocumentAggregate.builder()
                .document(Document.builder()
                        .netAmount(BigDecimal.valueOf(4000))
                        .totalAmount(BigDecimal.valueOf(5000))
                        .taxAmount(BigDecimal.valueOf(1000))
                        .build())
                .lines(List.of(
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build(),
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build()
                ))
                .build());

        assertThat(result).isEmpty();

    }

    @Test
    public void validate_summary_net_amount_is_not_equal_in_lines() {

        List<ValidationError> netAmountNotEqualInLines = summaryEqualLinesValidator.validate(DocumentAggregate.builder()
                .document(Document.builder()
                        .netAmount(BigDecimal.valueOf(5000))
                        .totalAmount(BigDecimal.valueOf(5000))
                        .taxAmount(BigDecimal.valueOf(1000))
                        .build())
                .lines(List.of(
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build(),
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build()
                ))
                .build());

        assertThat(netAmountNotEqualInLines).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.NET_AMOUNT_NOT_EQUAL_NET_AMOUNT_LINES);

    }

    @Test
    public void validate_summary_total_amount_is_not_equal_in_lines() {

        List<ValidationError> netAmountNotEqualInLines = summaryEqualLinesValidator.validate(DocumentAggregate.builder()
                .document(Document.builder()
                        .netAmount(BigDecimal.valueOf(4000))
                        .totalAmount(BigDecimal.valueOf(6000))
                        .taxAmount(BigDecimal.valueOf(1000))
                        .build())
                .lines(List.of(
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build(),
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build()
                ))
                .build());

        assertThat(netAmountNotEqualInLines).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.TOTAL_AMOUNT_NOT_EQUAL_TOTAL_AMOUNT_LINES);

    }

    @Test
    public void validate_summary_tax_amount_is_not_equal_in_lines() {

        List<ValidationError> netAmountNotEqualInLines = summaryEqualLinesValidator.validate(DocumentAggregate.builder()
                .document(Document.builder()
                        .netAmount(BigDecimal.valueOf(4000))
                        .totalAmount(BigDecimal.valueOf(5000))
                        .taxAmount(BigDecimal.valueOf(500))
                        .build())
                .lines(List.of(
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build(),
                        DocumentLine.builder()
                                .netAmount(BigDecimal.valueOf(2000))
                                .totalAmount(BigDecimal.valueOf(2500))
                                .totalTaxAmount(BigDecimal.valueOf(500))
                                .build()
                ))
                .build());

        assertThat(netAmountNotEqualInLines).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.TAX_AMOUNT_NOT_EQUAL_TAX_AMOUNT_LINES);

    }
}

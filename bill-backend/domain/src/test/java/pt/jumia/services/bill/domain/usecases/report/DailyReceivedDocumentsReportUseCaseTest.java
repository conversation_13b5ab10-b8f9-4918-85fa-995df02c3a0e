package pt.jumia.services.bill.domain.usecases.report;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.CommunicationsRequester;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.settings.DailyReceivedDocumentsReportSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentsUseCase;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DailyReceivedDocumentsReportUseCaseTest {
    @Mock
    private ReadDocumentsUseCase readDocumentsUseCase;
    @Mock
    private CommunicationsRequester communicationsRequester;
    @Mock
    private OverallSettings overallSettings;
    @Mock
    private DailyReceivedDocumentsReportSettings dailyReceivedDocumentsReportSettings;

    private DailyReceivedDocumentsReportUseCase dailyReceivedDocumentsReportUseCase;

    @BeforeEach
    void setUp() {
        dailyReceivedDocumentsReportUseCase = new DailyReceivedDocumentsReportUseCase(
                readDocumentsUseCase,
                communicationsRequester,
                overallSettings
        );

        when(overallSettings.getDailyReceivedDocumentsReportSettings())
                .thenReturn(dailyReceivedDocumentsReportSettings);
    }

    @Test
    void executeGenerateDailyReceivedDocumentsReport() {
        //Mock settings
        when(dailyReceivedDocumentsReportSettings.getEnabledBusinessLines()).thenReturn(
                List.of(BusinessLine.builder()
                                .countryCode(CountryCode.EG)
                                .shop("jumia")
                                .build(),
                        BusinessLine.builder()
                                .countryCode(CountryCode.UG)
                                .shop("jumia")
                                .build())
        );

        when(dailyReceivedDocumentsReportSettings.getCommunicationEmailsForBusinessLine("jumia", "EG"))
                .thenReturn(List.of("<EMAIL>"));

        when(dailyReceivedDocumentsReportSettings.getCommunicationEmailsForBusinessLine("jumia", "UG"))
                .thenReturn(List.of("<EMAIL>"));
        when(dailyReceivedDocumentsReportSettings.getReportTimeFrameInHours()).thenReturn(36);

        //Mock count requests
        when(readDocumentsUseCase.executeCountAll(
                any(DocumentFilter.class)
        )).thenReturn(25L);

        LocalDateTime reportRequestTime = LocalDateTime.now();
        LocalDateTime reportStart = reportRequestTime.minusHours(36);
        dailyReceivedDocumentsReportUseCase.generateReport(reportRequestTime);

        //verify settings calls
        verify(dailyReceivedDocumentsReportSettings).getReportTimeFrameInHours();
        verify(dailyReceivedDocumentsReportSettings).getEnabledBusinessLines();
        verify(dailyReceivedDocumentsReportSettings)
                .getCommunicationEmailsForBusinessLine("jumia", "EG");
        verify(dailyReceivedDocumentsReportSettings)
                .getCommunicationEmailsForBusinessLine("jumia", "UG");

        //verify count requests
        //unreviewed first 12 hours
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.UG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .reviewed(false)
                .receivedDateFrom(reportStart)
                .receivedDateTo(reportRequestTime.minusHours(24))
                .build());

        //unreviewed second 12 hours
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.EG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .reviewed(false)
                .receivedDateFrom(reportRequestTime.minusHours(24))
                .receivedDateTo(reportRequestTime.minusHours(12))
                .build());

        //unreviewed third 12 hours
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.EG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .reviewed(false)
                .receivedDateFrom(reportRequestTime.minusHours(12))
                .receivedDateTo(reportRequestTime)
                .build());

        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.UG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .reviewed(true)
                .updatedAtFrom(reportStart)
                .updatedAtTo(reportRequestTime)
                .status(DocumentStatus.TAX_SUCCESS)
                .build());
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.EG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .reviewed(true)
                .updatedAtFrom(reportStart)
                .updatedAtTo(reportRequestTime)
                .status(DocumentStatus.TAX_SUCCESS)
                .build());

        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.UG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .reviewed(true)
                .updatedAtFrom(reportStart)
                .updatedAtTo(reportRequestTime)
                .status(DocumentStatus.TAX_REJECTED)
                .build());
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.EG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .reviewed(true)
                .updatedAtFrom(reportStart)
                .updatedAtTo(reportRequestTime)
                .status(DocumentStatus.TAX_REJECTED)
                .build());

        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.UG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .updatedAtFrom(reportStart)
                .updatedAtTo(reportRequestTime)
                .status(DocumentStatus.TAX_CANCELLED)
                .build());
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter
                .builder()
                .countryCode(CountryCode.EG)
                .flow(DocumentFlow.RECEIVED)
                .shop("jumia")
                .updatedAtFrom(reportStart)
                .updatedAtTo(reportRequestTime)
                .status(DocumentStatus.TAX_CANCELLED)
                .build());

        Map<DocumentStatus, Long> statusesMap = new HashMap<DocumentStatus, Long>();
        for (DocumentStatus documentStatus : DocumentStatus.getReceivedDocumentsStatusForDailyEmail()) {
            statusesMap.put(documentStatus, 25L);
        }

        DailyReceivedDocumentsReport.ReviewReport reviewReport = DailyReceivedDocumentsReport.ReviewReport
                .builder()
                .reportFirstRangeStartDate(reportStart)
                .reportSecondRangeStartDate(reportRequestTime.minusHours(24))
                .reportThirdRangeStartDate(reportRequestTime.minusHours(12))
                .reportRangeEndDate(reportRequestTime)
                .countOfUnreviewedDocumentsForTheFirstThird(25L)
                .countOfUnreviewedDocumentsForTheSecondThird(25L)
                .countOfUnreviewedDocumentsForTheThirdThird(25L)
                .build();

        //verify emails sent
        verify(communicationsRequester).sendEmailByReceivedDocumentsReportToReceiver(
                DailyReceivedDocumentsReport.builder()
                        .businessLine(BusinessLine.builder()
                                .countryCode(CountryCode.EG)
                                .shop("jumia")
                                .build())
                        .reviewReport(reviewReport)
                        .statusesReport(DailyReceivedDocumentsReport.StatusesReport.builder()
                                .reportRangeEndDate(reportRequestTime)
                                .reportRangeStartDate(reportStart)
                                .range(36)
                                .statusesMap(statusesMap)
                                .build())
                        .build(),
                "<EMAIL>");
        verify(communicationsRequester).sendEmailByReceivedDocumentsReportToReceiver(
                DailyReceivedDocumentsReport.builder()
                        .businessLine(BusinessLine.builder()
                                .countryCode(CountryCode.UG)
                                .shop("jumia")
                                .build())
                        .reviewReport(reviewReport)
                        .statusesReport(DailyReceivedDocumentsReport.StatusesReport.builder()
                                .reportRangeEndDate(reportRequestTime)
                                .reportRangeStartDate(reportStart)
                                .range(36)
                                .statusesMap(statusesMap)
                                .build())
                        .build(),
                "<EMAIL>");
    }
}

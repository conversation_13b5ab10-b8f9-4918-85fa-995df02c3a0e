package pt.jumia.services.bill.domain.usecases.acl;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.Permissions;
import pt.jumia.services.bill.domain.entities.User;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UserPermissionValidationUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder()
            .username("username")
            .build();

    @Mock
    private GetAclUserUseCase getAclUserUseCase;

    @InjectMocks
    private UserPermissionValidationUseCase userPermissionValidationUseCase;

    @Test
    public void checkCanAccessWithPermission() {
        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .canAccess(true)
                .build());

        userPermissionValidationUseCase.checkCanAccessOrThrow(REQUEST_USER);

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }

    @Test
    public void checkCanAccessWithoutPermission() {
        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .canAccess(false)
                .build());

        assertThrows(UserForbiddenException.class, () -> userPermissionValidationUseCase.checkCanAccessOrThrow(REQUEST_USER));

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }

    @Test
    public void checkCanManageDocumentsWithoutPermission() {
        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .countriesPermissionsList(Map.of(CountryCode.EG, List.of(Permissions.CAN_MANAGE_DOCUMENTS),
                CountryCode.UG, List.of(Permissions.CAN_MANAGE_DOCUMENTS)))
                .build());

        assertThrows(UserForbiddenException.class, () -> userPermissionValidationUseCase.
                checkCanManageDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.DE));

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }

    @Test
    public void checkCanSeeDocumentsWithPermission() {
        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .countriesPermissionsList(Map.of(CountryCode.DE, List.of(Permissions.CAN_VIEW_DOCUMENTS)))
                .build());

        userPermissionValidationUseCase.checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.DE);

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }

    @Test
    public void checkGetCountriesWithCanViewDocumentsPermission() {
        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .countriesPermissionsList(Map.of(CountryCode.DE, List.of(Permissions.CAN_VIEW_DOCUMENTS), CountryCode.EG, List.of(Permissions.CAN_VIEW_DOCUMENTS)))
                .build());

        List<CountryCode> countries = userPermissionValidationUseCase.getCountriesCanViewDocumentsOrThrow(REQUEST_USER);
        assertThat(countries).containsExactlyInAnyOrder(CountryCode.EG, CountryCode.DE);

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }

    @Test
    public void checkCanSeeDocumentsOrThrowWithoutPermission() {
        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .countriesPermissionsList(Map.of(CountryCode.EG, List.of(Permissions.CAN_MANAGE_DOCUMENTS),
                        CountryCode.UG, List.of(Permissions.CAN_MANAGE_DOCUMENTS)))
                .build());

        assertThrows(UserForbiddenException.class, () -> userPermissionValidationUseCase.
                checkCanViewDocumentsByCountryCodeOrThrow(REQUEST_USER, CountryCode.EG));

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }

    @Test
    public void checkCanManageSettingsWithPermission() {
        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .canManageSettings(true)
                .build());

        userPermissionValidationUseCase.checkCanManageSettingsOrThrow(REQUEST_USER);

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }

    @Test
    public void checkCanManageSettingsWithoutPermission() {

        when(getAclUserUseCase.execute(REQUEST_USER)).thenReturn(User.builder()
                .username(REQUEST_USER.getUsername())
                .canManageSettings(false)
                .build());

        assertThatThrownBy(() -> userPermissionValidationUseCase.checkCanManageSettingsOrThrow(REQUEST_USER))
                .isInstanceOf(UserForbiddenException.class)
                .hasMessage("The user '" + REQUEST_USER.getUsername() + "' does not have permission 'manage_settings' on Bill");

        verify(getAclUserUseCase).execute(REQUEST_USER);
    }
}

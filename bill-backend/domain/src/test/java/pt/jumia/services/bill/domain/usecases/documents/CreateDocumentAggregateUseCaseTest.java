package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.KafkaProducer;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.exceptions.CreateStatementException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.IssuerTemplateRepository;
import pt.jumia.services.bill.domain.settings.KafkaSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.settings.OverrideIssuerSettings;
import pt.jumia.services.bill.domain.usecases.documentapilogs.UpsertDocumentApiLogUseCase;
import pt.jumia.services.bill.domain.usecases.documents.processors.DocumentProcessorChain;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationChain;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationException;
import pt.jumia.services.bill.domain.usecases.documents.validators.ValidationError;
import pt.jumia.services.bill.domain.usecases.metrics.MonitoringUseCase;
import pt.jumia.services.bill.domain.usecases.statements.CreateDocumentAggregateListUseCase;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

class CreateDocumentAggregateUseCaseTest extends BaseDomainTest {

    @Mock
    private DocumentAggregateRepository documentAggregateRepository;
    @Mock
    private DocumentRepository documentRepository;
    @Mock
    private DocumentValidationChain documentValidationChain;
    @Mock
    private DocumentProcessorChain documentProcessorChain;
    @Mock
    private CreateDocumentAggregateListUseCase createDocumentAggregateListUseCase;
    @Mock
    private TaxiRequester taxiRequester;
    @Mock
    private MonitoringUseCase monitoringUseCase;
    @Mock
    private IssuerTemplateRepository issuerTemplateRepository;
    @Mock
    private OverallSettings overallSettings;
    @Mock
    private OverrideIssuerSettings overrideIssuerSettings;
    @Mock
    private KafkaSettings kafkaSettings;
    @Mock
    private UpsertDocumentApiLogUseCase upsertDocumentApiLogUseCase;
    @Mock
    private DocumentResendUseCase documentResendUseCase;
    @Mock
    private KafkaProducer billProducer;

    private CreateDocumentAggregateUseCase createDocumentAggregateUseCase;

    @BeforeEach
    void setUp() {
        createDocumentAggregateUseCase = new CreateDocumentAggregateUseCase(
                documentAggregateRepository,
                issuerTemplateRepository,
                documentRepository,
                documentValidationChain,
                documentProcessorChain,
                createDocumentAggregateListUseCase,
                overallSettings,
                taxiRequester,
                monitoringUseCase,
                upsertDocumentApiLogUseCase,
                billProducer,
                documentResendUseCase
        );
    }

    @Test
    void testExecute_validSalesInvoice_runsValidationsAndProcessorsAndPersists() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .build())
                .build();
        when(overallSettings.getKafkaSettings()).thenReturn(kafkaSettings);
        when(kafkaSettings.isPushDocumentEnabled(
                initialDocument.getDocument().getShop(),
                initialDocument.getDocument().getCountry().getAlpha2())
        ).thenReturn(false);
        when(documentAggregateRepository.save(initialDocument)).thenReturn(savedDocument);
        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(false);
        when(documentRepository.updateStatus(savedDocument.getDocument().getId(), DocumentStatus.TAX_PENDING))
                .thenReturn(savedDocument.getDocument().toBuilder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());

        // Execute
        DocumentAggregate result = createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(documentValidationChain).runAllValidations(initialDocument);
        verify(documentProcessorChain).runBeforeSave(initialDocument);
        verify(documentAggregateRepository).save(initialDocument);
        verify(documentProcessorChain).runAfterSave(savedDocument);
        verify(taxiRequester).pushInvoice(savedDocument);
        verify(taxiRequester, never()).pushCreditNote(anyString(), any());
        verifyNoInteractions(billProducer);
        verify(monitoringUseCase).recordCreatedDocumentsRequest(savedDocument.getDocument());
        verify(upsertDocumentApiLogUseCase).execute(eq(1L), any());
        assertThat(result).isEqualTo(savedDocument);
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocument.getDocument().getStatus());
    }

    @Test
    void testExecute_validSalesInvoice_withKafka() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .build())
                .build();
        when(overallSettings.getKafkaSettings()).thenReturn(kafkaSettings);
        when(kafkaSettings.isPushDocumentEnabled(
                initialDocument.getDocument().getShop(),
                initialDocument.getDocument().getCountry().getAlpha2())
        ).thenReturn(true);
        when(documentAggregateRepository.save(initialDocument)).thenReturn(savedDocument);
        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(false);
        when(documentRepository.updateStatus(savedDocument.getDocument().getId(), DocumentStatus.TAX_PENDING))
                .thenReturn(savedDocument.getDocument().toBuilder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());

        // Execute
        DocumentAggregate result = createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(documentValidationChain).runAllValidations(initialDocument);
        verify(documentProcessorChain).runBeforeSave(initialDocument);
        verify(documentAggregateRepository).save(initialDocument);
        verify(documentProcessorChain).runAfterSave(savedDocument);
        verify(billProducer).pushDocument(savedDocument);
        verifyNoInteractions(taxiRequester);
        verify(monitoringUseCase).recordCreatedDocumentsRequest(savedDocument.getDocument());
        verify(upsertDocumentApiLogUseCase).execute(eq(1L), any());
        assertThat(result).isEqualTo(savedDocument);
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocument.getDocument().getStatus());
    }

    @Test
    void testExecute_validSALES_CREDIT_MEMO_runsValidationsAndProcessorsAndPersists() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_MEMO)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_MEMO)
                        .build())
                .build();
        when(overallSettings.getKafkaSettings()).thenReturn(kafkaSettings);
        when(kafkaSettings.isPushDocumentEnabled(
                initialDocument.getDocument().getShop(),
                initialDocument.getDocument().getCountry().getAlpha2())
        ).thenReturn(false);
        when(documentAggregateRepository.save(initialDocument)).thenReturn(savedDocument);
        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(false);
        when(documentRepository.updateStatus(savedDocument.getDocument().getId(), DocumentStatus.TAX_PENDING))
                .thenReturn(savedDocument.getDocument().toBuilder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());

        // Execute
        DocumentAggregate result = createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(documentValidationChain).runAllValidations(initialDocument);
        verify(documentProcessorChain).runBeforeSave(initialDocument);
        verify(documentAggregateRepository).save(initialDocument);
        verify(documentProcessorChain).runAfterSave(savedDocument);
        verify(taxiRequester).pushInvoice(savedDocument);
        verify(taxiRequester, never()).pushCreditNote(anyString(), any());
        verifyNoInteractions(billProducer);
        verify(monitoringUseCase).recordCreatedDocumentsRequest(savedDocument.getDocument());
        verify(upsertDocumentApiLogUseCase).execute(eq(1L), any());
        assertThat(result).isEqualTo(savedDocument);
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocument.getDocument().getStatus());
    }

    @Test
    void testExecute_validSalesCreditNote_runsValidationsAndProcessorsAndPersists() {
        // Prepare
        DocumentId originalInvoice = DocumentId.builder()
                .sid("original-invoice-sid")
                .build();
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .originalDocument(originalInvoice)
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
                        .originalDocument(originalInvoice)
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .build())
                .build();
        when(overallSettings.getKafkaSettings()).thenReturn(kafkaSettings);
        when(kafkaSettings.isPushDocumentEnabled(
                initialDocument.getDocument().getShop(),
                initialDocument.getDocument().getCountry().getAlpha2())
        ).thenReturn(false);
        when(documentAggregateRepository.save(initialDocument)).thenReturn(savedDocument);
        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(false);
        when(documentRepository.updateStatus(savedDocument.getDocument().getId(), DocumentStatus.TAX_PENDING))
                .thenReturn(savedDocument.getDocument().toBuilder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());

        // Execute
        DocumentAggregate result = createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(documentValidationChain).runAllValidations(initialDocument);
        verify(documentProcessorChain).runBeforeSave(initialDocument);
        verify(documentAggregateRepository).save(initialDocument);
        verify(documentProcessorChain).runAfterSave(savedDocument);
        verify(taxiRequester, never()).pushInvoice(any());
        verifyNoInteractions(billProducer);
        verify(taxiRequester).pushCreditNote(originalInvoice.getSid(), savedDocument);
        verify(monitoringUseCase).recordCreatedDocumentsRequest(savedDocument.getDocument());
        verify(upsertDocumentApiLogUseCase).execute(eq(1L), any());
        assertThat(result).isEqualTo(savedDocument);
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocument.getDocument().getStatus());
    }

    @Test
    void testExecute_validDocument_runsValidationsAndProcessorsAndPersists_taxiError() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .build())
                .build();
        when(overallSettings.getKafkaSettings()).thenReturn(kafkaSettings);
        when(kafkaSettings.isPushDocumentEnabled(
                initialDocument.getDocument().getShop(),
                initialDocument.getDocument().getCountry().getAlpha2())
        ).thenReturn(false);
        when(documentAggregateRepository.save(initialDocument)).thenReturn(savedDocument);
        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(false);
        doThrow(TaxiNetworkException.buildResponseNotOk(500, "Some taxi error"))
                .when(taxiRequester)
                .pushInvoice(initialDocument);
        when(documentRepository.updateStatus(savedDocument.getDocument().getId(), DocumentStatus.TAX_PENDING))
                .thenReturn(savedDocument.getDocument().toBuilder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());

        // Execute
        DocumentAggregate result = createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(documentValidationChain).runAllValidations(initialDocument);
        verify(documentProcessorChain).runBeforeSave(initialDocument);
        verify(documentAggregateRepository).save(initialDocument);
        verify(taxiRequester).pushInvoice(savedDocument);
        verify(taxiRequester, never()).pushCreditNote(anyString(), any());
        verifyNoInteractions(billProducer);
        verify(documentProcessorChain).runAfterSave(savedDocument);
        verify(monitoringUseCase).recordCreatedDocumentsRequest(savedDocument.getDocument());
        verify(upsertDocumentApiLogUseCase).execute(eq(1L), any());
        assertThat(result).isEqualTo(savedDocument);
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocument.getDocument().getStatus());
    }

    @Test
    void testExecuteAsStatement_success() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .totalAmount(BigDecimal.valueOf(44.55))
                                .build(),
                        DocumentLine.builder()
                                .totalAmount(BigDecimal.valueOf(-33.55))
                                .build()
                ))
                .document(Document.builder()
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate salesInvoiceDocument = DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder()
                        .totalAmount(BigDecimal.valueOf(44.55))
                        .build()))
                .document(Document.builder()
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate salesCreditNoteDocument = DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder()
                        .totalAmount(BigDecimal.valueOf(33.55))
                        .build()))
                .document(Document.builder()
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedSalesInvoiceDocument = DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder()
                        .totalAmount(BigDecimal.valueOf(44.55))
                        .build()))
                .document(Document.builder()
                        .id(UUID.fromString("f5352d50-e5d8-4e5e-bab6-55d872ccd781"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedSalesCreditNoteDocument = DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder()
                        .totalAmount(BigDecimal.valueOf(33.55))
                        .build()))
                .document(Document.builder()
                        .id(UUID.fromString("f5352d50-f5d8-4e5e-bbb6-55d872ccd781"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();

        Document savedSalesCreditNoteDocumentOriginalDocumentUpdated = Document.builder()
                .id(UUID.fromString("f5352d50-f5d8-4e5e-bbb6-55d872ccd781"))
                .sid("saved dummy document")
                .shop("jumia")
                .country(CountryCode.EG)
                .type(DocumentType.SALES_CREDIT_NOTE)
                .originalDocument(DocumentId.builder()
                        .sid("f5352d50-e5d8-4e5e-bab6-55d872ccd781")
                        .build())
                .status(DocumentStatus.TAX_PENDING)
                .flow(DocumentFlow.RETAIL)
                .build();

        DocumentAggregate savedSalesCreditNoteAggregateUpdated = DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder()
                        .totalAmount(BigDecimal.valueOf(33.55))
                        .build()))
                .document(savedSalesCreditNoteDocumentOriginalDocumentUpdated)
                .build();

        List<DocumentAggregate> initialDocumentAggregates = new ArrayList<>(List.of(
                salesInvoiceDocument, salesCreditNoteDocument));
        List<DocumentAggregate> savedDocumentAggregates = new ArrayList<>(List.of(
                savedSalesInvoiceDocument, savedSalesCreditNoteAggregateUpdated));

        when(createDocumentAggregateListUseCase.execute(initialDocument)).thenReturn(initialDocumentAggregates);

        when(documentAggregateRepository.saveAll(initialDocumentAggregates)).thenReturn(savedDocumentAggregates);

        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(true);

        when(issuerTemplateRepository.findByShopAndCountry("jumia", CountryCode.EG))
                .thenReturn(Optional.of(IssuerTemplate.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .build()));

        when(documentRepository.updateStatus(any(), eq(DocumentStatus.TAX_PENDING)))
                .thenReturn(
                        savedSalesInvoiceDocument.getDocument().toBuilder()
                                .status(DocumentStatus.TAX_PENDING)
                                .build(),
                        savedSalesCreditNoteDocument.getDocument().toBuilder()
                                .status(DocumentStatus.TAX_PENDING)
                                .build()
                );

        // Execute
        List<DocumentAggregate> result = createDocumentAggregateUseCase.executeAsStatement(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(createDocumentAggregateListUseCase).execute(initialDocument);
        verify(documentAggregateRepository).saveAll(initialDocumentAggregates);
        verify(documentProcessorChain, times(2)).runAfterSave(any());
        verify(taxiRequester, times(1)).pushStatement(any());
        verifyNoInteractions(billProducer);
        verify(monitoringUseCase, times(2)).recordCreatedDocumentsRequest(any());
        verify(upsertDocumentApiLogUseCase).execute(eq(1L), any());
        verifyNoMoreInteractions(monitoringUseCase);
        verify(documentRepository, times(2)).save(any());
        verifyNoMoreInteractions(documentRepository);
        assertThat(result.get(0)).isEqualTo(savedDocumentAggregates.get(0));
        assertThat(result.get(1)).isEqualTo(savedDocumentAggregates.get(1));
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocumentAggregates.get(0).getDocument().getStatus());
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocumentAggregates.get(1).getDocument().getStatus());
    }

    @Test
    void testExecuteAsStatement_notStoreAll() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .totalAmount(BigDecimal.valueOf(44.55))
                                .build(),
                        DocumentLine.builder()
                                .totalAmount(BigDecimal.valueOf(-33.55))
                                .build()
                ))
                .document(Document.builder()
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate salesInvoiceDocument = DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder()
                        .totalAmount(BigDecimal.valueOf(44.55))
                        .build()))
                .document(Document.builder()
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate salesCreditNoteDocument = DocumentAggregate.builder()
                .lines(List.of(DocumentLine.builder()
                        .totalAmount(BigDecimal.valueOf(33.55))
                        .build()))
                .document(Document.builder()
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();

        List<DocumentAggregate> initialDocumentAggregates = new ArrayList<>(List.of(
                salesInvoiceDocument, salesCreditNoteDocument));
        List<DocumentAggregate> savedDocumentAggregates = new ArrayList<>(List.of());

        when(createDocumentAggregateListUseCase.execute(initialDocument)).thenReturn(initialDocumentAggregates);

        when(documentAggregateRepository.saveAll(initialDocumentAggregates)).thenReturn(savedDocumentAggregates);

        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(true);

        when(issuerTemplateRepository.findByShopAndCountry("jumia", CountryCode.EG))
                .thenReturn(Optional.of(IssuerTemplate.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .build()));

        // Execute
        assertThatThrownBy(() -> createDocumentAggregateUseCase.executeAsStatement(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument))
                .isInstanceOf(CreateStatementException.class)
                .hasMessage(String.format(
                        "Can't store all document for statement: %s.", initialDocument.getDocument().getSid()
                ));

        // Verify
        verify(createDocumentAggregateListUseCase).execute(initialDocument);
        verify(documentAggregateRepository).saveAll(initialDocumentAggregates);
        verify(upsertDocumentApiLogUseCase).execute(eq(1L), any());
        verify(documentProcessorChain, times(2)).runBeforeSave(any());
        verifyNoInteractions(monitoringUseCase, taxiRequester);
        verifyNoInteractions(billProducer);
    }

    @Test
    void testExecute_invalidDocument_runsValidationsAndDoesntPersists() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder().sid("dummy document").build())
                .build();
        List<ValidationError> validationErrors = List.of(new ValidationError(ErrorCode.INCORRECT_LINE_COUNT, "Test message"));
        when(documentValidationChain.runAllValidations(initialDocument)).thenReturn(validationErrors);

        // Execute
        assertThatThrownBy(() -> createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument))
                .asInstanceOf(InstanceOfAssertFactories.type(DocumentValidationException.class))
                .extracting(DocumentValidationException::getErrors)
                .isEqualTo(validationErrors);

        // Verify
        verifyNoInteractions(documentProcessorChain, documentAggregateRepository);
        verifyNoInteractions(monitoringUseCase);
    }

    @Test
    void testExecute_validSalesInvoice_runsValidationsAndProcessorsAndPersistsChangingIssuerInfoNotReplaced() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate savedDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .build())
                .build();
        when(overallSettings.getKafkaSettings()).thenReturn(kafkaSettings);
        when(kafkaSettings.isPushDocumentEnabled(
                initialDocument.getDocument().getShop(),
                initialDocument.getDocument().getCountry().getAlpha2())
        ).thenReturn(false);
        when(documentAggregateRepository.save(initialDocument)).thenReturn(savedDocument);
        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(true);
        when(issuerTemplateRepository.findByShopAndCountry("jumia", CountryCode.EG))
                .thenReturn(Optional.empty());
        when(documentRepository.updateStatus(savedDocument.getDocument().getId(), DocumentStatus.TAX_PENDING))
                .thenReturn(savedDocument.getDocument().toBuilder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());

        // Execute
        DocumentAggregate result = createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(documentValidationChain).runAllValidations(initialDocument);
        verify(documentProcessorChain).runBeforeSave(initialDocument);
        verify(documentAggregateRepository).save(initialDocument);
        verify(documentProcessorChain).runAfterSave(savedDocument);
        verify(taxiRequester).pushInvoice(savedDocument);
        verify(taxiRequester, never()).pushCreditNote(anyString(), any());
        verifyNoInteractions(billProducer);
        verify(monitoringUseCase).recordCreatedDocumentsRequest(savedDocument.getDocument());
        assertThat(result).isEqualTo(savedDocument);
        assertThat(DocumentStatus.TAX_PENDING).isEqualTo(savedDocument.getDocument().getStatus());
    }

    @Test
    void testExecute_validSalesInvoice_runsValidationsAndProcessorsAndPersistsAfterChangingIssuerInfo() {
        // Prepare
        DocumentAggregate initialDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .sid("dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .flow(DocumentFlow.RETAIL)
                        .build())
                .build();
        DocumentAggregate documentAggregateToBeSaved = initialDocument;
        documentAggregateToBeSaved.setDocument(documentAggregateToBeSaved
                .getDocument()
                .toBuilder()
                .issuer((IssuerTemplate.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .build().convertToIssuer()))
                .build()
        );
        DocumentAggregate savedDocument = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
                        .sid("saved dummy document")
                        .shop("jumia")
                        .country(CountryCode.EG)
                        .type(DocumentType.SALES_INVOICE)
                        .issuer((IssuerTemplate.builder()
                                .type(IssuerType.BUSINESS)
                                .legalName("Some issuer")
                                .email("<EMAIL>")
                                .address(Address.builder()
                                        .street("Testing street")
                                        .build())
                                .build().convertToIssuer()))
                        .build())
                .build();
        when(overallSettings.getKafkaSettings()).thenReturn(kafkaSettings);
        when(kafkaSettings.isPushDocumentEnabled(
                initialDocument.getDocument().getShop(),
                initialDocument.getDocument().getCountry().getAlpha2())
        ).thenReturn(false);
        when(overallSettings.getOverrideIssuerSettings()).thenReturn(overrideIssuerSettings);
        when(overrideIssuerSettings.isEnabled("jumia", "EG")).thenReturn(true);
        when(issuerTemplateRepository.findByShopAndCountry("jumia", CountryCode.EG))
                .thenReturn(Optional.of(IssuerTemplate.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .build()));

        when(documentAggregateRepository.save(documentAggregateToBeSaved)).thenReturn(savedDocument);
        when(documentRepository.updateStatus(savedDocument.getDocument().getId(), DocumentStatus.TAX_PENDING))
                .thenReturn(savedDocument.getDocument().toBuilder()
                        .status(DocumentStatus.TAX_PENDING)
                        .build());

        // Execute
        DocumentAggregate result = createDocumentAggregateUseCase.execute(DocumentApiLog.builder()
                .id(1L)
                .documentSid(initialDocument.getDocument().getSid())
                .originalRequest("original-document")
                .issuedDate(initialDocument.getDocument().getIssuedDate())
                .build(), initialDocument);

        // Verify
        verify(documentValidationChain).runAllValidations(initialDocument);
        verify(documentProcessorChain).runBeforeSave(initialDocument);
        verify(documentAggregateRepository).save(documentAggregateToBeSaved);
        verify(documentProcessorChain).runAfterSave(savedDocument);
        verify(taxiRequester).pushInvoice(savedDocument);
        verify(taxiRequester, never()).pushCreditNote(anyString(), any());
        verifyNoInteractions(billProducer);
        verify(monitoringUseCase).recordCreatedDocumentsRequest(savedDocument.getDocument());
        verify(overallSettings).getOverrideIssuerSettings();
        verify(overrideIssuerSettings).isEnabled("jumia", "EG");
        verify(issuerTemplateRepository).findByShopAndCountry("jumia", CountryCode.EG);
        assertThat(result).isEqualTo(savedDocument);
        assertThat(savedDocument.getDocument().getStatus()).isEqualTo(DocumentStatus.TAX_PENDING);
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

class DocumentDuplicateValidatorTest extends BaseDomainTest {

    @Mock
    private DocumentRepository documentRepository;

    private DocumentDuplicateValidator documentDuplicateValidator;

    @BeforeEach
    void setUp() {
        documentDuplicateValidator = new DocumentDuplicateValidator(documentRepository);
    }

    @Test
    void testValidate_withExistingDocument_returnsExpectedError() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder().sid("1234").build())
                .build();
        when(documentRepository.existsBySidInErrors("1234")).thenReturn(true);

        List<ValidationError> result = documentDuplicateValidator.validate(documentAggregate);

        assertThat(result).extracting(ValidationError::getCode).containsExactly(ErrorCode.DUPLICATE_DOCUMENT);
    }

    @Test
    void testValidate_withNoExistingDocument_returnsNoErrors() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder().sid("1234").build())
                .build();
        when(documentRepository.existsBySidInErrors("1234")).thenReturn(false);

        List<ValidationError> result = documentDuplicateValidator.validate(documentAggregate);

        assertThat(result).isEmpty();
    }
}

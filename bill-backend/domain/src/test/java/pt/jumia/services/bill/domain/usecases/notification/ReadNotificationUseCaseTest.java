package pt.jumia.services.bill.domain.usecases.notification;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import pt.jumia.services.bill.domain.entities.Notification;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

class ReadNotificationUseCaseTest {

    @Mock
    private SettingRepository settingRepository;

    private ReadNotificationUseCase readNotificationUseCase;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        readNotificationUseCase = new ReadNotificationUseCase(settingRepository);
    }

    @Test
    void getPurchasePortal_returnsNotificationsForMatchingProperty() {
        Setting matchingSetting = Setting.builder()
                .property("reporting.purchase_portal")
                .value("<EMAIL>")
                .createdAt(LocalDateTime.now())
                .createdBy("admin")
                .updatedAt(LocalDateTime.now())
                .updatedBy("admin2")
                .build();

        Setting otherSetting = Setting.builder()
                .property("other.property")
                .value("<EMAIL>")
                .build();

        when(settingRepository.findAll()).thenReturn(Arrays.asList(matchingSetting, otherSetting));

        List<Notification> result = readNotificationUseCase.getPurchasePortal();

        assertThat(result).hasSize(1);

        Notification notification = result.get(0);
        assertThat(notification.getEmail()).isEqualTo("<EMAIL>");
        assertThat(notification.getCreatedAt()).isEqualTo(matchingSetting.getCreatedAt());
        assertThat(notification.getCreatedBy()).isEqualTo("admin");
        assertThat(notification.getUpdatedAt()).isEqualTo(matchingSetting.getUpdatedAt());
        assertThat(notification.getUpdatedBy()).isEqualTo("admin2");
    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.repository.IssuerTemplateRepository;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CreateIssuerTemplateUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder().username("<EMAIL>").build();
    private static final IssuerTemplate ISSUER_TEMPLATE = IssuerTemplate.builder()
            .type(IssuerType.BUSINESS)
            .shop("jumia")
            .legalName("legalName")
            .name("name")
            .taxIdentificationNumber("taxIdentificationNumber")
            .businessRegistrationNumber("businessRegistrationNumber")
            .branch("branch")
            .address(Address.builder()
                    .country(CountryCode.EG)
                    .region("region")
                    .city("city")
                    .street("street")
                    .buildingNumber("buildingNumber")
                    .floor("floor")
                    .postalCode("postalCode")
                    .additionalInformation("additionalInformation")
                    .build())
            .email("email")
            .mobilePhone("mobilePhone")
            .linePhone("linePhone")
            .build();

    @Mock
    private IssuerTemplateRepository issuerTemplateRepository;
    @InjectMocks
    private CreateIssuerTemplateUseCase createIssuerTemplateUseCase;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void create() {
        when(issuerTemplateRepository.save(any())).thenReturn(ISSUER_TEMPLATE);

        IssuerTemplate insertedIssuerTemplate = createIssuerTemplateUseCase.execute(ISSUER_TEMPLATE);

        verify(issuerTemplateRepository).save(eq(ISSUER_TEMPLATE));
        assertEquals(ISSUER_TEMPLATE, insertedIssuerTemplate);
    }

}

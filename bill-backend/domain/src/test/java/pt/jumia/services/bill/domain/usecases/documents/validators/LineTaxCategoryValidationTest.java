package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class LineTaxCategoryValidationTest extends BaseDomainTest {

    private final LineTaxCategoryValidation lineTaxCategoryValidation = new LineTaxCategoryValidation();

    @Test
    public void line_tax_category_by_authority_code_is_exist() {
        List<ValidationError> result = lineTaxCategoryValidation.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .category(Category.builder()
                                        .taxAuthorityCode("authority_code")
                                        .build())
                                .build()
                ))
                .build());

        assertThat(result).isEmpty();
    }

    @Test
    public void line_tax_category_by_sid_is_exist() {
        List<ValidationError> result = lineTaxCategoryValidation.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .category(Category.builder()
                                        .sid("jumia_tax_category")
                                        .build())
                                .build()
                ))
                .build());

        assertThat(result).isEmpty();
    }

    @Test
    public void line_tax_category_is_not_exist() {
        List<ValidationError> lineTaxCategoryNotExist = lineTaxCategoryValidation.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .category(null)
                                .build()
                ))
                .build());

        assertThat(lineTaxCategoryNotExist).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.INVALID_TAX_CATEGORY);
    }

    @Test
    public void line_tax_category_is_not_have_both() {
        List<ValidationError> lineTaxCategoryNotExist = lineTaxCategoryValidation.validate(DocumentAggregate.builder()
                .lines(List.of(
                        DocumentLine.builder()
                                .category(Category.builder()
                                        .sid(null)
                                        .taxAuthorityCode(null)
                                        .build())
                                .build()
                ))
                .build());

        assertThat(lineTaxCategoryNotExist).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.INVALID_TAX_CATEGORY);
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class LineCountValidatorTest extends BaseDomainTest {

    private final LineCountValidator lineCountValidator = new LineCountValidator();

    @Test
    void testValidate_documentAggregateWithMismatchedLineCount_returnsExpectedError() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder().lineCount(3).build())  // Expecting 3 lines
                .lines(List.of(DocumentLine.builder().build()))  // But only have 1 line
                .build();

        List<ValidationError> result = lineCountValidator.validate(documentAggregate);

        assertThat(result).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.INCORRECT_LINE_COUNT);
    }

    @Test
    void testValidate_documentAggregateWithCorrectLineCount_returnsNoErrors() {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder().lineCount(1).build())  // Expecting 1 lines
                .lines(List.of(DocumentLine.builder().build()))  // And we have 1 line
                .build();

        List<ValidationError> result = lineCountValidator.validate(documentAggregate);

        assertThat(result).isEmpty();
    }
}

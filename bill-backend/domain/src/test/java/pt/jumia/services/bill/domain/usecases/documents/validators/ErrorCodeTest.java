package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

class ErrorCodeTest {

    @Test
    void ensureThereAreNoRepeatedCodes() {
        Map<Integer, Long> codeOccurrences = Arrays.stream(ErrorCode.values())
                .collect(Collectors.groupingBy(ErrorCode::getCode, Collectors.counting()));

        assertThat(codeOccurrences).allSatisfy((code, count) -> assertThat(count).isEqualTo(1));
    }
}

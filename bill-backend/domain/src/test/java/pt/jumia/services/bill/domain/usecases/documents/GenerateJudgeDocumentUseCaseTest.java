package pt.jumia.services.bill.domain.usecases.documents;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.JudgeRequester;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.entities.dtos.OverrideFields;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class GenerateJudgeDocumentUseCaseTest extends BaseDomainTest {

    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid(null)
            .country(CountryCode.NG)
            .shop("jumia")
            .issuer(Issuer.builder().name("old-name-un-replaced-issuer").build())
            .receiver(Receiver.builder().name("old-name-un-replaced-receiver").build())
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("old-tax-document-number-un-updated")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .appliedTaxes(List.of(
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.VAT_GENERAL)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build(),
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.OTHER_AMOUNT)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build()
                    ))
                    .category(Category.builder()
                            .sid("sid-1")
                            .build())
                    .build(), DocumentLine.builder()
                    .appliedTaxes(List.of(
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.EXEMPT)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build(),
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.ZERO_RATE)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build()
                    ))
                    .category(Category.builder()
                            .sid("sid-2")
                            .build())
                    .build()))
            .build();


    @Mock
    private JudgeRequester judgeRequester;

    @Mock
    private DocumentRepository documentRepository;

    private GenerateJudgeDocumentUseCase generateJudgeDocumentUseCase;

    @BeforeEach
    public void setUp() {
        generateJudgeDocumentUseCase = new GenerateJudgeDocumentUseCase(
                judgeRequester,
                documentRepository
        );
    }

    @Test
    void executeWhenTaxSuccess() throws JsonProcessingException {

        when(judgeRequester.generateNewDocumentByCode(DOCUMENT_AGGREGATE)).thenReturn("fake_judge_sid");

        generateJudgeDocumentUseCase.execute(DOCUMENT_AGGREGATE);

        verify(documentRepository).save(DOCUMENT.toBuilder()
                .judgeSid("fake_judge_sid").build());
    }

    @Test
    void executeWhenTaxSuccessWithOverrides() {

        when(judgeRequester.generateNewDocumentByCode(any(DocumentAggregate.class)))
                .thenReturn("fake_judge_sid");

        generateJudgeDocumentUseCase.execute(DOCUMENT_AGGREGATE, OverrideFields.builder()
                .buyerLegalName("updatedBuyerLegalName")
                .invoiceNumberInsideTaxAuthorities("updatedTaxAuthoritiesNumber")
                .taxCategoriesMap(Map.of(
                        TaxCategory.VAT_GENERAL.name(), "A: Standard",
                        TaxCategory.ZERO_RATE.name(), "B: Zero",
                        TaxCategory.EXEMPT.name(), "C: Exempt"
                ))
                .build());

        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
        verify(documentRepository).save(documentCaptor.capture());

        Document savedDocument = documentCaptor.getValue();
        assertEquals("fake_judge_sid", savedDocument.getJudgeSid());
    }

}

package pt.jumia.services.bill.domain.usecases.documents.processors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.List;
import java.util.UUID;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class MoveErrorDocumentsToRetriedProcessorTest extends BaseDomainTest {

    @Mock
    private DocumentRepository documentRepository;

    private MoveErrorDocumentsToRetriedProcessor moveErrorDocumentsToRetriedProcessor;

    @BeforeEach
    void setUp() {
        moveErrorDocumentsToRetriedProcessor = new MoveErrorDocumentsToRetriedProcessor(documentRepository);
    }

    @Test
    void testProcess_withSomeDocumentsInError_movesAllErrorDocumentsToRetried() {

        UUID uuid = UUID.randomUUID();
        UUID differentUuid = UUID.randomUUID();


        // Prepare
        DocumentAggregate aggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .id(uuid)
                        .sid("1234")
                        .build())
                .build();
        when(documentRepository.findAll(
                DocumentFilter.builder()
                        .sid("1234")
                        .status(DocumentStatus.TAX_ERROR_ACKED)
                        .include(List.of(Document.Details.values()))
                        .build()))
                .thenReturn(List.of(
                        Document.builder()
                                .sid("1234")
                                .id(differentUuid)
                                .status(DocumentStatus.TAX_ERROR_ACKED)
                                .build(),
                        Document.builder()
                                .sid("1234")
                                .id(differentUuid)
                                .status(DocumentStatus.TAX_ERROR_ACKED)
                                .build()
                ));

        // Execute
        moveErrorDocumentsToRetriedProcessor.process(aggregate);

        // Verify
        verify(documentRepository, times(2)).save(Document.builder()
                .sid("1234")
                .id(differentUuid)
                .status(DocumentStatus.TAX_ERROR_RETRIED)
                .build());
    }

}

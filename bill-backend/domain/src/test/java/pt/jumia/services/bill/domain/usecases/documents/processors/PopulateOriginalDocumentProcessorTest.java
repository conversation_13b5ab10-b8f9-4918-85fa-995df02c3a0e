package pt.jumia.services.bill.domain.usecases.documents.processors;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class PopulateOriginalDocumentProcessorTest extends BaseDomainTest {

    @Mock
    private DocumentRepository documentRepository;

    private PopulateOriginalDocumentProcessor populateOriginalDocumentProcessor;

    @BeforeEach
    void setUp() {
        populateOriginalDocumentProcessor = new PopulateOriginalDocumentProcessor(documentRepository);
    }

    @Test
    void testProcess_withNoOriginalDocument_doesNothing() {
        // Execute
        populateOriginalDocumentProcessor.process(DocumentAggregate.builder()
                .document(Document.builder().build())
                .build());

        // Verify
        verifyNoInteractions(documentRepository);
    }

    @Test
    void testProcess_withOriginalDocumentAlreadyWithId_doesNothing() {
        // Execute
        populateOriginalDocumentProcessor.process(DocumentAggregate.builder()
                .document(Document.builder()
                        .originalDocument(DocumentId.builder()
                                .id(UUID.randomUUID())
                                .build()).build())
                .build());

        // Verify
        verifyNoInteractions(documentRepository);
    }

    @Test
    void testProcess_withNonExistingOriginalDocumentSid_throwsNotFoundException() {
        when(documentRepository.findDocumentIdBySidInSuccessStatus(any())).thenReturn(Optional.empty());

        // Execute
        Assertions.assertThatThrownBy(() ->
                populateOriginalDocumentProcessor.process(DocumentAggregate.builder()
                        .document(Document.builder()
                                .originalDocument(DocumentId.builder()
                                        .sid("non-existing")
                                        .build()).build())
                        .build())
        )
                .isInstanceOf(EntityNotFoundException.class)
                .hasMessageContaining("Document")
                .hasMessageContaining("sid")
                .hasMessageContaining("non-existing");

        // Verify
        verify(documentRepository).findDocumentIdBySidInSuccessStatus("non-existing");
    }

    @Test
    void testProcess_withExistingOriginalDocumentSid_populatesId() {
        DocumentId expectedOriginalDocument = DocumentId.builder()
                .id(UUID.randomUUID())
                .sid("existing")
                .build();
        when(documentRepository.findDocumentIdBySidInSuccessStatus(any())).thenReturn(Optional.of(expectedOriginalDocument));
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .originalDocument(DocumentId.builder()
                                .sid("existing")
                                .build()).build())
                .build();

        // Execute
        populateOriginalDocumentProcessor.process(documentAggregate);

        // Verify
        assertThat(documentAggregate.getDocument().getOriginalDocument()).isEqualTo(expectedOriginalDocument);
        verify(documentRepository).findDocumentIdBySidInSuccessStatus("existing");
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.Test;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

public class DiscountEqualDiscountLinesValidatorTest extends BaseDomainTest {

    private final DiscountEqualDiscountLinesValidator
            discountEqualDiscountLinesValidator = new DiscountEqualDiscountLinesValidator();

    @Test
    public void validate_discount_not_equal_discount_lines() {
        List<ValidationError> discountNotEqual = discountEqualDiscountLinesValidator.validate(DocumentAggregate.builder()
                .document(Document.builder()
                        .discountAmount(BigDecimal.valueOf(5000))
                        .build())
                .lines(List.of(
                        DocumentLine.builder()
                                .discount(DocumentLine.Discount.builder()
                                        .amount(BigDecimal.valueOf(2000))
                                        .build())
                                .build(),
                        DocumentLine.builder()
                                .discount(DocumentLine.Discount.builder()
                                        .amount(BigDecimal.valueOf(2000))
                                        .build())
                                .build()
                ))
                .build());

        assertThat(discountNotEqual).extracting(ValidationError::getCode)
                .containsExactly(ErrorCode.DISCOUNT_NOT_EQUAL_DISCOUNT_LINES);
    }

    @Test
    public void validate_discount_is_equal_discount_lines() {
        List<ValidationError> result = discountEqualDiscountLinesValidator.validate(DocumentAggregate.builder()
                .document(Document.builder()
                        .discountAmount(BigDecimal.valueOf(5000))
                        .build())
                .lines(List.of(
                        DocumentLine.builder()
                                .discount(DocumentLine.Discount.builder()
                                        .amount(BigDecimal.valueOf(2000))
                                        .build())
                                .build(),
                        DocumentLine.builder()
                                .discount(DocumentLine.Discount.builder()
                                        .amount(BigDecimal.valueOf(2000))
                                        .build())
                                .build(),
                        DocumentLine.builder()
                                .discount(DocumentLine.Discount.builder()
                                        .amount(BigDecimal.valueOf(1000))
                                        .build())
                                .build()
                ))
                .build());

        assertThat(result).isEmpty();
    }
}

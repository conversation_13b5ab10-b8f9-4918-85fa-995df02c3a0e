package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.dtos.TaxAuthoritiesDetailsUpdateDto;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.exceptions.document.InvalidDocumentTransitionException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.settings.JudgeSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.metrics.MonitoringUseCase;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Currency;
import java.util.Optional;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

class UpdateTaxAuthoritiesDetailsUseCaseTest extends BaseDomainTest {

    @Mock
    private GenerateJudgeDocumentUseCase generateJudgeDocumentUseCase;
    @Mock
    private DocumentAggregateRepository documentAggregateRepository;
    @Mock
    private SendEmailDocumentToReceiverUseCase sendEmailDocumentToReceiverUseCase;
    @Mock
    private MonitoringUseCase monitoringUseCase;

    @Mock
    private JudgeSettings judgeSettings;

    @Mock
    private OverallSettings overallSettings;

    private UpdateTaxAuthoritiesDetailsUseCase updateTaxAuthoritiesDetailsUseCase;

    @BeforeEach
    public void setUp() {
        updateTaxAuthoritiesDetailsUseCase = new UpdateTaxAuthoritiesDetailsUseCase(
                generateJudgeDocumentUseCase,
                documentAggregateRepository,
                sendEmailDocumentToReceiverUseCase,
                monitoringUseCase,
                overallSettings);
    }

    @Test
    public void updateNonExistingDocument() {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_SUCCESS);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SUBMITTED_INVALID);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(Optional.empty());

        // Execute
        assertThatThrownBy(() -> updateTaxAuthoritiesDetailsUseCase.execute(updateRequest))
                .isInstanceOf(EntityNotFoundException.class)
                .hasMessageContaining(originalDocument.getId().toString());

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verifyNoMoreInteractions(documentAggregateRepository);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoInteractions(monitoringUseCase);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateInvalidDocumentStatusTransitionSuccessToError() {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_SUCCESS);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SUBMITTED_INVALID);
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .document(originalDocument)
                .build();
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));

        // Execute
        assertThatThrownBy(() -> updateTaxAuthoritiesDetailsUseCase.execute(updateRequest))
                .isInstanceOf(InvalidDocumentTransitionException.class)
                .hasMessageContaining(originalDocument.getId().toString())
                .hasMessageContaining(originalDocument.getSid());

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verifyNoMoreInteractions(documentAggregateRepository);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoInteractions(monitoringUseCase);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateInvalidDocumentStatusTransitionSkippedToSuccess() {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_SKIPPED);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SUCCESS);
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .document(originalDocument)
                .build();
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));

        // Execute
        assertThatThrownBy(() -> updateTaxAuthoritiesDetailsUseCase.execute(updateRequest))
                .isInstanceOf(InvalidDocumentTransitionException.class)
                .hasMessageContaining(originalDocument.getId().toString())
                .hasMessageContaining(originalDocument.getSid());

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verifyNoMoreInteractions(documentAggregateRepository);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoInteractions(monitoringUseCase);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateNewTaxAuthoritiesDetailsWithOpenErrorStatus() throws Exception {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_PENDING);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SUBMITTED_INVALID);
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(null)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAX_SUBMITTED_INVALID)
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(updateRequest.getTaxAuthoritiesDetails().toBuilder()
                        .document(expectedUpdatedDocument)
                        .build())
                .document(expectedUpdatedDocument)
                .build();
        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateTAXErrorTaxAuthoritiesDetailsWithOpenErrorStatus() throws Exception {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_ERROR);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SUBMITTED_INVALID);
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(null)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAX_SUBMITTED_INVALID)
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(updateRequest.getTaxAuthoritiesDetails().toBuilder()
                        .document(expectedUpdatedDocument)
                        .build())
                .document(expectedUpdatedDocument)
                .build();
        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateExistingTaxAuthoritiesDetailsWithFinalErrorStatus() throws Exception {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_SUBMITTED_INVALID);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_ERROR_ACKED);
        TaxAuthoritiesDetails originalTaxAuthoritiesDetails = updateRequest.getTaxAuthoritiesDetails().toBuilder()
                .submissionId("originalSubmissionId")
                .document(originalDocument)
                .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                .createdBy("test user")
                .generateId()
                .build();
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(originalTaxAuthoritiesDetails)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAX_ERROR_ACKED)
                .build();
        TaxAuthoritiesDetails expectedUpdatedTaxAuthoritiesDetails = originalTaxAuthoritiesDetails.toBuilder()
                .submissionId(updateRequest.getTaxAuthoritiesDetails().getSubmissionId())
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(expectedUpdatedTaxAuthoritiesDetails)
                .document(expectedUpdatedDocument)
                .build();
        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verify(monitoringUseCase).recordTaxUpdatesRequest(expectedUpdatedDocument);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateNewTaxAuthoritiesDetailsWithFinalSuccessStatus() throws Exception{
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_PENDING);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SUCCESS);
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(null)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAX_SUCCESS)
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(updateRequest.getTaxAuthoritiesDetails().toBuilder()
                        .document(expectedUpdatedDocument)
                        .build())
                .document(expectedUpdatedDocument)
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregateWithDocument = expectedUpdatedDocumentAggregate.toBuilder()
                .document(expectedUpdatedDocument.toBuilder()
                        .judgeSid(UUID.randomUUID().toString())
                        .build())
                .build();
        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));
        when(documentAggregateRepository.save(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregate);
        when(generateJudgeDocumentUseCase.execute(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregateWithDocument);

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verify(generateJudgeDocumentUseCase).execute(expectedUpdatedDocumentAggregate);
        verify(monitoringUseCase).recordTaxUpdatesRequest(expectedUpdatedDocument);
        verify(sendEmailDocumentToReceiverUseCase).execute(expectedUpdatedDocumentAggregateWithDocument);
    }

    @Test
    public void updateExistingTaxAuthoritiesDetailsWithFinalSuccessStatus() throws Exception {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_PENDING);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SKIPPED);
        TaxAuthoritiesDetails originalTaxAuthoritiesDetails = updateRequest.getTaxAuthoritiesDetails().toBuilder()
                .submissionId("originalSubmissionId")
                .document(originalDocument)
                .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                .createdBy("test user")
                .generateId()
                .build();
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(originalTaxAuthoritiesDetails)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAX_SKIPPED)
                .build();
        TaxAuthoritiesDetails expectedUpdatedTaxAuthoritiesDetails = originalTaxAuthoritiesDetails.toBuilder()
                .submissionId(updateRequest.getTaxAuthoritiesDetails().getSubmissionId())
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(expectedUpdatedTaxAuthoritiesDetails)
                .document(expectedUpdatedDocument)
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregateWithDocument = expectedUpdatedDocumentAggregate.toBuilder()
                .document(expectedUpdatedDocument.toBuilder()
                        .judgeSid(UUID.randomUUID().toString())
                        .build())
                .build();

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));
        when(documentAggregateRepository.save(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregate);
        when(generateJudgeDocumentUseCase.execute(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregateWithDocument);

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verify(generateJudgeDocumentUseCase).execute(expectedUpdatedDocumentAggregate);
        verify(monitoringUseCase).recordTaxUpdatesRequest(expectedUpdatedDocument);
        verify(sendEmailDocumentToReceiverUseCase).execute(expectedUpdatedDocumentAggregateWithDocument);
    }

    @Test
    public void updateExistingTaxAuthoritiesDetailsWithFinalSuccessStatusWithoutGeneratingPdf() throws Exception {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_PENDING);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_SKIPPED);
        TaxAuthoritiesDetails originalTaxAuthoritiesDetails = updateRequest.getTaxAuthoritiesDetails().toBuilder()
                .submissionId("originalSubmissionId")
                .document(originalDocument)
                .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                .createdBy("test user")
                .generateId()
                .build();
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(originalTaxAuthoritiesDetails)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAX_SKIPPED)
                .build();
        TaxAuthoritiesDetails expectedUpdatedTaxAuthoritiesDetails = originalTaxAuthoritiesDetails.toBuilder()
                .submissionId(updateRequest.getTaxAuthoritiesDetails().getSubmissionId())
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(expectedUpdatedTaxAuthoritiesDetails)
                .document(expectedUpdatedDocument)
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregateWithDocument = expectedUpdatedDocumentAggregate.toBuilder()
                .document(expectedUpdatedDocument.toBuilder()
                        .judgeSid(UUID.randomUUID().toString())
                        .build())
                .build();
        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);

        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));
        when(documentAggregateRepository.save(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregate);

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verify(monitoringUseCase).recordTaxUpdatesRequest(expectedUpdatedDocument);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateExistingTaxAuthoritiesDetailsFromSuccessToRejectionStatus() throws Exception {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_SUCCESS);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAX_REJECTED);
        TaxAuthoritiesDetails originalTaxAuthoritiesDetails = updateRequest.getTaxAuthoritiesDetails().toBuilder()
                .submissionId("originalSubmissionId")
                .document(originalDocument)
                .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                .createdBy("test user")
                .generateId()
                .build();
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(originalTaxAuthoritiesDetails)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAX_REJECTED)
                .build();
        TaxAuthoritiesDetails expectedUpdatedTaxAuthoritiesDetails = originalTaxAuthoritiesDetails.toBuilder()
                .submissionId(updateRequest.getTaxAuthoritiesDetails().getSubmissionId())
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(expectedUpdatedTaxAuthoritiesDetails)
                .document(expectedUpdatedDocument)
                .build();

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));
        when(documentAggregateRepository.save(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregate);
        when(generateJudgeDocumentUseCase.execute(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregate);

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verify(monitoringUseCase).recordTaxUpdatesRequest(expectedUpdatedDocument);
        verify(generateJudgeDocumentUseCase).execute(expectedUpdatedDocumentAggregate);
        verify(sendEmailDocumentToReceiverUseCase).execute(expectedUpdatedDocumentAggregate);
    }

    @Test
    public void updateWithNoStatusChange() throws Exception{
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_SUCCESS);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(
                originalDocument,
                DocumentStatus.TAX_SUCCESS);
        TaxAuthoritiesDetails originalTaxAuthoritiesDetails = updateRequest.getTaxAuthoritiesDetails().toBuilder()
                .submissionId("originalSubmissionId")
                .document(originalDocument)
                .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                .createdBy("test user")
                .generateId()
                .build();
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(originalTaxAuthoritiesDetails)
                .document(originalDocument)
                .build();
        TaxAuthoritiesDetails expectedUpdatedTaxAuthoritiesDetails = originalTaxAuthoritiesDetails.toBuilder()
                .submissionId(updateRequest.getTaxAuthoritiesDetails().getSubmissionId())
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(expectedUpdatedTaxAuthoritiesDetails)
                .document(originalDocument)
                .build();
        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));
        when(documentAggregateRepository.save(expectedUpdatedDocumentAggregate)).thenReturn(expectedUpdatedDocumentAggregate);

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verify(monitoringUseCase).recordTaxUpdatesRequest(originalDocument);
        verifyNoInteractions(generateJudgeDocumentUseCase, sendEmailDocumentToReceiverUseCase);
    }

    @Test
    public void updateDocumentStatusFromTAX_FAILED_REQUESTtoTAXI_FAILED_FETCH_ITEM() throws Exception {
        // Prepare
        Document originalDocument = generateDocument(DocumentStatus.TAX_FAILED_REQUEST);
        TaxAuthoritiesDetailsUpdateDto updateRequest = generateTaxAuthoritiesUpdateDto(originalDocument, DocumentStatus.TAXI_FAILED_FETCH_ITEM);
        DocumentAggregate originalDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(null)
                .document(originalDocument)
                .build();
        Document expectedUpdatedDocument = originalDocument.toBuilder()
                .status(DocumentStatus.TAXI_FAILED_FETCH_ITEM)
                .build();
        DocumentAggregate expectedUpdatedDocumentAggregate = DocumentAggregate.builder()
                .taxAuthoritiesDetails(updateRequest.getTaxAuthoritiesDetails().toBuilder()
                        .document(expectedUpdatedDocument)
                        .build())
                .document(expectedUpdatedDocument)
                .build();
        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.isAutomaticPdfGenerationEnabled(anyString(), anyString())).thenReturn(true);
        when(documentAggregateRepository.findByDocumentId(originalDocument.getId())).thenReturn(
                Optional.of(originalDocumentAggregate));

        // Execute
        updateTaxAuthoritiesDetailsUseCase.execute(updateRequest);

        // Verify
        verify(documentAggregateRepository).findByDocumentId(originalDocument.getId());
        verify(documentAggregateRepository).save(expectedUpdatedDocumentAggregate);
        verifyNoInteractions(generateJudgeDocumentUseCase);
        verifyNoInteractions(sendEmailDocumentToReceiverUseCase);
    }

    private Document generateDocument(DocumentStatus status) {
        return Document.builder()
                .status(status)
                .judgeSid(null)
                .country(CountryCode.NG)
                .shop("jumia")
                .type(DocumentType.SALES_INVOICE)
                .sid("abdsabdasbsasfasf")
                .flow(DocumentFlow.RETAIL)
                .generatedBy("NAV")
                .issuedDate(LocalDateTime.now())
                .currency(Currency.getInstance("UGX"))
                .receiver(Receiver.builder()
                        .type(ReceiverType.CUSTOMER)
                        .legalName("Some receiver")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .issuer(Issuer.builder()
                        .type(IssuerType.BUSINESS)
                        .legalName("Some issuer")
                        .email("<EMAIL>")
                        .address(Address.builder()
                                .street("Testing street")
                                .build())
                        .generateId()
                        .build())
                .lineCount(1)
                .totalAmount(new BigDecimal("120.00"))
                .netAmount(new BigDecimal("100.00"))
                .taxAmount(new BigDecimal("20.00"))
                .discountAmount(new BigDecimal("0.00"))
                .originalDocument(null)
                .notes(null)
                .generateId()
                .build();
    }

    private TaxAuthoritiesDetailsUpdateDto generateTaxAuthoritiesUpdateDto(Document document, DocumentStatus documentStatus) {
        return TaxAuthoritiesDetailsUpdateDto.builder()
                .documentId(document.getId())
                .documentStatus(documentStatus)
                .taxAuthoritiesDetails(TaxAuthoritiesDetails.builder()
                        .statusCode("400")
                        .errorCode("50")
                        .exception("ExceptionErrorStringMessage")
                        .deviceNumber("*********")
                        .verificationCode("dummy")
                        .qrCode("very_dummy")
                        .taxDocumentNumber("even_more_dummy")
                        .submissionId("cant_be_more_dummy")
                        .generateId()
                        .build())
                .build();
    }
}

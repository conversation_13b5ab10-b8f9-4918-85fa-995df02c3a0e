package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.SettingNotActiveException;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.settings.RejectionSettings;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class DeclineDocumentRejectionUseCaseTest extends BaseDomainTest {
    private static final String DOCUMENT_SID = "document-sid";
    private static final UUID DOCUMENT_UUID = UUID.randomUUID();

    @Mock
    private DocumentRepository documentRepository;
    @Mock
    private TaxiRequester taxiRequester;
    @Mock
    private OverallSettings overallSettings;
    @Mock
    private RejectionSettings rejectionSettings;

    @InjectMocks
    private DeclineDocumentRejectionUseCase declineDocumentRejectionUseCase;

    @Test
    public void declineDocument_AlreadyDeclinedAndInSuccessStatus() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .sid(DOCUMENT_SID)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        Document declinedDocument = declineDocumentRejectionUseCase.execute(document);

        assertEquals(document, declinedDocument);

        verifyNoInteractions(documentRepository);
        verifyNoInteractions(taxiRequester);
        verifyNoInteractions(overallSettings);
    }

    @Test
    public void declineDocument_InvalidStatus() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .sid(DOCUMENT_SID)
                .status(DocumentStatus.TAX_SUBMITTED_INVALID)
                .build();

        assertThrows(ConflictOperationException.class, () ->
                declineDocumentRejectionUseCase.execute(document));

        verifyNoInteractions(documentRepository);
        verifyNoInteractions(taxiRequester);
        verifyNoInteractions(overallSettings);
    }

    @Test
    public void declineDocument_settingsDisabled() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .sid(DOCUMENT_SID)
                .shop("jumia_test")
                .country(CountryCode.EG)
                .type(DocumentType.SALES_CREDIT_MEMO)
                .status(DocumentStatus.TAX_REJECTED)
                .build();

        when(overallSettings.getRejectionSettings()).thenReturn(rejectionSettings);
        when(rejectionSettings.getCreditMemoRejectionDeclineEnabled(
                document.getShop(),
                document.getCountry().getAlpha2()))
                .thenReturn(false);
        assertThrows(SettingNotActiveException.class, () ->
                declineDocumentRejectionUseCase.execute(document));

        verify(overallSettings).getRejectionSettings();
        verify(rejectionSettings).getCreditMemoRejectionDeclineEnabled(
                "jumia_test",
                "EG"
        );
        verifyNoInteractions(documentRepository);
        verifyNoInteractions(taxiRequester);
    }

    @Test
    public void declineDocument_taxiNetworkError() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .sid(DOCUMENT_SID)
                .shop("jumia_test")
                .country(CountryCode.EG)
                .type(DocumentType.SALES_CREDIT_NOTE)
                .status(DocumentStatus.TAX_REJECTED)
                .build();

        when(overallSettings.getRejectionSettings()).thenReturn(rejectionSettings);
        when(rejectionSettings.getCreditNoteRejectionDeclineEnabled(
                document.getShop(),
                document.getCountry().getAlpha2()))
                .thenReturn(true);
        doThrow(TaxiNetworkException
                .buildResponseNotOk(200, "error"))
                .when(taxiRequester)
                .declineDocumentRejection(DOCUMENT_UUID.toString());

        assertThrows(TaxiNetworkException.class, () ->
                declineDocumentRejectionUseCase.execute(document));

        verify(overallSettings).getRejectionSettings();
        verify(rejectionSettings).getCreditNoteRejectionDeclineEnabled(
                "jumia_test",
                "EG"
        );
        verify(taxiRequester).declineDocumentRejection(DOCUMENT_UUID.toString());
        verifyNoInteractions(documentRepository);
    }

    @Test
    public void declineDocument_success() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .sid(DOCUMENT_SID)
                .shop("jumia_test")
                .country(CountryCode.EG)
                .type(DocumentType.SALES_CREDIT_NOTE)
                .status(DocumentStatus.TAX_REJECTED)
                .build();

        when(overallSettings.getRejectionSettings()).thenReturn(rejectionSettings);
        when(rejectionSettings.getCreditNoteRejectionDeclineEnabled(
                document.getShop(),
                document.getCountry().getAlpha2()))
                .thenReturn(true);

        Document savedDeclinedDocument = document
                .toBuilder()
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        when(documentRepository.save(savedDeclinedDocument)).thenReturn(savedDeclinedDocument);


        Document declinedDocument = declineDocumentRejectionUseCase.execute(document);
        assertEquals(savedDeclinedDocument, declinedDocument);

        verify(overallSettings).getRejectionSettings();
        verify(rejectionSettings).getCreditNoteRejectionDeclineEnabled(
                "jumia_test",
                "EG"
        );
        verify(taxiRequester).declineDocumentRejection(DOCUMENT_UUID.toString());
        verify(documentRepository).save(declinedDocument);
    }

    @Test
    public void declineDocumentForDebitNote_success() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .sid(DOCUMENT_SID)
                .shop("jumia_test")
                .country(CountryCode.EG)
                .type(DocumentType.SALES_DEBIT_NOTE)
                .status(DocumentStatus.TAX_REJECTED)
                .build();

        when(overallSettings.getRejectionSettings()).thenReturn(rejectionSettings);
        when(rejectionSettings.getDebitNoteRejectionDeclineEnabled(
                document.getShop(),
                document.getCountry().getAlpha2()))
                .thenReturn(true);

        Document savedDeclinedDocument = document
                .toBuilder()
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        when(documentRepository.save(savedDeclinedDocument)).thenReturn(savedDeclinedDocument);


        Document declinedDocument = declineDocumentRejectionUseCase.execute(document);
        assertEquals(savedDeclinedDocument, declinedDocument);

        verify(overallSettings).getRejectionSettings();
        verify(rejectionSettings).getDebitNoteRejectionDeclineEnabled(
                "jumia_test",
                "EG"
        );
        verify(taxiRequester).declineDocumentRejection(DOCUMENT_UUID.toString());
        verify(documentRepository).save(declinedDocument);
    }
}

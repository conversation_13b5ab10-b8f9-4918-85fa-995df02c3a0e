package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

public class AckDocumentUseCaseTest extends BaseDomainTest {

    private static final UUID DOCUMENT_UUID = UUID.randomUUID();

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private TaxiRequester taxiRequester;

    private AckDocumentUseCase ackDocumentUseCase;

    @BeforeEach
    void setUp() {
        ackDocumentUseCase = new AckDocumentUseCase(
                documentRepository,
                taxiRequester
        );
    }

    @Test
    void executeByDocumentIdUUIDTest() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .country(CountryCode.EG)
                .status(DocumentStatus.TAX_SUBMITTED_INVALID)
                .build();

        ackDocumentUseCase.execute(document);

        verify(taxiRequester).ackError(DOCUMENT_UUID.toString());
    }

    @Test
    void executeByDocumentIdUUIDStatusNotTAX_ERRORTest() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .country(CountryCode.EG)
                .status(DocumentStatus.TAX_SUCCESS)
                .build();

        assertThatThrownBy(() -> ackDocumentUseCase.execute(document))
                .isInstanceOf(ConflictOperationException.class);

        verifyNoInteractions(taxiRequester);
        verify(documentRepository, never()).save(any());
    }

    @Test
    public void execute_skipAlreadyAcknowledged() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .country(CountryCode.EG)
                .status(DocumentStatus.TAX_ERROR_ACKED)
                .build();

        ackDocumentUseCase.execute(document);

        verifyNoInteractions(taxiRequester);
        verify(documentRepository, never()).save(any());
    }
}

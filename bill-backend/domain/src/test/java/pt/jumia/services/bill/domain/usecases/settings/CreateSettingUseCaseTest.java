package pt.jumia.services.bill.domain.usecases.settings;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.DataEventsNotificator;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CreateSettingUseCaseTest {

    private static final RequestUser REQUEST_USER = RequestUser.builder().username("<EMAIL>").build();
    private static final Setting SETTING = Setting.builder()
            .property("some.property")
            .type(Setting.Type.OVERRIDE)
            .value("some-value")
            .overrideKey("some-override-key")
            .description("some-description")
            .build();

    @Mock
    private SettingRepository settingRepository;
    @Mock
    private DataEventsNotificator dataEventsNotificator;
    @InjectMocks
    private CreateSettingUseCase createSettingUseCase;
    @Captor
    private ArgumentCaptor<Setting> settingCaptor;

    @BeforeEach
    public void setUp() {
        RequestContext.setUser(REQUEST_USER);
    }

    @Test
    public void create() {
        when(settingRepository.findByProperty(SETTING.getProperty())).thenReturn(List.of(Setting.builder().build()));
        when(settingRepository.insert(any())).thenReturn(SETTING);
        LocalDateTime timeBeforeInsert = LocalDateTime.now(ZoneOffset.UTC);

        Setting insertedSetting = createSettingUseCase.execute(SETTING);

        verify(settingRepository).findByProperty(SETTING.getProperty());
        verify(settingRepository).insert(settingCaptor.capture());
        Setting setting = settingCaptor.getValue();
        verify(dataEventsNotificator).notifySettingChanges();
        assertThat(setting.getCreatedAt()).isAfter(timeBeforeInsert);
        assertThat(setting.getCreatedBy()).isEqualTo(REQUEST_USER.getUsername());
        assertThat(setting.getUpdatedAt()).isAfter(timeBeforeInsert);
        assertThat(setting.getUpdatedBy()).isEqualTo(REQUEST_USER.getUsername());
        assertThat(insertedSetting).isEqualTo(SETTING);
    }

    @Test
    public void create_nullSetting() {
        assertThatThrownBy(() -> createSettingUseCase.execute(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Setting cannot be null.");

        verifyNoInteractions(settingRepository);
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void create_defaultSetting() {
        Setting defaultSetting = SETTING.toBuilder().type(Setting.Type.DEFAULT).build();

        assertThatThrownBy(() -> createSettingUseCase.execute(defaultSetting))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("You can only create override settings.");

        verifyNoInteractions(settingRepository);
        verifyNoInteractions(dataEventsNotificator);
    }

    @Test
    public void create_settingWithoutDefaultParent() {
        when(settingRepository.findByProperty(SETTING.getProperty()))
                .thenReturn(List.of());

        assertThatThrownBy(() -> createSettingUseCase.execute(SETTING))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("You must have a default setting to make an override.");

        verify(settingRepository).findByProperty(SETTING.getProperty());
        verify(settingRepository, never()).insert(any());
        verifyNoInteractions(dataEventsNotificator);
    }
}

package pt.jumia.services.bill.domain.usecases.documentapilogs;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.repository.DocumentApiLogRepository;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UpsertDocumentApiLogUseCaseTest {
    @Mock
    private DocumentApiLogRepository documentApiLogRepository;

    @InjectMocks
    private UpsertDocumentApiLogUseCase upsertDocumentApiLogUseCase;

    @Test
    public void executeCreateNewDocumentApiLog() {
        DocumentApiLog documentApiLogToBeSaved = DocumentApiLog.builder()
                .originalRequest("original-document")
                .documentSid("document-sid")
                .build();
        when(documentApiLogRepository.insert(any(DocumentApiLog.class)))
                .thenReturn(documentApiLogToBeSaved);

        DocumentApiLog documentApiLog = upsertDocumentApiLogUseCase.execute(documentApiLogToBeSaved);
        assertThat(documentApiLog.withoutDbFields())
                .usingRecursiveComparison()
                .isEqualTo(documentApiLogToBeSaved.withoutDbFields());

    }

    @Test
    public void executeUpsertNewDocumentApiLog() {
        DocumentApiLog documentApiLogToBeSaved = DocumentApiLog.builder()
                .originalRequest("original-document")
                .documentSid("document-sid")
                .build();
        when(documentApiLogRepository.findById(1L)).thenReturn(Optional.empty());
        when(documentApiLogRepository.insert(any(DocumentApiLog.class)))
                .thenReturn(documentApiLogToBeSaved);

        DocumentApiLog documentApiLog = upsertDocumentApiLogUseCase.execute(1L, documentApiLogToBeSaved);
        assertThat(documentApiLog.withoutDbFields())
                .usingRecursiveComparison()
                .isEqualTo(documentApiLogToBeSaved.withoutDbFields());

    }


    @Test
    public void executeUpdateDocumentApiLog() {
        DocumentApiLog documentApiLogToBeSaved = DocumentApiLog.builder()
                .originalRequest("original-document")
                .documentSid("document-sid")
                .build();
        when(documentApiLogRepository.findById(1L))
                .thenReturn(Optional.of(documentApiLogToBeSaved
                        .toBuilder()
                        .id(1L)
                        .build()));
        when(documentApiLogRepository.update(eq(1L), any(DocumentApiLog.class)))
                .thenReturn(documentApiLogToBeSaved);

        DocumentApiLog documentApiLog = upsertDocumentApiLogUseCase.execute(1L, documentApiLogToBeSaved);
        assertThat(documentApiLog.withoutDbFields())
                .usingRecursiveComparison()
                .isEqualTo(documentApiLogToBeSaved.withoutDbFields());

    }
}

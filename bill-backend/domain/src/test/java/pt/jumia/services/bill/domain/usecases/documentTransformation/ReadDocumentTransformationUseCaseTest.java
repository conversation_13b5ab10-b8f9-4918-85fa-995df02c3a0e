package pt.jumia.services.bill.domain.usecases.documentTransformation;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;
import pt.jumia.services.bill.domain.usecases.documenettransformation.ReadDocumentTransformationUseCase;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith( MockitoExtension.class )
public class ReadDocumentTransformationUseCaseTest {

    private static final DocumentTransformation DOCUMENT_TRANSFORMATION = DocumentTransformation.builder()
            .id(1L)
            .document(Document.builder().id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb")).build())
            .newValue("newValue")
            .originalValue("oldValue")
            .createdAt(LocalDateTime.now(ZoneOffset.UTC))
            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
            .build();

    @Mock
    private DocumentTransformationRepository documentTransformationRepository;

    @InjectMocks
    private ReadDocumentTransformationUseCase readDocumentTransformationUseCase;

    @Test
    public void readByDocumentId_success() {
        List<DocumentTransformation> DOCUMENT_TRANSFORMATION_LIST = new ArrayList<>();
        DOCUMENT_TRANSFORMATION_LIST.add(DOCUMENT_TRANSFORMATION);
        when(documentTransformationRepository.findByDocumentId(DOCUMENT_TRANSFORMATION.getDocument().getId()))
                .thenReturn(DOCUMENT_TRANSFORMATION_LIST);

        List<DocumentTransformation> documentTransformation =
                readDocumentTransformationUseCase.readByDocumentId(DOCUMENT_TRANSFORMATION.getDocument().getId());

        verify(documentTransformationRepository).findByDocumentId(DOCUMENT_TRANSFORMATION.getDocument().getId());
        assertThat(documentTransformation).isEqualTo(DOCUMENT_TRANSFORMATION_LIST);
    }

    @Test
    public void fetchById_notFound() {
        when(documentTransformationRepository.findByDocumentId(DOCUMENT_TRANSFORMATION.getDocument().getId()))
                .thenReturn(Collections.emptyList());

        assertThat(readDocumentTransformationUseCase
                .readByDocumentId(DOCUMENT_TRANSFORMATION.getDocument().getId())).isEmpty();

        verify(documentTransformationRepository).findByDocumentId(DOCUMENT_TRANSFORMATION.getDocument().getId());
    }
}

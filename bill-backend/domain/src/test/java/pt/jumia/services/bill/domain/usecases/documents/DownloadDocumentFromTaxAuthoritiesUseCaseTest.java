package pt.jumia.services.bill.domain.usecases.documents;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class DownloadDocumentFromTaxAuthoritiesUseCaseTest extends BaseDomainTest {

    @Mock
    private TaxiRequester taxiRequester;

    private DownloadDocumentFromTaxAuthoritiesUseCase downloadDocumentFromTaxAuthoritiesUseCase;

    private static final UUID DOCUMENT_UUID = UUID.randomUUID();
    private static final Document DOCUMENT = Document.builder()
            .id(DOCUMENT_UUID)
            .sid("dummy document")
            .build();

    @BeforeEach
    void setUp() {
        downloadDocumentFromTaxAuthoritiesUseCase = new DownloadDocumentFromTaxAuthoritiesUseCase(
                taxiRequester
        );
    }

    @Test
    void executeByDocumentIdUUID() {
        String attachmentContent = "Once upon a time, there was ...";
        String contentType = "application/pdf";

        when(taxiRequester.getTaxAuthoritiesPdf(DOCUMENT.getId().toString())).thenReturn(
                DocumentFile.builder()
                        .content(attachmentContent.getBytes())
                        .mediaType(contentType)
                        .build()
        );

        DocumentFile documentFile = downloadDocumentFromTaxAuthoritiesUseCase.execute(DOCUMENT.getId().toString());

        assertThat(documentFile).isEqualTo(DocumentFile.builder()
                .content(attachmentContent.getBytes())
                .mediaType(contentType)
                .build());

        verify(taxiRequester).getTaxAuthoritiesPdf(DOCUMENT.getId().toString());
    }

    @Test
    void executeByDocumentIdUUIDNetworkException() throws TaxiNetworkException {
        when(taxiRequester.getTaxAuthoritiesPdf(DOCUMENT.getId().toString())).thenThrow(
                TaxiNetworkException.buildResponseNotOk(404, "Invalid Document Status")
        );

        assertThatThrownBy(() -> downloadDocumentFromTaxAuthoritiesUseCase.execute(DOCUMENT.getId().toString()))
                .isInstanceOf(TaxiNetworkException.class);
    }

}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

class DocumentValidationChainTest extends BaseDomainTest {

    @Mock
    private AtLeastOneLineValidator atLeastOneLineValidator;
    @Mock
    private LineCountValidator lineCountValidator;
    @Mock
    private OriginalDocumentExistsValidator originalDocumentExistsValidator;
    @Mock
    private DiffDocumentPayloadValidator diffDocumentPayloadValidator;
    @Mock
    private DocumentDuplicateValidator documentDuplicateValidator;
    @Mock
    private TaxCategoryTotalsUniqueValidator taxCategoryTotalsUniqueValidator;
    @Mock
    private TaxFieldsValidation taxFieldsValidation;
    @Mock
    private DocumentLineAppliedTaxFieldsValidation documentLineAppliedTaxFieldsValidation;
    @Mock
    private CurrencyExchangeRateValidator currencyExchangeRateValidator;
    @Mock
    private LinesPositionsValidator linesPositionsValidator;
    @Mock
    private DocumentIssuedReasonValidator documentIssuedReasonValidator;

    @Mock
    private KeDocumentValidator keDocumentValidator;

    private DocumentValidationChain documentValidationChain;

    @BeforeEach
    void setUp() {
        documentValidationChain = new DocumentValidationChain(
                atLeastOneLineValidator,
                lineCountValidator,
                originalDocumentExistsValidator,
                diffDocumentPayloadValidator,
                documentDuplicateValidator,
                taxCategoryTotalsUniqueValidator,
                taxFieldsValidation,
                documentLineAppliedTaxFieldsValidation,
                currencyExchangeRateValidator,
                linesPositionsValidator,
                documentIssuedReasonValidator,
                keDocumentValidator
        );
    }

    @Test
    void testRun_validatorsAreCalledAndErrorsArePropagated() {
        when(atLeastOneLineValidator.validate(any())).thenReturn(List.of());
        when(lineCountValidator.validate(any())).thenReturn(List.of());
        when(originalDocumentExistsValidator.validate(any())).thenReturn(List.of());
        when(documentDuplicateValidator.validate(any())).thenReturn(List.of());
        when(taxCategoryTotalsUniqueValidator.validate(any())).thenReturn(List.of());

        // Prepare
        List<ValidationError> someErrors = List.of(
                new ValidationError(ErrorCode.MISSING_REQUIRED_FIELD, "Missing line"),
                new ValidationError(ErrorCode.INCORRECT_LINE_COUNT, "Missing line")
        );
        List<ValidationError> moreErrors = List.of(
                new ValidationError(ErrorCode.INVALID_ORIGINAL_DOCUMENT, "Invalid original document")
        );


        when(atLeastOneLineValidator.validate(any())).thenReturn(someErrors);
        when(documentDuplicateValidator.validate(any())).thenReturn(moreErrors);

        // Execute
        List<ValidationError> errors = documentValidationChain.runAllValidations(DocumentAggregate.builder().build());

        // Verify
        assertThat(errors).containsAll(someErrors).containsAll(moreErrors);
        verify(atLeastOneLineValidator).validate(any());
        verify(lineCountValidator).validate(any());
        verify(originalDocumentExistsValidator).validate(any());
        verify(diffDocumentPayloadValidator).validate(any());
        verify(documentDuplicateValidator).validate(any());
    }

    @Test
    void testRun_preSplitValidatorsAreCalledAndErrorsArePropagated() {
        when(atLeastOneLineValidator.validate(any())).thenReturn(List.of());
        when(lineCountValidator.validate(any())).thenReturn(List.of());
        when(taxCategoryTotalsUniqueValidator.validate(any())).thenReturn(List.of());

        // Prepare
        List<ValidationError> someErrors = List.of(
                new ValidationError(ErrorCode.MISSING_REQUIRED_FIELD, "Missing line"),
                new ValidationError(ErrorCode.INCORRECT_LINE_COUNT, "Missing line")
        );

        when(atLeastOneLineValidator.validate(any())).thenReturn(someErrors);

        // Execute
        List<ValidationError> errors = documentValidationChain.runPreSplitValidations(DocumentAggregate.builder().build());

        // Verify
        assertThat(errors).containsAll(someErrors);
        verify(atLeastOneLineValidator).validate(any());
        verify(lineCountValidator).validate(any());
        verifyNoInteractions(originalDocumentExistsValidator);
        verifyNoInteractions(diffDocumentPayloadValidator);
        verifyNoInteractions(documentDuplicateValidator);
    }


    @Test
    void testRun_postSplitValidatorsAreCalledAndErrorsArePropagated() {
        when(originalDocumentExistsValidator.validate(any())).thenReturn(List.of());
        when(documentDuplicateValidator.validate(any())).thenReturn(List.of());


        List<ValidationError> someErrors = List.of(
                new ValidationError(ErrorCode.INVALID_ORIGINAL_DOCUMENT, "Invalid original document")
        );

        when(documentDuplicateValidator.validate(any())).thenReturn(someErrors);

        // Execute
        List<ValidationError> errors = documentValidationChain.runPostSplitValidations(DocumentAggregate.builder().build());

        // Verify
        assertThat(errors).containsAll(someErrors);
        verifyNoInteractions(atLeastOneLineValidator);
        verifyNoInteractions(lineCountValidator);
        verifyNoInteractions(taxCategoryTotalsUniqueValidator);
        verify(originalDocumentExistsValidator).validate(any());
        verify(diffDocumentPayloadValidator).validate(any());
        verify(documentDuplicateValidator).validate(any());
    }
}

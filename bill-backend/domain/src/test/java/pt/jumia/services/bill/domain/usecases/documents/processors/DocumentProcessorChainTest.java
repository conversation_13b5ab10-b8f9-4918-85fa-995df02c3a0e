package pt.jumia.services.bill.domain.usecases.documents.processors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

class DocumentProcessorChainTest extends BaseDomainTest {
    @Mock
    private PopulateOriginalDocumentProcessor populateOriginalDocumentProcessor;
    @Mock
    private MoveErrorDocumentsToRetriedProcessor moveErrorDocumentsToRetriedProcessor;
    @Mock
    private AfromsProcessor afromsProcessor;
    @Mock
    private AutoAckDocumentProcessor autoAckDocumentProcessor;

    private DocumentProcessorChain documentProcessorChain;

    @BeforeEach
    void setUp() {
        documentProcessorChain = new DocumentProcessorChain(
                populateOriginalDocumentProcessor,
                autoAckDocumentProcessor,
                moveErrorDocumentsToRetriedProcessor,
                afromsProcessor
        );
    }

    @Test
    void testRunBeforeSaveChain_processorsAreCalled() {
        // Execute
        documentProcessorChain.runBeforeSave(DocumentAggregate.builder().build());

        // Verify
        verify(populateOriginalDocumentProcessor).process(any());
        verify(autoAckDocumentProcessor).process(any());
        verifyNoInteractions(moveErrorDocumentsToRetriedProcessor);
    }

    @Test
    void testRunAfterSaveChain_processorsAreCalled() {
        // Execute
        documentProcessorChain.runAfterSave(DocumentAggregate.builder().build());

        // Verify
        verify(moveErrorDocumentsToRetriedProcessor).process(any());
        verifyNoInteractions(autoAckDocumentProcessor);
        verifyNoInteractions(populateOriginalDocumentProcessor);
    }
}

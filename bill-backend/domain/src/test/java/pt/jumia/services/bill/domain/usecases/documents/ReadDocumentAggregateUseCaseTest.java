package pt.jumia.services.bill.domain.usecases.documents;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class ReadDocumentAggregateUseCaseTest extends BaseDomainTest {

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .document(Document
                    .builder()
                    .sid("dummy document")
                    .generateId()
                    .build())
            .build();

    @Mock
    private DocumentAggregateRepository documentAggregateRepository;

    private ReadDocumentAggregateUseCase readDocumentAggregateUseCase;

    @BeforeEach
    public void setUp() {
        readDocumentAggregateUseCase = new ReadDocumentAggregateUseCase(documentAggregateRepository);
    }

    @Test
    void readDocumentAggregateById() {
        when(documentAggregateRepository
                .findByDocumentId(DOCUMENT_AGGREGATE.getDocument().getId()))
                .thenReturn(Optional.of(DOCUMENT_AGGREGATE));

        DocumentAggregate documentAggregate = readDocumentAggregateUseCase
                .execute(DOCUMENT_AGGREGATE.getDocument().getId());

        assertThat(documentAggregate).isEqualTo(DOCUMENT_AGGREGATE);
        verify(documentAggregateRepository)
                .findByDocumentId(DOCUMENT_AGGREGATE.getDocument().getId());
    }

    @Test
    void readDocumentAggregateById_notFound() {
        when(documentAggregateRepository
                .findByDocumentId(DOCUMENT_AGGREGATE.getDocument().getId()))
                .thenThrow(EntityNotFoundException.class);

        Assertions.assertThrows(EntityNotFoundException.class, () -> readDocumentAggregateUseCase
                .execute(DOCUMENT_AGGREGATE.getDocument().getId()));

        verify(documentAggregateRepository)
                .findByDocumentId(DOCUMENT_AGGREGATE.getDocument().getId());
    }

    @Test
    void readDocumentAggregateBySid() {
        DocumentFilter documentFilter = DocumentFilter.builder()
                .statuses(List.of(DocumentStatus.TAX_SUCCESS, DocumentStatus.TAX_SKIPPED))
                .sid(DOCUMENT_AGGREGATE.getDocument().getSid())
                .include(List.of(Document.Details.values()))
                .build();
        when(documentAggregateRepository
                .findAll(documentFilter))
                .thenReturn(List.of(DOCUMENT_AGGREGATE));
        DocumentAggregate documentAggregate = readDocumentAggregateUseCase
                .executeBySidAndSuccessStatus(DOCUMENT_AGGREGATE.getDocument().getSid());

        assertThat(documentAggregate).isEqualTo(DOCUMENT_AGGREGATE);
        verify(documentAggregateRepository).findAll(documentFilter);
    }

    @Test
    void readDocumentAggregateBySid_notFound() {
        DocumentFilter documentFilter = DocumentFilter.builder()
                .statuses(List.of(DocumentStatus.TAX_SUCCESS, DocumentStatus.TAX_SKIPPED))
                .sid(DOCUMENT_AGGREGATE.getDocument().getSid())
                .include(List.of(Document.Details.values()))
                .build();

        when(documentAggregateRepository.findAll(documentFilter))
                .thenReturn(List.of());

        Assertions.assertThrows(EntityNotFoundException.class, () -> readDocumentAggregateUseCase
                .executeBySidAndSuccessStatus(DOCUMENT_AGGREGATE.getDocument().getSid()));

        verify(documentAggregateRepository).findAll(documentFilter);
    }
}

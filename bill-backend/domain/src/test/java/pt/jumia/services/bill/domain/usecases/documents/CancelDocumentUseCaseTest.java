package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.CancelRequest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.SettingNotActiveException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.settings.CancellationSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;

import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class CancelDocumentUseCaseTest extends BaseDomainTest {

    private static final UUID DOCUMENT_UUID = UUID.randomUUID();
    private static final CancelRequest CANCEL_REQUEST = CancelRequest.builder()
            .documentId(DOCUMENT_UUID)
            .cancellationReason("Cancellation Reason")
            .build();

    @Mock
    private TaxiRequester taxiRequester;

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private OverallSettings overallSettings;

    @Mock
    private CancellationSettings cancellationSettings;

    @InjectMocks
    private CancelDocumentUseCase cancelDocumentUseCase;

    @Test
    void execute_success_invoice() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .status(DocumentStatus.TAX_SUCCESS)
                .type(DocumentType.SALES_INVOICE)
                .shop("jumia")
                .country(CountryCode.EG)
                .build();
        Document expected = document.toBuilder().status(DocumentStatus.TAX_CANCELLED).build();

        when(overallSettings.getCancellationSettings()).thenReturn(cancellationSettings);
        when(cancellationSettings.getInvoiceCancellationEnabled("jumia", "EG")).
                thenReturn(true);
        when(documentRepository.save(expected)).thenReturn(expected);

        Document actual = cancelDocumentUseCase.execute(document, CANCEL_REQUEST);

        assertEquals(expected, actual);
        verify(taxiRequester).cancelDocument(CANCEL_REQUEST);
        verify(documentRepository).save(document.toBuilder().status(DocumentStatus.TAX_CANCELLED).build());
    }

    @Test
    void execute_success_creditNote() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .status(DocumentStatus.TAX_SUCCESS)
                .type(DocumentType.SALES_CREDIT_NOTE)
                .shop("jumia")
                .country(CountryCode.EG)
                .build();
        Document expected = document.toBuilder().status(DocumentStatus.TAX_CANCELLED).build();

        when(overallSettings.getCancellationSettings()).thenReturn(cancellationSettings);
        when(cancellationSettings.getCreditNoteCancellationEnabled("jumia", "EG")).
                thenReturn(true);
        when(documentRepository.save(expected)).thenReturn(expected);

        Document actual = cancelDocumentUseCase.execute(document, CANCEL_REQUEST);

        assertEquals(expected, actual);
        verify(taxiRequester).cancelDocument(CANCEL_REQUEST);
        verify(documentRepository).save(document.toBuilder().status(DocumentStatus.TAX_CANCELLED).build());
    }

    @Test
    void execute_documentAlreadyCancelled() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .status(DocumentStatus.TAX_CANCELLED)
                .type(DocumentType.SALES_INVOICE)
                .shop("jumia")
                .country(CountryCode.EG)
                .build();


        cancelDocumentUseCase.execute(document, CANCEL_REQUEST);

        verifyNoInteractions(taxiRequester);
        verifyNoInteractions(overallSettings);
        verifyNoInteractions(cancellationSettings);
        verifyNoInteractions(documentRepository);
    }

    @Test
    void execute_invalidStatus() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .status(DocumentStatus.TAX_PENDING)
                .type(DocumentType.SALES_INVOICE)
                .shop("jumia")
                .country(CountryCode.EG)
                .build();

        assertThatThrownBy(() -> cancelDocumentUseCase.execute(document, CANCEL_REQUEST))
                .isInstanceOf(ConflictOperationException.class);

        verifyNoInteractions(taxiRequester);
        verifyNoInteractions(overallSettings);
        verifyNoInteractions(cancellationSettings);
        verifyNoInteractions(documentRepository);
    }

    @Test
    public void execute_settingIsDisabled() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .status(DocumentStatus.TAX_SUCCESS)
                .type(DocumentType.SALES_INVOICE)
                .shop("jumia")
                .country(CountryCode.EG)
                .build();

        when(overallSettings.getCancellationSettings()).thenReturn(cancellationSettings);
        when(cancellationSettings.getInvoiceCancellationEnabled("jumia", "EG")).
                thenReturn(false);

        assertThatThrownBy(() -> cancelDocumentUseCase.execute(document, CANCEL_REQUEST))
                .isInstanceOf(SettingNotActiveException.class);

        verifyNoInteractions(taxiRequester);
        verifyNoInteractions(documentRepository);
    }

    @Test
    void execute_success_debitNote() {
        Document document = Document.builder()
                .id(DOCUMENT_UUID)
                .status(DocumentStatus.TAX_SUCCESS)
                .type(DocumentType.SALES_DEBIT_NOTE)
                .shop("jumia")
                .country(CountryCode.EG)
                .build();
        Document expected = document.toBuilder().status(DocumentStatus.TAX_CANCELLED).build();

        when(overallSettings.getCancellationSettings()).thenReturn(cancellationSettings);
        when(cancellationSettings.getDebitNoteCancellationEnabled("jumia", "EG")).
                thenReturn(true);
        when(documentRepository.save(expected)).thenReturn(expected);

        Document actual = cancelDocumentUseCase.execute(document, CANCEL_REQUEST);

        assertEquals(expected, actual);
        verify(taxiRequester).cancelDocument(CANCEL_REQUEST);
        verify(documentRepository).save(document.toBuilder().status(DocumentStatus.TAX_CANCELLED).build());
    }

}

package pt.jumia.services.bill.domain.usecases.documents;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import pt.jumia.services.bill.domain.BaseDomainTest;

import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.fake.FakeDocument;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;;

import java.util.Optional;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;


class DocumentResendUseCaseTest extends BaseDomainTest {

    @Mock
    DocumentAggregateRepository documentAggregateRepository;

    @Mock
    private TaxiRequester taxiRequester;

    private DocumentResendUseCase documentResendUseCase;

    @BeforeEach
    void setUp() {
        documentResendUseCase = new DocumentResendUseCase(
                documentAggregateRepository,
                taxiRequester
        );
    }

    @Test
    void execute_resend_when_taxi_internal_error_success() {
        Document document = FakeDocument.DOCUMENT_1.toBuilder().country(CountryCode.EG).status(DocumentStatus.TAXI_INTERNAL_ERROR).build();
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .build();

        when(documentAggregateRepository.findByDocumentId(document.getId())).thenReturn(Optional.of(documentAggregate));

        documentResendUseCase.execute(document);

        verify(taxiRequester).pushInvoice(documentAggregate);
        verifyNoMoreInteractions(taxiRequester);
    }

    @Test
    void execute_resend_when_taxi_pending_success() {
        Document document = FakeDocument.DOCUMENT_1.toBuilder().country(CountryCode.EG).status(DocumentStatus.TAX_PENDING).build();
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(document)
                .build();

        when(documentAggregateRepository.findByDocumentId(document.getId())).thenReturn(Optional.of(documentAggregate));

        documentResendUseCase.execute(document);

        verify(taxiRequester).pushInvoice(documentAggregate);
        verifyNoMoreInteractions(taxiRequester);
    }
}

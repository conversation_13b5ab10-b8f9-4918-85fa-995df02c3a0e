package pt.jumia.services.bill.domain.usecases.report;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.CommunicationsRequester;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.settings.DailyIssuedDocumentsReportSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentsUseCase;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DailyIssuedDocumentsReportUseCaseTest {

    @Mock
    private ReadDocumentsUseCase readDocumentsUseCase;
    @Mock
    private CommunicationsRequester communicationsRequester;
    @Mock
    private OverallSettings overallSettings;
    @Mock
    private DailyIssuedDocumentsReportSettings dailyIssuedDocumentsReportSettings;

    private DailyIssuedDocumentsReportUseCase dailyIssuedDocumentsReportUseCase;

    @BeforeEach
    void setUp() {
        dailyIssuedDocumentsReportUseCase = new DailyIssuedDocumentsReportUseCase(
                readDocumentsUseCase,
                communicationsRequester,
                overallSettings
        );

        when(overallSettings.getDailyIssuedDocumentsReportSettings()).thenReturn(dailyIssuedDocumentsReportSettings);
    }

    @Test
    void executeGenerateDailyReport() {
        //Mock settings
        when(dailyIssuedDocumentsReportSettings.getEnabledBusinessLines()).thenReturn(
                List.of(BusinessLine.builder()
                                .countryCode(CountryCode.EG)
                                .shop("jumia")
                                .build(),
                        BusinessLine.builder()
                                .countryCode(CountryCode.UG)
                                .shop("jumia")
                                .build())
        );

        when(dailyIssuedDocumentsReportSettings.getCommunicationEmailsForBusinessLine("jumia", "EG"))
                .thenReturn(List.of("<EMAIL>"));

        when(dailyIssuedDocumentsReportSettings.getCommunicationEmailsForBusinessLine("jumia", "UG"))
                .thenReturn(List.of("<EMAIL>"));

        when(dailyIssuedDocumentsReportSettings.getDetailedReportTimeFrameInHours(anyString())).thenReturn(36);
        when(dailyIssuedDocumentsReportSettings.getOverviewReportTimeFrameInDays(anyString())).thenReturn(5);

        //Mock count requests
        when(readDocumentsUseCase.executeCountAll(
                any(DocumentFilter.class)
        )).thenReturn(100L);

        LocalDateTime reportRequestTime = LocalDateTime.now();
        dailyIssuedDocumentsReportUseCase.generateDailyReport(reportRequestTime);
        LocalDateTime dayReportStart = reportRequestTime.minusHours(36);
        LocalDateTime rangeReportStart = reportRequestTime.minusDays(5);

        //verify settings requested
        verify(dailyIssuedDocumentsReportSettings, times(2)).getDetailedReportTimeFrameInHours(anyString());
        verify(dailyIssuedDocumentsReportSettings, times(2)).getOverviewReportTimeFrameInDays(anyString());
        verify(dailyIssuedDocumentsReportSettings).getEnabledBusinessLines();
        verify(dailyIssuedDocumentsReportSettings).getCommunicationEmailsForBusinessLine("jumia", "EG");
        verify(dailyIssuedDocumentsReportSettings).getCommunicationEmailsForBusinessLine("jumia", "UG");

        //verify read document calls
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter.builder()
                .countryCode(CountryCode.EG)
                .flow(DocumentFlow.RETAIL)
                .shop("jumia")
                .status(DocumentStatus.NEW)
                .updatedAtFrom(dayReportStart)
                .updatedAtTo(reportRequestTime)
                .build());
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter.builder()
                .countryCode(CountryCode.EG)
                .flow(DocumentFlow.RETAIL)
                .shop("jumia")
                .status(DocumentStatus.TAX_SUCCESS)
                .updatedAtFrom(rangeReportStart)
                .updatedAtTo(reportRequestTime)
                .build());
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter.builder()
                .countryCode(CountryCode.UG)
                .flow(DocumentFlow.RETAIL)
                .shop("jumia")
                .status(DocumentStatus.TAX_SUBMITTED_INVALID)
                .updatedAtFrom(dayReportStart)
                .updatedAtTo(reportRequestTime)
                .build());
        verify(readDocumentsUseCase).executeCountAll(DocumentFilter.builder()
                .countryCode(CountryCode.UG)
                .flow(DocumentFlow.RETAIL)
                .shop("jumia")
                .status(DocumentStatus.TAX_PENDING)
                .updatedAtFrom(rangeReportStart)
                .updatedAtTo(reportRequestTime)
                .build());

        //verify emails sent
        verify(communicationsRequester).sendEmailByIssuedDocumentsReportToReceiver(generateDailyReportPayload(
                CountryCode.EG,
                "jumia",
                reportRequestTime,
                dayReportStart,
                rangeReportStart
        ), "<EMAIL>");
        verify(communicationsRequester).sendEmailByIssuedDocumentsReportToReceiver(generateDailyReportPayload(
                CountryCode.UG,
                "jumia",
                reportRequestTime,
                dayReportStart,
                rangeReportStart
        ), "<EMAIL>");
    }

    private DailyIssuedDocumentsReport generateDailyReportPayload(
            CountryCode countryCode,
            String shop,
            LocalDateTime reportRequestTime,
            LocalDateTime detailedReportStart,
            LocalDateTime overviewReportStart
    ) {
        Map<DocumentStatus, Long> statusesMap = new HashMap<DocumentStatus, Long>();
        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            statusesMap.put(documentStatus, 100L);
        }
        return DailyIssuedDocumentsReport.builder()
                .businessLine(BusinessLine.builder()
                        .countryCode(countryCode)
                        .shop(shop)
                        .build())
                .detailedReport(DailyIssuedDocumentsReport.RangeReport.builder()
                        .reportRangeStartDate(detailedReportStart)
                        .reportRangeEndDate(reportRequestTime)
                        .statusesMap(statusesMap)
                        .range(36)
                        .build())
                .overviewReport(DailyIssuedDocumentsReport.RangeReport.builder()
                        .reportRangeStartDate(overviewReportStart)
                        .reportRangeEndDate(reportRequestTime)
                        .statusesMap(statusesMap)
                        .range(5)
                        .build())
                .build();
    }
}

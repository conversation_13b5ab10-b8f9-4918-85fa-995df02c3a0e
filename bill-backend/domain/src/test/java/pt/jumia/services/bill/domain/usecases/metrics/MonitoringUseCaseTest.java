package pt.jumia.services.bill.domain.usecases.metrics;

import com.neovisionaries.i18n.CountryCode;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.Counter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
public class MonitoringUseCaseTest {

    private static final String NAMESPACE = "bill:";
    private static final String CREATED_DOCUMENTS_REQUEST_COUNTER_NAME = "created_documents_total";
    private static final String CREATED_DOCUMENTS_REQUEST_DESCRIPTION = "Total created documents.";
    private static final String TAX_UPDATES_REQUEST_COUNTER_NAME = "tax_updates_documents_total";
    private static final String TAX_UPDATES_REQUEST_DESCRIPTION = "Total tax updates documents.";
    private static final String RETRIED_DOCUMENTS_COUNTER_NAME = "retried_documents_total";
    private static final String RETRIED_DOCUMENTS_REQUEST_DESCRIPTION = "Total retried documents.";
    private static final String SHOP_LABEL_KEY = "shop";
    private static final String COUNTRY_LABEL_KEY = "country";
    private static final String STATUS_LABEL_KEY = "status";
    private static final String DOCUMENT_TYPE_LABEL_KEY = "document_type";

    private static final String SHOP = "jumia";
    private static final CountryCode COUNTRY = CountryCode.EG;
    private static final DocumentType DOCUMENT_TYPE = DocumentType.SALES_INVOICE;
    private static final Document DOCUMENT = Document.builder()
            .shop(SHOP)
            .country(COUNTRY)
            .type(DOCUMENT_TYPE)
            .build();
    private static final DocumentStatus STATUS_TAX_SUCCESS = DocumentStatus.TAX_SUCCESS;

    private Counter createdDocumentsRequests;
    private Counter taxUpdatesRequests;
    private Counter retriedDocumentsRequests;

    @Mock
    private CollectorRegistry collectorRegistry;

    @InjectMocks
    private MonitoringUseCase monitoringUseCase;

    @BeforeEach
    public void mockCounters() {

        this.createdDocumentsRequests = Mockito.spy(Counter.build()
                .name(NAMESPACE.concat(CREATED_DOCUMENTS_REQUEST_COUNTER_NAME))
                .help(CREATED_DOCUMENTS_REQUEST_DESCRIPTION)
                .labelNames(SHOP_LABEL_KEY, COUNTRY_LABEL_KEY, DOCUMENT_TYPE_LABEL_KEY)
                .register(this.collectorRegistry));

        this.taxUpdatesRequests = Mockito.spy(Counter.build()
                .name(NAMESPACE.concat(TAX_UPDATES_REQUEST_COUNTER_NAME))
                .help(TAX_UPDATES_REQUEST_DESCRIPTION)
                .labelNames(SHOP_LABEL_KEY, COUNTRY_LABEL_KEY, DOCUMENT_TYPE_LABEL_KEY, STATUS_LABEL_KEY)
                .register(this.collectorRegistry));

        this.retriedDocumentsRequests = Mockito.spy(Counter.build()
                .name(NAMESPACE.concat(RETRIED_DOCUMENTS_COUNTER_NAME))
                .help(RETRIED_DOCUMENTS_REQUEST_DESCRIPTION)
                .labelNames(SHOP_LABEL_KEY, COUNTRY_LABEL_KEY, DOCUMENT_TYPE_LABEL_KEY)
                .register(this.collectorRegistry));

        ReflectionTestUtils.setField(monitoringUseCase, "createdDocumentsRequests", this.createdDocumentsRequests);
        ReflectionTestUtils.setField(monitoringUseCase, "taxUpdatesRequests", this.taxUpdatesRequests);
        ReflectionTestUtils.setField(monitoringUseCase, "retriedDocumentsRequests", this.retriedDocumentsRequests);
    }

    @Test
    public void testRecordCreatedDocumentsRequestsMetrics() {

        Counter.Child child = mock(Counter.Child.class);

        doReturn(child).when(createdDocumentsRequests).labels(any());

        monitoringUseCase.recordCreatedDocumentsRequest(DOCUMENT);

        verify(createdDocumentsRequests).labels(SHOP, COUNTRY.getAlpha2(), DOCUMENT_TYPE.name());
        verify(child).inc();

        verifyNoInteractions(taxUpdatesRequests);
        verifyNoInteractions(retriedDocumentsRequests);
    }

    @Test
    public void testRecordTaxUpdatesRequestMetrics() {

        Counter.Child child = mock(Counter.Child.class);

        doReturn(child).when(taxUpdatesRequests).labels(any());

        monitoringUseCase.recordTaxUpdatesRequest(DOCUMENT.toBuilder().status(STATUS_TAX_SUCCESS).build());

        verify(taxUpdatesRequests).labels(SHOP, COUNTRY.getAlpha2(), DOCUMENT_TYPE.name(), STATUS_TAX_SUCCESS.name());
        verify(child).inc();

        verifyNoInteractions(createdDocumentsRequests);
        verifyNoInteractions(retriedDocumentsRequests);
    }

    @Test
    public void testRecordRetriedDocumentsRequestMetrics() {

        Counter.Child child = mock(Counter.Child.class);

        doReturn(child).when(retriedDocumentsRequests).labels(any());

        monitoringUseCase.recordRetriedDocumentsRequest(DOCUMENT);

        verify(retriedDocumentsRequests).labels(SHOP, COUNTRY.getAlpha2(), DOCUMENT_TYPE.name());
        verify(child).inc();

        verifyNoInteractions(createdDocumentsRequests);
        verifyNoInteractions(taxUpdatesRequests);
    }
}

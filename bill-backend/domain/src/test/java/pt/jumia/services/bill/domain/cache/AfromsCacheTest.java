package pt.jumia.services.bill.domain.cache;

import com.neovisionaries.i18n.CountryCode;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import pt.jumia.services.bill.domain.AfromsRequester;
import pt.jumia.services.bill.domain.BaseDomainTest;
import pt.jumia.services.bill.domain.caches.AfromsCache;
import pt.jumia.services.bill.domain.entities.AfromsProductDetails;
import pt.jumia.services.bill.domain.properties.NetworkProperties;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class AfromsCacheTest extends BaseDomainTest {

    private static final NetworkProperties.Afroms AFR_OMS_PROPERTIES = new NetworkProperties.Afroms();

    @Mock
    private AfromsRequester afromsRequester;

    @Mock
    private NetworkProperties networkProperties;

    private AfromsCache afrOmsCache;

    @BeforeEach
    public void setUp() {
        when(networkProperties.getAfroms()).thenReturn(AFR_OMS_PROPERTIES);

        MeterRegistry meterRegistry = new SimpleMeterRegistry();

        afrOmsCache = new AfromsCache(
                afromsRequester,
                networkProperties,
                meterRegistry
        );
    }

    @Test
    public void testOms_missingInCache() {
        CountryCode countryCode = CountryCode.EG;
        String shop = "jumia";
        String itemCode = "ZZ918MW19QHE0NAFAMZ";
        String variant = "38898562";
        AfromsProductDetails afromsProductDetails = AfromsProductDetails.builder()
                .ucrId("test-id")
                .ucrPath("test-path")
                .build();
        doReturn(afromsProductDetails).when(afromsRequester).getProductDetails(countryCode.getAlpha2(),
                shop,
                this.formatSKU(itemCode, variant));

        AfromsProductDetails afromsProdDetails = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);

        verify(afromsRequester).getProductDetails(countryCode.getAlpha2(), shop, this.formatSKU(itemCode, variant));
        assertThat(afromsProdDetails).isEqualTo(afromsProductDetails);
    }

    @Test
    public void testOms_presentInCache() {
        CountryCode countryCode = CountryCode.EG;
        String shop = "jumia";
        String itemCode = "ZZ918MW19QHE0NAFAMZ";
        String variant = "38898562";
        AfromsProductDetails afromsProductDetails = AfromsProductDetails.builder()
                .ucrId("test-id")
                .ucrPath("test-path")
                .build();
        doReturn(afromsProductDetails).when(afromsRequester).getProductDetails(countryCode.getAlpha2(),
                shop,
                this.formatSKU(itemCode, variant));

        AfromsProductDetails afromsProdDetails1 = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);
        AfromsProductDetails afromsProdDetails2 = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);

        verify(afromsRequester, times(1)).getProductDetails(countryCode.getAlpha2(),
                shop,
                this.formatSKU(itemCode, variant));
        assertThat(afromsProdDetails1).isEqualTo(afromsProductDetails);
        assertThat(afromsProdDetails2).isEqualTo(afromsProductDetails);
    }

    @Test
    public void testOms_nullVariant_missingInCache() {
        CountryCode countryCode = CountryCode.EG;
        String shop = "jumia";
        String itemCode = "ZZ918MW19QHE0NAFAMZ";
        String variant = null;
        AfromsProductDetails afromsProductDetails = AfromsProductDetails.builder()
                .ucrId("test-id")
                .ucrPath("test-path")
                .build();
        doReturn(afromsProductDetails).when(afromsRequester).getProductDetails(countryCode.getAlpha2(),
                shop,
                itemCode);

        AfromsProductDetails afromsProdDetails = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);

        verify(afromsRequester).getProductDetails(countryCode.getAlpha2(), shop, itemCode);
        assertThat(afromsProdDetails).isEqualTo(afromsProductDetails);
    }

    @Test
    public void testOms_nullVariant_presentInCache() {
        CountryCode countryCode = CountryCode.EG;
        String shop = "jumia";
        String itemCode = "ZZ918MW19QHE0NAFAMZ";
        String variant = null;
        AfromsProductDetails afromsProductDetails = AfromsProductDetails.builder()
                .ucrId("test-id")
                .ucrPath("test-path")
                .build();
        doReturn(afromsProductDetails).when(afromsRequester).getProductDetails(countryCode.getAlpha2(),
                shop,
                itemCode);

        AfromsProductDetails afromsProdDetails1 = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);
        AfromsProductDetails afromsProdDetails2 = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);

        verify(afromsRequester, times(1)).getProductDetails(countryCode.getAlpha2(),
                shop,
                itemCode);
        assertThat(afromsProdDetails1).isEqualTo(afromsProductDetails);
        assertThat(afromsProdDetails2).isEqualTo(afromsProductDetails);
    }

    @Test
    public void testOms_cache_emptyAfrOmsProductDetails() {
        CountryCode countryCode = CountryCode.EG;
        String shop = "jumia";
        String itemCode = "ZZ918MW19QHE0NAFAMZ";
        String variant = "38898562";
        AfromsProductDetails afromsProductDetails = AfromsProductDetails.builder().build();
        doReturn(afromsProductDetails).when(afromsRequester).getProductDetails(countryCode.getAlpha2(),
                shop,
                this.formatSKU(itemCode, variant));

        AfromsProductDetails afromsProdDetails1 = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);
        AfromsProductDetails afromsProdDetails2 = afrOmsCache.getAfromsCacheProductDetails(countryCode,
                shop,
                itemCode,
                variant);

        verify(afromsRequester, times(1)).getProductDetails(countryCode.getAlpha2(),
                shop,
                this.formatSKU(itemCode, variant));
        assertThat(afromsProdDetails1).isEqualTo(afromsProductDetails);
        assertThat(afromsProdDetails2).isEqualTo(afromsProductDetails);
    }

    private String formatSKU(String itemCode, String variantId) {
        return String.format("%s-%s", itemCode, variantId);
    }
}

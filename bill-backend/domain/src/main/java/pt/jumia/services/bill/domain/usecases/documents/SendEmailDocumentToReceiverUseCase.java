package pt.jumia.services.bill.domain.usecases.documents;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import pt.jumia.services.bill.domain.CommunicationsRequester;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.Notification;
import pt.jumia.services.bill.domain.exceptions.document.MissingDocumentPdfException;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.notification.ReadNotificationUseCase;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class SendEmailDocumentToReceiverUseCase {

    private final CommunicationsRequester communicationsRequester;
    private final OverallSettings overallSettings;
    private final ReadNotificationUseCase readNotificationUseCase;
    private final GenerateJudgeDocumentUseCase generateJudgeDocumentUseCase;

    public void execute(DocumentAggregate documentAggregate) {
        if (documentAggregate.getDocument().getJudgeSid() == null) {
            throw MissingDocumentPdfException.isNull(documentAggregate.getDocument());
        }

        if (this.overallSettings.getCommunicationsSettings().isEnabled(
                documentAggregate.getDocument().getShop(),
                documentAggregate.getDocument().getCountry().getAlpha2())) {

            if (hasContactInformation(documentAggregate)) {
                log.info("Notifying '{}' with document '{}'",
                        documentAggregate.getDocument().getReceiver().getEmail(), documentAggregate.getDocument().getSid());
                communicationsRequester.sendEmailReceivedDocument(documentAggregate);
            } else {
                log.warn("Not contacting receiver as we don't have contact information '{}'",
                        documentAggregate.getDocument().getSid());
            }

        }
    }

    public void execute(DocumentAggregate documentAggregate, List<String> emails) throws JsonProcessingException {
        List<Notification> notifications = readNotificationUseCase.getPurchasePortal();

        if (ObjectUtils.isEmpty(documentAggregate.getDocument().getJudgeSid())){
            log.info("Generating Judge Sid for document '{}'", documentAggregate.getDocument());
            documentAggregate = generateJudgeDocumentUseCase.execute(documentAggregate);
        }

        emails.addAll(notifications.stream()
                .map(Notification::getEmail)
                .collect(Collectors.toList()));

        DocumentAggregate finalDocumentAggregate = documentAggregate;
        emails.forEach(email -> {
            communicationsRequester.sendEmailReceivedDocument(finalDocumentAggregate, email);
        });
    }

    private boolean hasContactInformation(DocumentAggregate documentAggregate) {
        if (documentAggregate.getDocument().getReceiver() == null) {
            return false;
        }
        return documentAggregate.getDocument().getReceiver().getEmail() != null ||
                documentAggregate.getDocument().getReceiver().getMobilePhone() != null;
    }
}

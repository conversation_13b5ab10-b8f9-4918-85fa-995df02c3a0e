package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class TaxCategoryTotal {

    //
    // Internal fields
    //

    private UUID id;
    private Document document;

    //
    // Tax information
    //

    private TaxCategory taxCategory;
    private BigDecimal taxRate;
    private BigDecimal taxFixedAmount;

    //
    // Summary
    //

    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;

    public TaxCategoryTotal withoutDbField() {
        return this.toBuilder()
                .id(null)
                .document(null)
                .taxRate(taxRate != null ? taxRate.setScale(4, RoundingMode.CEILING) : null)
                .taxFixedAmount(taxFixedAmount != null ? taxFixedAmount.setScale(4, RoundingMode.CEILING) : null)
                .totalAmount(totalAmount != null ? totalAmount.setScale(4, RoundingMode.CEILING) : null)
                .netAmount(netAmount != null ? netAmount.setScale(4, RoundingMode.CEILING) : null)
                .taxAmount(taxAmount != null ? taxAmount.setScale(4, RoundingMode.CEILING) : null)
                .build();
    }

    public static class TaxCategoryTotalBuilder {

        public TaxCategoryTotalBuilder generateId() {
            id = UUID.randomUUID();
            return this;
        }

    }

    public void convertAmountsToPositiveValue() {
        this.taxFixedAmount = this.taxFixedAmount != null && this.taxFixedAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.taxFixedAmount.negate() : this.taxFixedAmount;
        this.taxAmount = this.taxAmount != null && this.taxAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.taxAmount.negate() : this.taxAmount;
        this.totalAmount = this.totalAmount != null && this.totalAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalAmount.negate() : this.totalAmount;
        this.netAmount = this.netAmount != null && this.netAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.netAmount.negate() : this.netAmount;
    }
}

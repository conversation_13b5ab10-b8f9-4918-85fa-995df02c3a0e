package pt.jumia.services.bill.domain.usecases.documents.processors;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

@Component
@RequiredArgsConstructor
public class PopulateOriginalDocumentProcessor implements Processor {

    private final DocumentRepository documentRepository;

    @Override
    public void process(DocumentAggregate aggregate) {
        Document document = aggregate.getDocument();
        DocumentId originalDocument = document.getOriginalDocument();
        if (originalDocument != null && originalDocument.getId() == null) {
            DocumentId originalDocumentId = documentRepository
                    .findDocumentIdBySidInSuccessStatus(originalDocument.getSid())
                    .orElseThrow(() -> EntityNotFoundException.createNotFound(Document.class, "sid", originalDocument.getSid()));

            document.setOriginalDocument(originalDocumentId);
        }
    }
}

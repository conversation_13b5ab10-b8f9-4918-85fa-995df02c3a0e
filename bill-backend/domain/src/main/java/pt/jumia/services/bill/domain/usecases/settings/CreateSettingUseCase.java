package pt.jumia.services.bill.domain.usecases.settings;


import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.DataEventsNotificator;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

@Component
@AllArgsConstructor
public class CreateSettingUseCase {

    private final SettingRepository settingRepository;
    private final DataEventsNotificator dataEventsNotificator;

    public Setting execute(Setting setting) {

        if (setting == null) {
            // If programing error passed a null setting, throw IllegalArgumentException
            throw new IllegalArgumentException("Setting cannot be null.");
        }

        if (!setting.isTypeOverride() || setting.isOverrideKeyEmpty()) {
            throw new IllegalArgumentException("You can only create override settings.");
        }

        List<Setting> settingsWithProperty = settingRepository.findByProperty(setting.getProperty());
        if (settingsWithProperty.isEmpty()) {
            throw new IllegalArgumentException("You must have a default setting to make an override.");
        }

        setting = setting.toBuilder()
                .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                .createdBy(RequestContext.getUsername())
                .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
                .updatedBy(RequestContext.getUsername())
                .build();

        Setting createdSetting = settingRepository.insert(setting);
        dataEventsNotificator.notifySettingChanges();
        return createdSetting;
    }
}

package pt.jumia.services.bill.domain.usecases.metrics;

import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.Counter;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;

@Component
public class MonitoringUseCase {

    private static final String NAMESPACE = "bill:";
    private static final String CREATED_DOCUMENTS_REQUEST_COUNTER_NAME = "created_documents_total";
    private static final String CREATED_DOCUMENTS_REQUEST_DESCRIPTION = "Total created documents.";
    private static final String TAX_UPDATES_REQUEST_COUNTER_NAME = "tax_updates_documents_total";
    private static final String TAX_UPDATES_REQUEST_DESCRIPTION = "Total tax updates documents.";
    private static final String RETRIED_DOCUMENTS_COUNTER_NAME = "retried_documents_total";
    private static final String RETRIED_DOCUMENTS_REQUEST_DESCRIPTION = "Total retried documents.";
    private static final String COUNTRY_LABEL_KEY = "country";
    private static final String SHOP_LABEL_KEY = "shop";
    private static final String STATUS_LABEL_KEY = "status";
    private static final String DOCUMENT_TYPE_LABEL_KEY = "document_type";

    private final Counter createdDocumentsRequests;
    private final Counter taxUpdatesRequests;
    private final Counter retriedDocumentsRequests;

    public MonitoringUseCase(CollectorRegistry collectorRegistry) {

        this.createdDocumentsRequests = Counter.build()
                .name(NAMESPACE.concat(CREATED_DOCUMENTS_REQUEST_COUNTER_NAME))
                .help(CREATED_DOCUMENTS_REQUEST_DESCRIPTION)
                .labelNames(SHOP_LABEL_KEY, COUNTRY_LABEL_KEY, DOCUMENT_TYPE_LABEL_KEY)
                .register(collectorRegistry);

        this.taxUpdatesRequests = Counter.build()
                .name(NAMESPACE.concat(TAX_UPDATES_REQUEST_COUNTER_NAME))
                .help(TAX_UPDATES_REQUEST_DESCRIPTION)
                .labelNames(SHOP_LABEL_KEY, COUNTRY_LABEL_KEY, DOCUMENT_TYPE_LABEL_KEY, STATUS_LABEL_KEY)
                .register(collectorRegistry);

        this.retriedDocumentsRequests = Counter.build()
                .name(NAMESPACE.concat(RETRIED_DOCUMENTS_COUNTER_NAME))
                .help(RETRIED_DOCUMENTS_REQUEST_DESCRIPTION)
                .labelNames(SHOP_LABEL_KEY, COUNTRY_LABEL_KEY, DOCUMENT_TYPE_LABEL_KEY)
                .register(collectorRegistry);
    }

    public void recordCreatedDocumentsRequest(Document document) {
        this.createdDocumentsRequests.labels(
                document.getShop(),
                document.getCountry().getAlpha2(),
                document.getType().name()).inc();
    }

    public void recordTaxUpdatesRequest(Document document) {
        this.taxUpdatesRequests.labels(
                document.getShop(),
                document.getCountry().getAlpha2(),
                document.getType().name(),
                document.getStatus().name()).inc();
    }

    public void recordRetriedDocumentsRequest(Document document) {
        this.retriedDocumentsRequests.labels(
                document.getShop(),
                document.getCountry().getAlpha2(),
                document.getType().name()).inc();
    }
}

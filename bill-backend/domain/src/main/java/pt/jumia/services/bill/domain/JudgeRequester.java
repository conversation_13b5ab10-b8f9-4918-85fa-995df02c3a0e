package pt.jumia.services.bill.domain;

import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.GhDocPdfRequestPayloadInfo;
import pt.jumia.services.bill.domain.exceptions.judge.JudgeNetworkException;

public interface JudgeRequester {
    DocumentFile downloadDocumentBySid(String sid) throws JudgeNetworkException;

    String generateNewDocumentByCode(DocumentAggregate documentAggregate);

    String generateNewDocumentByCodeWithAllDocFields(DocumentAggregate documentAggregate, GhDocPdfRequestPayloadInfo docPdfFields);
}

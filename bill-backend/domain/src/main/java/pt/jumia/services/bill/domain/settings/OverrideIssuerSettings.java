package pt.jumia.services.bill.domain.settings;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;

@Data
public class OverrideIssuerSettings {

    public static final String ENABLED = "issuer-override-enabled";

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<Boolean> issuerOverrideEnabled;

    public OverrideIssuerSettings(OverallSettings overallSettings) {
        this.issuerOverrideEnabled = overallSettings.createSetting(
                ENABLED,
                false,
                Boolean::parseBoolean
        );
    }

    public boolean isEnabled(String shop, String countryCode) {
        return this.issuerOverrideEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

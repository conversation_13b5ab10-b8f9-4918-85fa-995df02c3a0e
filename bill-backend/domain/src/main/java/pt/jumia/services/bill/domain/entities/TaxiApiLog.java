package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
public class TaxiApiLog {
    private Long id;
    private String relatedEntityId;
    private String relatedEntityCode;
    private String type;
    private String shop;
    private String country;
    private String originalRequest;
    private String request;
    private String response;
    private Integer nrAttempts;
    private String status;
    private Integer statusCode;
    private Long originalApiLog;
    private Long rootApiLog;
    private String exception;
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;
    private LocalDateTime issuedDate;
    private String errorCode;
}

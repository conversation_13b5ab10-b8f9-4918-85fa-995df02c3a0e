package pt.jumia.services.bill.domain.usecases.notification;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Notification;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.util.List;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
public class ReadNotificationUseCase {

    private final SettingRepository settingRepository;

    private static final String PURCHASE_PORTAL_PROPERTY = "reporting.purchase_portal";
    public List<Notification> getPurchasePortal() {
        List<Setting> settings = settingRepository.findAll();
        return settings.stream()
                .filter(setting -> setting.getProperty().equals(PURCHASE_PORTAL_PROPERTY))
                .map(setting -> Notification.builder()
                        .email(setting.getValue())
                        .createdAt(setting.getCreatedAt())
                        .createdBy(setting.getCreatedBy())
                        .updatedAt(setting.getUpdatedAt())
                        .updatedBy(setting.getUpdatedBy())
                        .build())
                .collect(Collectors.toList());
    }
}

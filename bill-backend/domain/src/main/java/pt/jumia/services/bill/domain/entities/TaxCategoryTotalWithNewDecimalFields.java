package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TaxCategoryTotalWithNewDecimalFields extends TaxCategoryTotal {

    private BigDecimal taxRate_new;
    private BigDecimal taxFixedAmount_new;
    private BigDecimal totalAmount_new;
    private BigDecimal netAmount_new;
    private BigDecimal taxAmount_new;

    @Builder(builderMethodName = "newDecimalFieldsBuilder")
    public TaxCategoryTotalWithNewDecimalFields(TaxCategoryTotal taxCategoryTotal,
                                              BigDecimal taxRate_new,
                                              BigDecimal taxFixedAmount_new,
                                              BigDecimal totalAmount_new,
                                              BigDecimal netAmount_new,
                                              BigDecimal taxAmount_new) {
        super(taxCategoryTotal.toBuilder());
        this.taxRate_new = taxRate_new;
        this.taxFixedAmount_new = taxFixedAmount_new;
        this.totalAmount_new = totalAmount_new;
        this.netAmount_new = netAmount_new;
        this.taxAmount_new = taxAmount_new;
    }

    @Override
    public TaxCategoryTotal withoutDbField() {
        TaxCategoryTotal taxCategoryTotal = super.withoutDbField();
        return TaxCategoryTotalWithNewDecimalFields.newDecimalFieldsBuilder()
                .taxCategoryTotal(taxCategoryTotal)
                .taxRate_new(taxRate_new != null ? taxRate_new.setScale(10, RoundingMode.CEILING) : null)
                .taxFixedAmount_new(taxFixedAmount_new != null ? taxFixedAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .totalAmount_new(totalAmount_new != null ? totalAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .netAmount_new(netAmount_new != null ? netAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .taxAmount_new(taxAmount_new != null ? taxAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .build();
    }

    @Override
    public void convertAmountsToPositiveValue() {
        super.convertAmountsToPositiveValue();
        
        this.taxRate_new = this.taxRate_new != null && this.taxRate_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.taxRate_new.negate() : this.taxRate_new;
        this.taxFixedAmount_new = this.taxFixedAmount_new != null && this.taxFixedAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.taxFixedAmount_new.negate() : this.taxFixedAmount_new;
        this.totalAmount_new = this.totalAmount_new != null && this.totalAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalAmount_new.negate() : this.totalAmount_new;
        this.netAmount_new = this.netAmount_new != null && this.netAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.netAmount_new.negate() : this.netAmount_new;
        this.taxAmount_new = this.taxAmount_new != null && this.taxAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.taxAmount_new.negate() : this.taxAmount_new;
    }
}

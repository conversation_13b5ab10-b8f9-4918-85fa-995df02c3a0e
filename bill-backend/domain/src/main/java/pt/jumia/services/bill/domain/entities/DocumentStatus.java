package pt.jumia.services.bill.domain.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;

import javax.print.Doc;
import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
@Getter
public enum DocumentStatus {
    NEW(false),
    TAX_PENDING(false),
    TAX_SKIPPED(true),

    // Tax Errors -------------------------
    TAX_FAILED_REQUEST(false),
    TAX_FAILED_RESPONSE(false),
    TAX_SUBMITTED_INVALID(false),
    // End Tax Errors ----------------------
    TAXI_FAILED_MAPPING(false),
    TAXI_FAILED_FETCH_ITEM(false),
    TAXI_INTERNAL_ERROR(false),
    TAX_ERROR(false), // TOD<PERSON> remove this when complete the whole moving action from TaxI to Bill

    TAX_ERROR_RETRIED(false),
    TAX_ERROR_ACKED(false),
    TAX_SUCCESS(true),
    TAX_CANCELLED(true),
    TAX_REJECTED(true);
    private final boolean isSuccessful;

    private static final Set<String> ALLOWED_COUNTRIES_FOR_NEW_TO_SUCCESS_TRANSITION = Set.of(
            CountryCode.GH.name()
    );

    private static final Set<String> ALLOWED_COUNTRIES_FOR_SUCCESS_TO_ERROR_TRANSITION = Set.of(
            CountryCode.GH.name()
    );


    private List<DocumentStatus> getNextPossibleStatuses() {
        switch (this) {
            case NEW:
                return List.of(TAX_PENDING);
            case TAXI_FAILED_MAPPING:
            case TAXI_FAILED_FETCH_ITEM:
            case TAX_FAILED_REQUEST:
                return List.of(
                        TAX_FAILED_RESPONSE,
                        TAXI_FAILED_FETCH_ITEM,
                        TAX_PENDING,
                        TAX_SUCCESS,
                        TAX_ERROR_ACKED
                );
            case TAX_REJECTED:
            case TAX_FAILED_RESPONSE:
            case TAX_SUBMITTED_INVALID:
                return List.of(
                        TAX_PENDING,
                        TAX_SUCCESS,
                        TAX_ERROR_ACKED
                );
            case TAX_PENDING:
                return List.of(
                        TAX_SKIPPED,
                        TAX_SUCCESS,
                        TAXI_FAILED_MAPPING,
                        TAXI_FAILED_FETCH_ITEM,
                        TAX_FAILED_REQUEST,
                        TAX_FAILED_RESPONSE,
                        TAX_SUBMITTED_INVALID,
                        TAX_REJECTED
                );
            case TAX_SUCCESS:
                return List.of(TAX_CANCELLED, TAX_REJECTED);
            case TAX_ERROR_ACKED:
                return List.of(TAX_ERROR_RETRIED);
            case TAX_SKIPPED:
            case TAX_ERROR_RETRIED:
                return List.of();
            case TAX_CANCELLED:
                return List.of(TAX_SUCCESS);
            case TAX_ERROR:
                return List.of(
                        TAX_PENDING,
                        TAXI_INTERNAL_ERROR,
                        TAX_ERROR_RETRIED,
                        TAXI_FAILED_MAPPING,
                        TAXI_FAILED_FETCH_ITEM,
                        TAX_FAILED_REQUEST,
                        TAX_FAILED_RESPONSE,
                        TAX_SUBMITTED_INVALID,
                        TAX_ERROR_ACKED
                );
            case TAXI_INTERNAL_ERROR:
                return List.of(
                        TAX_SUCCESS,
                        TAX_PENDING,
                        TAX_SKIPPED,
                        TAXI_INTERNAL_ERROR,
                        TAXI_FAILED_MAPPING,
                        TAXI_FAILED_FETCH_ITEM,
                        TAX_FAILED_REQUEST,
                        TAX_FAILED_RESPONSE,
                        TAX_SUBMITTED_INVALID
                );
            default:
                throw ConflictOperationException.createInvalidDocumentStatus(this);
        }
    }

    public boolean canTransitionTo(DocumentStatus nextStatus, CountryCode country) {

        if (this.isNewToSuccessTransition(nextStatus, country.name())) {
            return true;
        }

        if (this.isNewToFailedRequestTransition(nextStatus, country.name())) {
            return true;
        }

        if (this.isSuccessToFailedResponseTransition(nextStatus, country.name())){
            return true;
        }

        return this.getNextPossibleStatuses().contains(nextStatus);
    }

    public boolean isNewToSuccessTransition(DocumentStatus nextStatus, String countryName) {
        return this == DocumentStatus.NEW
                && nextStatus == DocumentStatus.TAX_SUCCESS
                && ALLOWED_COUNTRIES_FOR_NEW_TO_SUCCESS_TRANSITION.contains(countryName);
    }

    public boolean isNewToFailedRequestTransition(DocumentStatus nextStatus,  String countryName) {
        return this == DocumentStatus.NEW
                && nextStatus == DocumentStatus.TAXI_INTERNAL_ERROR
                && ALLOWED_COUNTRIES_FOR_NEW_TO_SUCCESS_TRANSITION.contains(countryName);
    }

    public boolean isSuccessToFailedResponseTransition(DocumentStatus nextStatus, String countryName){
        return this == DocumentStatus.TAX_SUCCESS
                && nextStatus == DocumentStatus.TAX_FAILED_RESPONSE
                && ALLOWED_COUNTRIES_FOR_SUCCESS_TO_ERROR_TRANSITION.contains(countryName);
    }

    public static List<DocumentStatus> getErrorStatuses() {
        return List.of(
                TAX_ERROR,
                TAXI_INTERNAL_ERROR,
                TAX_SUBMITTED_INVALID,
                TAXI_FAILED_MAPPING,
                TAXI_FAILED_FETCH_ITEM,
                TAX_FAILED_REQUEST,
                TAX_FAILED_RESPONSE
        );
    }

    public static List<DocumentStatus> getRetryStatuses() {
        return List.of(
                TAX_ERROR,
                TAXI_INTERNAL_ERROR,
                TAX_SUBMITTED_INVALID,
                TAXI_FAILED_MAPPING,
                TAX_FAILED_REQUEST,
                TAX_FAILED_RESPONSE,
                TAXI_FAILED_FETCH_ITEM,
                TAX_REJECTED
        );
    }

    public static List<DocumentStatus> getTaxiError() {
        return List.of(
                TAXI_INTERNAL_ERROR
        );
    }

    public static List<DocumentStatus> getTaxError() {
        return List.of(
                TAX_ERROR,
                TAX_SUBMITTED_INVALID,
                TAXI_FAILED_MAPPING,
                TAXI_FAILED_FETCH_ITEM,
                TAX_FAILED_REQUEST,
                TAX_FAILED_RESPONSE,
                TAX_REJECTED
        );
    }

    public static List<DocumentStatus> getReceivedDocumentsStatusForDailyEmail() {
        return List.of(TAX_SUCCESS, TAX_REJECTED, TAX_CANCELLED);
    }

    public static List<DocumentStatus> getResendStatuses() {
        return List.of(
                TAXI_INTERNAL_ERROR,
                TAX_PENDING
        );
    }
}

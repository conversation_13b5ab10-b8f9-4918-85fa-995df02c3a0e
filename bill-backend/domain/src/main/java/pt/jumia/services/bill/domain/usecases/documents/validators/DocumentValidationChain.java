package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class DocumentValidationChain {

    private final List<Validator> preSplitValidators;
    private final List<Validator> postSplitValidators;

    public DocumentValidationChain(
            AtLeastOneLineValidator atLeastOneLineValidator,
            LineCountValidator lineCountValidator,
            OriginalDocumentExistsValidator originalDocumentExistsValidator,
            DiffDocumentPayloadValidator diffDocumentPayloadValidator,
            DocumentDuplicateValidator documentDuplicateValidator,
            TaxCategoryTotalsUniqueValidator taxCategoryTotalsUniqueValidator,
            TaxFieldsValidation taxFieldsValidation,
            DocumentLineAppliedTaxFieldsValidation documentLineAppliedTaxFieldsValidation,
            CurrencyExchangeRateValidator currencyExchangeRateValidator,
            LinesPositionsValidator linesPositionsValidator,
            DocumentIssuedReasonValidator documentIssuedReasonValidator,
            KeDocumentValidator keDocumentValidator
    ) {

        this.preSplitValidators = List.of(
                atLeastOneLineValidator,
                lineCountValidator,
                taxCategoryTotalsUniqueValidator,
                taxFieldsValidation,
                documentLineAppliedTaxFieldsValidation,
                currencyExchangeRateValidator,
                keDocumentValidator,
                linesPositionsValidator);

        this.postSplitValidators = List.of(
                originalDocumentExistsValidator,
                diffDocumentPayloadValidator,
                documentDuplicateValidator,
                documentIssuedReasonValidator);

        // Ignore these validations until we check every single calculation.
        // also, Egypt local authority's will accept all invoices even they are invalid on calculation level.
        // discountEqualDiscountLinesValidator,
        // summaryEqualLinesValidator,
        // linesValuesValidator,
        // taxSummaryValidation,
        // lineTaxCategoryValidation
    }

    public List<ValidationError> runAllValidations(DocumentAggregate aggregate) {
        List<ValidationError> errors = new ArrayList<>(this.runPreSplitValidations(aggregate));
        errors.addAll(this.runPostSplitValidations(aggregate));

        return errors;
    }

    public List<ValidationError> runPreSplitValidations(DocumentAggregate aggregate) {
        return preSplitValidators.stream()
                .flatMap(validator -> validator.validate(aggregate).stream())
                .collect(Collectors.toList());
    }

    public List<ValidationError> runPostSplitValidations(DocumentAggregate aggregate) {
        return postSplitValidators.stream()
                .flatMap(validator -> validator.validate(aggregate).stream())
                .collect(Collectors.toList());
    }
}

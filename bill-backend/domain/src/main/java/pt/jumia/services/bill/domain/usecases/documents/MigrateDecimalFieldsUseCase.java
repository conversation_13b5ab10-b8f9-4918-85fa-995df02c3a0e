package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.repository.DocumentLineRepository;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.TaxCategoryTotalRepository;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class MigrateDecimalFieldsUseCase {

    private final DocumentRepository documentRepository;
    private final DocumentLineRepository documentLineRepository;
    private final TaxCategoryTotalRepository taxCategoryTotalRepository;
    private final EntityManager entityManager;

    private static final int BATCH_SIZE = 1000;

    @Transactional
    public Map<String, Integer> migrateDocuments(Long startId, Long endId) {
        Map<String, Integer> result = new HashMap<>();
        
        // Migrate documents
        String documentQuery = "UPDATE documents SET " +
                "total_amount_new = total_amount, " +
                "net_amount_new = net_amount, " +
                "tax_amount_new = tax_amount, " +
                "discount_amount_new = discount_amount, " +
                "total_items_discount_amount_new = total_items_discount_amount, " +
                "extra_discount_amount_new = extra_discount_amount, " +
                "issued_to_local_currency_exchange_rate_new = issued_to_local_currency_exchange_rate " +
                "WHERE id BETWEEN :startId AND :endId";
        
        Query query = entityManager.createNativeQuery(documentQuery);
        query.setParameter("startId", startId);
        query.setParameter("endId", endId);
        int documentsUpdated = query.executeUpdate();
        result.put("documents", documentsUpdated);
        
        // Migrate document lines
        String documentLinesQuery = "UPDATE document_lines SET " +
                "quantity_new = quantity, " +
                "unit_price_new = unit_price, " +
                "total_amount_new = total_amount, " +
                "net_amount_new = net_amount, " +
                "total_tax_amount_new = total_tax_amount, " +
                "discount_amount_new = discount_amount, " +
                "discount_rate_new = discount_rate " +
                "WHERE fk_document IN (SELECT id FROM documents WHERE id BETWEEN :startId AND :endId)";
        
        query = entityManager.createNativeQuery(documentLinesQuery);
        query.setParameter("startId", startId);
        query.setParameter("endId", endId);
        int documentLinesUpdated = query.executeUpdate();
        result.put("document_lines", documentLinesUpdated);
        
        // Migrate tax category totals
        String taxCategoryTotalsQuery = "UPDATE tax_category_totals SET " +
                "tax_rate_new = tax_rate, " +
                "tax_fixed_amount_new = tax_fixed_amount, " +
                "total_amount_new = total_amount, " +
                "net_amount_new = net_amount, " +
                "tax_amount_new = tax_amount " +
                "WHERE fk_document IN (SELECT id FROM documents WHERE id BETWEEN :startId AND :endId)";
        
        query = entityManager.createNativeQuery(taxCategoryTotalsQuery);
        query.setParameter("startId", startId);
        query.setParameter("endId", endId);
        int taxCategoryTotalsUpdated = query.executeUpdate();
        result.put("tax_category_totals", taxCategoryTotalsUpdated);
        
        // Migrate documents_aud
        String documentsAudQuery = "UPDATE documents_aud SET " +
                "total_amount_new = total_amount, " +
                "net_amount_new = net_amount, " +
                "tax_amount_new = tax_amount, " +
                "discount_amount_new = discount_amount, " +
                "total_items_discount_amount_new = total_items_discount_amount, " +
                "extra_discount_amount_new = extra_discount_amount, " +
                "issued_to_local_currency_exchange_rate_new = issued_to_local_currency_exchange_rate " +
                "WHERE id IN (SELECT id FROM documents WHERE id BETWEEN :startId AND :endId)";
        
        query = entityManager.createNativeQuery(documentsAudQuery);
        query.setParameter("startId", startId);
        query.setParameter("endId", endId);
        int documentsAudUpdated = query.executeUpdate();
        result.put("documents_aud", documentsAudUpdated);
        
        return result;
    }

    @Transactional(readOnly = true)
    public Map<String, Object> getMigrationStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // Check documents migration status
        String documentsStatusQuery = "SELECT " +
                "COUNT(*) as total, " +
                "SUM(CASE WHEN total_amount_new = 0 THEN 0 ELSE 1 END) as migrated " +
                "FROM documents";
        
        Query query = entityManager.createNativeQuery(documentsStatusQuery);
        Object[] documentsResult = (Object[]) query.getSingleResult();
        Map<String, Object> documentsStatus = new HashMap<>();
        documentsStatus.put("total", documentsResult[0]);
        documentsStatus.put("migrated", documentsResult[1]);
        documentsStatus.put("percentage", calculatePercentage((Long) documentsResult[1], (Long) documentsResult[0]));
        status.put("documents", documentsStatus);
        
        // Check document lines migration status
        String documentLinesStatusQuery = "SELECT " +
                "COUNT(*) as total, " +
                "SUM(CASE WHEN quantity_new = 0 THEN 0 ELSE 1 END) as migrated " +
                "FROM document_lines";
        
        query = entityManager.createNativeQuery(documentLinesStatusQuery);
        Object[] documentLinesResult = (Object[]) query.getSingleResult();
        Map<String, Object> documentLinesStatus = new HashMap<>();
        documentLinesStatus.put("total", documentLinesResult[0]);
        documentLinesStatus.put("migrated", documentLinesResult[1]);
        documentLinesStatus.put("percentage", calculatePercentage((Long) documentLinesResult[1], (Long) documentLinesResult[0]));
        status.put("document_lines", documentLinesStatus);
        
        // Check tax category totals migration status
        String taxCategoryTotalsStatusQuery = "SELECT " +
                "COUNT(*) as total, " +
                "SUM(CASE WHEN total_amount_new = 0 THEN 0 ELSE 1 END) as migrated " +
                "FROM tax_category_totals";
        
        query = entityManager.createNativeQuery(taxCategoryTotalsStatusQuery);
        Object[] taxCategoryTotalsResult = (Object[]) query.getSingleResult();
        Map<String, Object> taxCategoryTotalsStatus = new HashMap<>();
        taxCategoryTotalsStatus.put("total", taxCategoryTotalsResult[0]);
        taxCategoryTotalsStatus.put("migrated", taxCategoryTotalsResult[1]);
        taxCategoryTotalsStatus.put("percentage", calculatePercentage((Long) taxCategoryTotalsResult[1], (Long) taxCategoryTotalsResult[0]));
        status.put("tax_category_totals", taxCategoryTotalsStatus);
        
        return status;
    }
    
    private double calculatePercentage(Long migrated, Long total) {
        if (total == 0) {
            return 0.0;
        }
        return (double) migrated / total * 100.0;
    }
}

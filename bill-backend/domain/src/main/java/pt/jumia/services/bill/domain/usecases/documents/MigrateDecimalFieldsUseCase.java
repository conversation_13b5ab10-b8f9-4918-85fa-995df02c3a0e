package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import pt.jumia.services.bill.domain.repository.DocumentLineRepository;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.TaxCategoryTotalRepository;
import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class MigrateDecimalFieldsUseCase {

    private final DocumentRepository documentRepository;
    private final DocumentLineRepository documentLineRepository;
    private final TaxCategoryTotalRepository taxCategoryTotalRepository;

    @Transactional
    public Map<String, Integer> migrateDocuments(Long startId, Long endId) {
        Map<String, Integer> result = new HashMap<>();

        // Migrate documents
        int documentsUpdated = documentRepository.migrateDecimalFields(startId, endId);
        result.put("documents", documentsUpdated);

        // Migrate document lines
        int documentLinesUpdated = documentLineRepository.migrateDecimalFields(startId, endId);
        result.put("document_lines", documentLinesUpdated);

        // Migrate tax category totals
        int taxCategoryTotalsUpdated = taxCategoryTotalRepository.migrateDecimalFields(startId, endId);
        result.put("tax_category_totals", taxCategoryTotalsUpdated);

        // Migrate documents_aud
        int documentsAudUpdated = documentRepository.migrateAuditDecimalFields(startId, endId);
        result.put("documents_aud", documentsAudUpdated);

        return result;
    }

    @Transactional(readOnly = true)
    public Map<String, Object> getMigrationStatus() {
        Map<String, Object> status = new HashMap<>();

        // Get migration status from repositories
        status.put("documents", documentRepository.getDecimalFieldsMigrationStatus());
        status.put("document_lines", documentLineRepository.getDecimalFieldsMigrationStatus());
        status.put("tax_category_totals", taxCategoryTotalRepository.getDecimalFieldsMigrationStatus());

        return status;
    }
}

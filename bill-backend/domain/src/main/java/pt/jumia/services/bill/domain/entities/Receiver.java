package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Value;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.UUID;

@Value
@Builder(toBuilder = true)
public class Receiver {

    UUID id;
    ReceiverType type;

    //
    // Identification
    //

    String legalName;
    String name;
    String nationalIdentificationNumber;
    String taxIdentificationNumber;
    String businessRegistrationNumber;

    //
    // Contact
    //

    Address address;
    String email;
    String mobilePhone;
    String linePhone;

    //
    // Audit information
    //

    LocalDateTime createdAt;
    String createdBy;

    public Receiver withoutDbField() {
        return this.toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public static class ReceiverBuilder {

        public ReceiverBuilder generateId() {
            id = UUID.nameUUIDFromBytes(this.generateStringId().getBytes(StandardCharsets.UTF_8));
            return this;
        }

        private String generateStringId() {
            return "Receiver{" +
                    "type=" + type +
                    ", legalName='" + legalName + '\'' +
                    ", name='" + name + '\'' +
                    ", nationalIdentificationNumber='" + nationalIdentificationNumber + '\'' +
                    ", taxIdentificationNumber='" + taxIdentificationNumber + '\'' +
                    ", businessRegistrationNumber='" + businessRegistrationNumber + '\'' +
                    ", address=" + (address != null ? address.generateStringId() : null) +
                    ", email='" + email + '\'' +
                    ", mobilePhone='" + mobilePhone + '\'' +
                    ", linePhone='" + linePhone + '\'' +
                    '}';
        }

    }
}

package pt.jumia.services.bill.domain.repository;

import pt.jumia.services.bill.domain.entities.DocumentTransformation;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface DocumentTransformationRepository {
    Optional<DocumentTransformation> findById(long id);

    List<DocumentTransformation> findByDocumentId(UUID documentId);

    DocumentTransformation insert(DocumentTransformation documentTransformation);

    DocumentTransformation update(Long id, DocumentTransformation documentTransformation);

    Optional<DocumentTransformation> findByDocumentIdAndTypeAndOriginalValue(UUID documentId,
                                                                             DocumentTransformation.EntityType type,
                                                                             String originalValue);
}

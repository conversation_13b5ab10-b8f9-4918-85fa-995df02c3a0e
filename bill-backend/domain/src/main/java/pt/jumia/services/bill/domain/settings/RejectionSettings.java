package pt.jumia.services.bill.domain.settings;

import lombok.Data;

@Data
public class RejectionSettings {

    public static final String INVOICE_REJECTION_DECLINE_ENABLED = "documents.sales_invoice.rejection_decline_enabled";
    public static final String CREDIT_NOTE_REJECTION_DECLINE_ENABLED = "documents.sales_credit_note.rejection_decline_enabled";
    public static final String CREDIT_MEMO_REJECTION_DECLINE_ENABLED = "documents.sales_credit_memo.rejection_decline_enabled";
    public static final String DEBIT_NOTE_REJECTION_DECLINE_ENABLED = "documents.sales_debit_note.rejection_decline_enabled";
    public static final String PURCHASE_INVOICE_REJECTION_DECLINE_ENABLED = "documents.purchase_invoice.rejection_decline_enabled";
    public static final String PURCHASE_CREDIT_NOTE_REJECTION_DECLINE_ENABLED = "documents.purchase_credit_note.rejection_decline_enabled";
    public static final String PURCHASE_DEBIT_NOTE_REJECTION_DECLINE_ENABLED = "documents.purchase_debit_note.rejection_decline_enabled";

    private final OverallSettings.OverridableSetting<Boolean> invoiceRejectionDeclineEnabled;
    private final OverallSettings.OverridableSetting<Boolean> creditNoteRejectionDeclineEnabled;
    private final OverallSettings.OverridableSetting<Boolean> creditMemoRejectionDeclineEnabled;
    private final OverallSettings.OverridableSetting<Boolean> debitNoteRejectionDeclineEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseInvoiceRejectionDeclineEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseCreditNoteRejectionDeclineEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseDebitNoteRejectionDeclineEnabled;

    public RejectionSettings(OverallSettings overallSettings) {

        this.invoiceRejectionDeclineEnabled = overallSettings.createSetting(
                INVOICE_REJECTION_DECLINE_ENABLED,
                true,
                Boolean::parseBoolean);

        this.creditNoteRejectionDeclineEnabled = overallSettings.createSetting(
                CREDIT_NOTE_REJECTION_DECLINE_ENABLED,
                true,
                Boolean::parseBoolean);

        this.creditMemoRejectionDeclineEnabled = overallSettings.createSetting(
                CREDIT_MEMO_REJECTION_DECLINE_ENABLED,
                true,
                Boolean::parseBoolean);

        this.debitNoteRejectionDeclineEnabled = overallSettings.createSetting(
                DEBIT_NOTE_REJECTION_DECLINE_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseInvoiceRejectionDeclineEnabled = overallSettings.createSetting(
                PURCHASE_INVOICE_REJECTION_DECLINE_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseCreditNoteRejectionDeclineEnabled = overallSettings.createSetting(
                PURCHASE_CREDIT_NOTE_REJECTION_DECLINE_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseDebitNoteRejectionDeclineEnabled = overallSettings.createSetting(
                PURCHASE_DEBIT_NOTE_REJECTION_DECLINE_ENABLED,
                true,
                Boolean::parseBoolean);
    }

    public boolean getInvoiceRejectionDeclineEnabled(String shop, String countryCode) {
        return this.invoiceRejectionDeclineEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getCreditNoteRejectionDeclineEnabled(String shop, String countryCode) {
        return this.creditNoteRejectionDeclineEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getCreditMemoRejectionDeclineEnabled(String shop, String countryCode) {
        return this.creditMemoRejectionDeclineEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getDebitNoteRejectionDeclineEnabled(String shop, String countryCode) {
        return this.debitNoteRejectionDeclineEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseInvoiceRejectionDeclineEnabled(String shop, String countryCode) {
        return this.purchaseInvoiceRejectionDeclineEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseCreditNoteRejectionDeclineEnabled(String shop, String countryCode) {
        return this.purchaseCreditNoteRejectionDeclineEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseDebitNoteRejectionDeclineEnabled(String shop, String countryCode) {
        return this.purchaseDebitNoteRejectionDeclineEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

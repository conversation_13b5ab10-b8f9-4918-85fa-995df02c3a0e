package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class DocumentApiLog {
    private Long id;
    private String documentSid;
    private String originalRequest;
    private LocalDateTime issuedDate;
    private List<UUID> resultingDocumentsIds;
    private DocumentApiLogStatus status;
    private String errors;

    // Audit information
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    public enum DocumentApiLogStatus {
        SAVED,
        FAILED_UCR_MAPPING,
        FAILED_SPLITTING,
        FAILED_ACKNOWLEDGE,
        FAILED_VALIDATION_CHAIN
    }

    public DocumentApiLog withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdBy(null)
                .createdAt(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }
}

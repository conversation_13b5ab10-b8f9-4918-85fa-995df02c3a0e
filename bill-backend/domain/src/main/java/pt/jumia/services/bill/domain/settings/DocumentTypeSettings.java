package pt.jumia.services.bill.domain.settings;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import pt.jumia.services.bill.domain.entities.DocumentType;

@Data
public class DocumentTypeSettings {
    public static final String NEGATIVE_DOCUMENT_TYPE = "documents-types.negative-document-type";

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<DocumentType> negativeDocumentType;

    public DocumentTypeSettings(OverallSettings overallSettings) {
        this.negativeDocumentType = overallSettings.createSetting(
                NEGATIVE_DOCUMENT_TYPE,
                DocumentType.SALES_CREDIT_NOTE,
                DocumentType::valueOf
        );
    }

    public DocumentType getNegativeDocumentType(String shop, String countryCode) {
        return this.negativeDocumentType.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class DocumentIssuedReasonValidator implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        List<ValidationError> errors = new ArrayList<>();

        IssuedReason issuedReason = aggregate.getDocument().getIssuedReason();
        if (aggregate.getDocument().getType().isIssuedReasonRequired() && (issuedReason == null || issuedReason.getCode() == null)) {
            errors.add(new ValidationError(
                    ErrorCode.ISSUED_REASON_REQUIRED,
                    String.format("Issued reason is required for document type '%s'", aggregate.getDocument().getType())
            ));
        }

        if (issuedReason != null) {
            IssuedReasonCode code = issuedReason.getCode();

            if (!code.supportedByDocumentType(aggregate.getDocument().getType())) {
                errors.add(new ValidationError(
                        ErrorCode.INVALID_ISSUED_REASON,
                        String.format("Issued reason not compatible with document type '%s'", aggregate.getDocument().getType())
                ));
            }

            if (code.isNotesRequired() && StringUtils.isBlank(issuedReason.getNotes())) {
                errors.add(new ValidationError(
                        ErrorCode.ISSUED_REASON_NOTES_REQUIRED,
                        String.format("Issued reason notes required for the specified issued reason '%s'",
                                code)
                ));
            }
        }

        return errors;
    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.DocumentFile;

@Component
@RequiredArgsConstructor
public class DownloadDocumentFromTaxAuthoritiesUseCase {
    private final TaxiRequester taxiRequester;

    public DocumentFile execute(String id) {
        return taxiRequester.getTaxAuthoritiesPdf(id);
    }
}

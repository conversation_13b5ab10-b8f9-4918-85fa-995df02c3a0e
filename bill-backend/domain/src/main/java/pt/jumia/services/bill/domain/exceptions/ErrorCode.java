package pt.jumia.services.bill.domain.exceptions;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ErrorCode {

    // 1xxx Server errors in general
    INTERNAL_ERROR(1000),

    // 2xxx ACL errors
    FORBIDDEN(2000),
    ACL_ERROR(2100),

    // 3xxx client errors
    // 30xx general client errors
    ENTITY_NOT_FOUND(3000),
    INVALID_PAYLOAD(3001),
    MISSING_DOCUMENT_PDF(3002),
    DUPLICATE_ENTRY(3003),
    INVALID_DOCUMENT_STATUS_ACK(3004),
    DATA_INTEGRITY_VIOLATION(3005),
    INVALID_SETTING(3006),
    ILLEGAL_ARGUMENT(3007),
    INVALID_DOCUMENT_STATUS(3008),
    INVALID_DOCUMENT_STATUS_CANCEL(3009),
    SETTING_NOT_ACTIVE(3010),
    INVALID_DECLINE_DOCUMENT_REJECTION_CANCEL(3011),
    // 31xx document validation errors
    INVALID_DOCUMENT(3100),
    MISSING_REQUIRED_FIELD(3101),
    INCORRECT_LINE_COUNT(3102),
    INVALID_ORIGINAL_DOCUMENT(3103),
    DUPLICATE_DOCUMENT(3104),
    INVALID_DOCUMENT_STATUS_TRANSITION(3105),
    ISSUED_REASON_REQUIRED(3106),
    INVALID_ISSUED_REASON(3107),
    ISSUED_REASON_NOTES_REQUIRED(3108),
    DISCOUNT_NOT_EQUAL_DISCOUNT_LINES(3109),
    NET_AMOUNT_NOT_EQUAL_NET_AMOUNT_LINES(3110),
    TOTAL_AMOUNT_NOT_EQUAL_TOTAL_AMOUNT_LINES(3111),
    TAX_AMOUNT_NOT_EQUAL_TAX_AMOUNT_LINES(3112),
    TOTAL_AMOUNT_NOT_VALID_LINE(3113),
    INVALID_LINE_QUANTITY(3114),
    NET_AMOUNT_NOT_VALID_LINE(3115),
    TAX_SUMMARY_NOT_VALID(3116),
    INVALID_TAX_CATEGORY(3117),
    INVALID_LINE_APPLIED_TAX(3118),
    INVALID_TAX_CATEGORY_TOTALS(3119),
    INVALID_CURRENCY_EXCHANGE_RATE(3120),
    DUPLICATE_DOCUMENT_PAYLOAD(3121),
    INCORRECT_LINE_POSITION_VALUE(3122),
    AMOUNTS_AFTER_SPLITTING_NOT_EQUAL_TO_ORIGINAL_AMOUNTS(3123),
    INVALID_ORIGINAL_DOCUMENT_AFTER_SPLITTING(3124),
    DOCUMENT_STATUS_NOT_VALID_FOR_PRINTING(3125),
    INVALID_DOCUMENT_TRANSACTION_TYPE(3126),
    INVALID_DOCUMENT_TRANSACTION_PROGRESS(3127),
    INVALID_LINE_PACKAGE(3128),
    INVALID_DOCUMENT_CODE(3129),

    // 4xxx external communications errors
    // 40xx taxi errors
    TAXI_REQUEST_FAILED(4000),

    // 41xx Judge errors
    JUDGE_REQUEST_FAILED(4100),

    // 42xx Judge errors
    COMMUNICATIONS_REQUEST_FAILED(4200),

    RECEIVER_TYPE(5000),
    RECEIVER_TYPE_NOT_IMPLEMENT(5001),

    //6xxx Errors During CSV exporting
    CREATE_CSV_FILE(6000),
    CREATE_CSV_FILE_DATA_EMPTY(6001),

    // 7xxx Afroms errors
    AFROMS_REQUEST_FAILED(7000),
    AFROMS_MISSING_CONFIGURATION(7001),
    AFROMS_PRODUCT_NOT_FOUND(7002),

    //8xxx Statement Errors
    CREATE_STATEMENT(8000);

    private final int code;
}

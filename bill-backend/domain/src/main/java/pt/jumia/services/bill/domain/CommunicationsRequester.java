package pt.jumia.services.bill.domain;

import pt.jumia.services.bill.domain.entities.DailyIssuedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DailyReceivedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;

public interface CommunicationsRequester {

    void sendEmailReceivedDocument(DocumentAggregate document);

    void sendEmailReceivedDocument(DocumentAggregate documentAggregate, String email);

    void sendEmailByIssuedDocumentsReportToReceiver(DailyIssuedDocumentsReport report, String email);

    void sendEmailByReceivedDocumentsReportToReceiver(DailyReceivedDocumentsReport report, String email);

}

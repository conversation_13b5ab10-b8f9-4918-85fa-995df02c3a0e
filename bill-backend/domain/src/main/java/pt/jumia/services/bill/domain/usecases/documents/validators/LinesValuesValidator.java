package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class LinesValuesValidator implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        List<ValidationError> errors = new ArrayList<>();

        aggregate.getLines().forEach((documentLine -> {

            if (!documentLine.getQuantity().equals(BigDecimal.ZERO)) {
                BigDecimal netAmount = documentLine.getUnitPrice().multiply(documentLine.getQuantity())
                        .setScale(6, RoundingMode.HALF_UP);

                if (!documentLine.getNetAmount().setScale(6, RoundingMode.HALF_UP).equals(netAmount)) {
                    errors.add(new ValidationError(
                            ErrorCode.NET_AMOUNT_NOT_VALID_LINE,
                            String.format("Net amount is not equal the total item price in document line: %s",
                                    documentLine.getItemCode())
                    ));
                }
            }

            BigDecimal totalLineAmount;
            if (documentLine.getDiscount() != null) {
                totalLineAmount = documentLine.getNetAmount()
                        .add(documentLine.getTotalTaxAmount())
                        .subtract(documentLine.getDiscount().getAmount())
                        .setScale(6, RoundingMode.HALF_UP);
            } else {
                totalLineAmount = documentLine.getNetAmount()
                        .add(documentLine.getTotalTaxAmount())
                        .setScale(6, RoundingMode.HALF_UP);
            }

            if (!documentLine.getTotalAmount().setScale(6, RoundingMode.HALF_UP).equals(totalLineAmount)) {
                errors.add(new ValidationError(
                        ErrorCode.TOTAL_AMOUNT_NOT_VALID_LINE,
                        String.format("Total amount is not equal the sum of net and tax amounts in document line: %s",
                                documentLine.getItemCode())
                ));
            }
        }));

        return errors;
    }
}

package pt.jumia.services.bill.domain.usecases.settings;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.DataEventsNotificator;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;
import java.util.Optional;

@Component
@AllArgsConstructor
public class UpdateSettingUseCase {

    private final SettingRepository settingRepository;
    private final DataEventsNotificator dataEventsNotificator;

    public Setting execute(long id, Setting settingToUpdate) {

        Optional<Setting> optionalSetting = settingRepository.findById(id);
        if (optionalSetting.isEmpty()) {
            throw EntityNotFoundException.createNotFound(Setting.class, id);
        }

        Setting settingWithUpdatedApplied = validateSettingFieldsAndGetUpdates(optionalSetting.get(), settingToUpdate)
                .toBuilder()
                .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
                .updatedBy(RequestContext.getUsername())
                .build();

        Setting updatedSetting = settingRepository.update(id, settingWithUpdatedApplied);
        dataEventsNotificator.notifySettingChanges();
        return updatedSetting;
    }

    private Setting validateSettingFieldsAndGetUpdates(Setting settingRead, Setting settingUpdates) {
        if (settingRead.isTypeOverride()) {
            return validateOverrideSettingFieldsAndGetUpdates(settingRead, settingUpdates);
        } else {
            return validateDefaultSettingFieldsAndGetUpdates(settingRead, settingUpdates);
        }
    }

    private Setting validateDefaultSettingFieldsAndGetUpdates(Setting settingRead, Setting settingUpdates) {
        Setting settingReadToCompare = settingRead.withoutDbFields().toBuilder()
                .value(null).description(null).build();
        Setting settingUpdatesToCompare = settingUpdates.withoutDbFields().toBuilder()
                .value(null).description(null).build();

        if (!settingReadToCompare.equals(settingUpdatesToCompare)) {
            throw new IllegalArgumentException("You can only update the value and description of a default setting.");
        }

        if (Objects.isNull(settingUpdates.getValue())) {
            throw new IllegalArgumentException("Setting values cannot be null.");
        }

        return settingRead.toBuilder()
                .value(settingUpdates.getValue())
                .description(settingUpdates.getDescription())
                .build();
    }

    private Setting validateOverrideSettingFieldsAndGetUpdates(Setting settingRead, Setting settingUpdates) {
        Setting settingReadToCompare = settingRead.withoutDbFields().toBuilder()
                .overrideKey(null).value(null).description(null).build();
        Setting settingUpdatesToCompare = settingUpdates.withoutDbFields().toBuilder()
                .overrideKey(null).value(null).description(null).build();

        if (!settingReadToCompare.equals(settingUpdatesToCompare)) {
            throw new IllegalArgumentException(
                    "You can only update the value, description and overrideKey of a override setting.");
        }

        if (Objects.isNull(settingUpdates.getValue())) {
            throw new IllegalArgumentException("Setting values cannot be null.");
        }

        if (settingUpdates.isOverrideKeyEmpty()) {
            throw new IllegalArgumentException("Override setting key cannot be empty.");
        }

        return settingRead.toBuilder()
                .overrideKey(settingUpdates.getOverrideKey())
                .value(settingUpdates.getValue())
                .description(settingUpdates.getDescription())
                .build();
    }
}

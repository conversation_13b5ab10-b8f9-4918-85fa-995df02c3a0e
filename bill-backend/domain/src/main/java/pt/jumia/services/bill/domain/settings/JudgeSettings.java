package pt.jumia.services.bill.domain.settings;


import lombok.Data;

@Data
public class JudgeSettings {

    public static final String INVOICE_CODES = "judge.invoice_codes";
    public static final String CREDIT_NOTE_CODES = "judge.credit_note_codes";
    public static final String CREDIT_MEMOS = "judge.credit_memos";

    public static final String AUTOMATIC_PDF_GENERATION_ENABLED = "judge.automatic_pdf_generation.enabled";

    private final OverallSettings.OverridableSetting<String> invoiceCodes;
    private final OverallSettings.OverridableSetting<String> creditNoteCodes;
    private final OverallSettings.OverridableSetting<String> creditMemos;

    private final OverallSettings.OverridableSetting<Boolean> automaticPdfGenerationEnabled;

    public JudgeSettings(OverallSettings overallSettings) {

        this.invoiceCodes = overallSettings.createSetting(
                INVOICE_CODES,
                "efris_invoice",
                String::valueOf);

        this.creditNoteCodes = overallSettings.createSetting(
                CREDIT_NOTE_CODES,
                "efris_credit_note",
                String::valueOf);

        this.creditMemos = overallSettings.createSetting(
                CREDIT_MEMOS,
                "efris_credit_memos",
                String::valueOf);

        this.automaticPdfGenerationEnabled = overallSettings.createSetting(
                AUTOMATIC_PDF_GENERATION_ENABLED,
                false,
                Boolean::parseBoolean);
    }

    public String getInvoiceCodes(String shop, String countryCode){
        return this.invoiceCodes.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public String getCreditNoteCodes(String shop, String countryCode){
        return this.creditNoteCodes.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public String getCreditMemos(String shop, String countryCode){
        return this.creditMemos.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean isAutomaticPdfGenerationEnabled(String shop, String countryCode){
        return this.automaticPdfGenerationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class DocumentLine {

    //
    // Internal fields
    //

    private UUID id;
    private Document document;

    //
    // General information
    //

    private int position;
    private BigDecimal quantity;
    private UnitOfMeasure unitOfMeasure;
    private String unitOfPackage;

    //
    // Product information
    //

    private String itemCode;
    private String skuVariant;
    private String itemName;
    private ItemType itemType;
    private Category category;

    //
    // Price information
    //

    private BigDecimal unitPrice;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal totalTaxAmount;
    private List<AppliedTax> appliedTaxes;
    private Discount discount;

    public DocumentLine withoutDbField() {
        return this.toBuilder()
                .id(null)
                .document(null)
                .category(category != null ? category.withoutDbField() : null)
                .build();
    }

    public static class DocumentLineBuilder {

        public DocumentLineBuilder generateId() {
            id = UUID.randomUUID();
            return this;
        }

    }

    @Data
    @Builder(toBuilder = true)
    public static class AppliedTax {
        private TaxCategory taxCategory;
        private BigDecimal taxRate;
        private BigDecimal taxFixedAmount;
        private BigDecimal taxAmount;

        public void convertAmountsToPositiveValue() {
            this.taxFixedAmount = this.taxFixedAmount != null && this.taxFixedAmount.compareTo(BigDecimal.ZERO) < 0 ?
                    this.taxFixedAmount.negate() : this.taxFixedAmount;
            this.taxAmount = this.taxAmount != null && this.taxAmount.compareTo(BigDecimal.ZERO) < 0 ?
                    this.taxAmount.negate() : this.taxAmount;
        }
    }

    @Data
    @Builder(toBuilder = true)
    public static class Discount {
        private BigDecimal rate;
        private BigDecimal amount;

        public void convertAmountsToPositiveValue() {
            this.amount = this.amount != null && this.amount.compareTo(BigDecimal.ZERO) < 0 ?
                    this.amount.negate() : this.amount;
        }
    }

    public void convertAmountsToPositiveValue() {
        this.totalAmount = this.totalAmount != null && this.totalAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalAmount.negate() : this.totalAmount;
        this.netAmount = this.netAmount != null && this.netAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.netAmount.negate() : this.netAmount;
        this.unitPrice = this.unitPrice != null && this.unitPrice.compareTo(BigDecimal.ZERO) < 0 ?
                this.unitPrice.negate() : this.unitPrice;
        this.totalTaxAmount = this.totalTaxAmount != null && this.totalTaxAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalTaxAmount.negate() :
                this.totalTaxAmount;
        this.appliedTaxes.forEach(AppliedTax::convertAmountsToPositiveValue);
        if (this.discount != null) {
            this.discount.convertAmountsToPositiveValue();
        }
    }

    public boolean isPositive() {
        return this.totalAmount.compareTo(BigDecimal.ZERO) >= 0;
    }
}

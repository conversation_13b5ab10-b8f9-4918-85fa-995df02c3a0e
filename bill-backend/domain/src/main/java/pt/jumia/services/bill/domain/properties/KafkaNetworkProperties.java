package pt.jumia.services.bill.domain.properties;

import lombok.Data;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * at 05/17/2022
 */
@Data
@Component
@ConfigurationProperties(prefix = "kafka")
public class KafkaNetworkProperties {

    private Topics topics = new Topics();

    @Bean
    @ConditionalOnProperty("kafka.topics.bill-documents.create")
    public NewTopic billTopic() {
        return new NewTopic(
                this.topics.billDocuments.getName(),
                this.topics.billDocuments.partitions,
                this.topics.billDocuments.replication
        );
    }

    @Bean
    @ConditionalOnProperty("kafka.topics.taxi-documents-status-updates.create")
    public NewTopic taxiTopic() {
        return new NewTopic(
                this.topics.billDocuments.getName(),
                this.topics.billDocuments.partitions,
                this.topics.billDocuments.replication
        );
    }

    @Data
    public static class Bill {

        private Consumer consumer;
        private Producer producer;

        @Data
        public static class Consumer {
            private String keyDeserializer = "org.apache.kafka.common.serialization.StringDeserializer";
            private String valueDeserializer = "org.springframework.kafka.support.serializer.JsonDeserializer";
        }

        @Data
        public static class Producer {
            private String valueSerializer = "org.springframework.kafka.support.serializer.JsonSerializer";
        }
    }

    @Data
    public static class Topics {

        private TopicProperties billDocuments;
        private TopicProperties taxiDocumentsStatusUpdates;

        @Data
        public static class TopicProperties {
            private boolean autoStart;
            private String name;
            private int partitions = 1;
            private short replication = 1;
        }
    }
}


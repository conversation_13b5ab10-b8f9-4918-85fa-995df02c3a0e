package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder(toBuilder = true)
public class DocumentAggregate {

    private Document document;
    private List<DocumentLine> lines;
    private List<DocumentTransformation> documentTransformations;
    private List<TaxCategoryTotal> taxCategoryTotals;
    private TaxAuthoritiesDetails taxAuthoritiesDetails;

    public DocumentAggregate withoutDbField() {
        return this.toBuilder()
                .document(document != null ? document.withoutDbField() : null)
                .lines(lines != null ? lines
                        .stream()
                        .map(DocumentLine::withoutDbField)
                        .collect(Collectors.toList()) : null)
                .documentTransformations(documentTransformations != null ? documentTransformations
                        .stream()
                        .map(DocumentTransformation::withoutDbFields)
                        .collect(Collectors.toList()) : null)
                .taxCategoryTotals(taxCategoryTotals != null ?
                        taxCategoryTotals
                                .stream()
                                .map(TaxCategoryTotal::withoutDbField)
                                .collect(Collectors.toList()) : null)
                .taxAuthoritiesDetails(null)
                .build();
    }

    public void convertAmountsToPositiveValue() {
        this.document.convertAmountsToPositiveValue();
        this.lines.forEach(DocumentLine::convertAmountsToPositiveValue);
        this.taxCategoryTotals.forEach(TaxCategoryTotal::convertAmountsToPositiveValue);
    }
}

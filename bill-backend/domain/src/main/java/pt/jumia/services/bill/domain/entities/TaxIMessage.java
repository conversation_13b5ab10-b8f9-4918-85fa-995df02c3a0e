package pt.jumia.services.bill.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class TaxIMessage {

    @Nullable
    private Long id;

    private String sid;

    private Map<String, Object> payload;

    private Status status;

    private String errorCode;

    private String errorDescription;


    public TaxIMessage withoutDbFields() {
        return toBuilder()
                .id(null)
                .build();
    }

    public enum Status {
        NEW("New"),
        CREATED("Created successfully"),
        CREATE_FAILED("Creation failed"),
        PUBLISHED("Published successfully"),
        PUBLISH_FAILED("Publishing failed"),
        SUBMITTED("Submitted successfully"),
        SUBMIT_FAILED("Submission failed"),
        PROCESSED("Processed successfully"),
        PROCESS_FAILED("Processing failed"),
        CANCELLED("Cancelled");

        private final String name;

        Status(String name) {
            this.name = name;
        }

        public String toString() {
            return this.name;
        }
    }
}

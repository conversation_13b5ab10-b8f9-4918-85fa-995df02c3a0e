package pt.jumia.services.bill.domain.repository;

import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface DocumentAggregateRepository {
    Optional<DocumentAggregate> findByDocumentId(UUID id);

    List<DocumentAggregate> findAll(DocumentFilter documentAggregatesFilters);

    List<DocumentAggregate> findAll(DocumentFilter documentAggregatesFilters,
                                    DocumentSortFilters sortFilters,
                                    PageFilters pageFilters);

    DocumentAggregate save(DocumentAggregate documentAggregate);

    List<DocumentAggregate> saveAll(List<DocumentAggregate> documentAggregates);
}

package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;

import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class ReadDocumentAggregateUseCase {
    private final DocumentAggregateRepository documentAggregateRepository;

    public DocumentAggregate executeBySidAndSuccessStatus(String documentSid) {
        List<DocumentAggregate> documentAggregates = documentAggregateRepository
                .findAll(DocumentFilter.builder()
                        .sid(documentSid)
                        .statuses(List.of(DocumentStatus.TAX_SUCCESS,
                                DocumentStatus.TAX_SKIPPED))
                        .include(List.of(Document.Details.values()))
                        .build());
        if (documentAggregates.size() == 0) {
            throw EntityNotFoundException.createNotFound(Document.class,
                    "success or skipped status and sid",
                    documentSid);
        }
        return documentAggregates.get(0);
    }

    public DocumentAggregate execute(UUID documentId) {
        return documentAggregateRepository.findByDocumentId(documentId)
                .orElseThrow(() -> EntityNotFoundException.createNotFound(Document.class, documentId));
    }

    public List<DocumentAggregate> execute(DocumentFilter filter, DocumentSortFilters sort, PageFilters page) {
        return documentAggregateRepository
                .findAll(filter.toBuilder().include(List.of(Document.Details.values())).build(), sort, page);
    }
}

package pt.jumia.services.bill.domain.exceptions.afroms;

import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

public class AfromsErrorException extends CodedException {

    private static final long serialVersionUID = 2941703717236621963L;

    private AfromsErrorException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    private AfromsErrorException(ErrorCode errorCode, String message, Exception cause) {
        super(errorCode, message, cause);
    }

    public static AfromsErrorException buildResponseNotOk(int statusCode, String response) {
        return new AfromsErrorException(ErrorCode.AFROMS_REQUEST_FAILED,
                String.format("%s - %s", statusCode, response));
    }

    public static AfromsErrorException buildResponseNotOk(int statusCode, String response, Exception e) {
        return new AfromsErrorException(ErrorCode.AFROMS_REQUEST_FAILED,
                String.format("%s - %s", statusCode, response), e);
    }

    public static AfromsErrorException buildFailedRequest(String message, Exception cause) {
        return new AfromsErrorException(ErrorCode.AFROMS_REQUEST_FAILED, message, cause);
    }

    public static AfromsErrorException missingConfiguration(String message) {
        return new AfromsErrorException(ErrorCode.AFROMS_MISSING_CONFIGURATION, message);
    }

    public static AfromsErrorException productNotFound(String message) {
        return new AfromsErrorException(ErrorCode.AFROMS_PRODUCT_NOT_FOUND, message);
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class DiffDocumentPayloadValidator implements Validator {

    private final DocumentAggregateRepository documentAggregateRepository;

    @Override
    public List<ValidationError> validate(DocumentAggregate newAggregate) {
        List<ValidationError> errors = new ArrayList<>();

        DocumentFilter filter = DocumentFilter.builder()
                .sid(newAggregate.getDocument().getSid())
                .statuses(DocumentStatus.getErrorStatuses())
                .include(List.of(Document.Details.values()))
                .build();

        documentAggregateRepository.findAll(filter)
                .forEach(currentAggregate -> {
                    if (this.compare(currentAggregate, newAggregate)) {
                        errors.add(new ValidationError(
                                ErrorCode.DUPLICATE_DOCUMENT_PAYLOAD,
                                String.format("Repeated document data with sid '%s'", newAggregate.getDocument().getSid())
                        ));
                    }
                });
        return errors;
    }

    private boolean compare(DocumentAggregate currentAggregate, DocumentAggregate newAggregate) {
        return currentAggregate.withoutDbField().equals(newAggregate.withoutDbField());
    }
}

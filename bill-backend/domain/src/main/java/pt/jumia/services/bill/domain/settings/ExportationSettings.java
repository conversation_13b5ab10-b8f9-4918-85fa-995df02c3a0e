package pt.jumia.services.bill.domain.settings;

import lombok.Data;

@Data
public class ExportationSettings {

    private static final String EXPORTATION_DOCUMENT_ROWS_LIMIT = "exportation.documents.rows_limit";
    private static final String EXPORTATION_DOCUMENT_EXCEPTION_TRACE_LINES_LIMIT = "exportation.documents.exception_trace_lines_limit";

    private final OverallSettings.OverridableSetting<Integer> exportationDocumentRowsLimit;
    private final OverallSettings.OverridableSetting<Integer> exportationDocumentExceptionTraceLinesLimit;

    public ExportationSettings(OverallSettings overallSettings) {
        this.exportationDocumentRowsLimit = overallSettings.createSetting(EXPORTATION_DOCUMENT_ROWS_LIMIT, 50000, Integer::parseInt);
        this.exportationDocumentExceptionTraceLinesLimit = overallSettings.createSetting(EXPORTATION_DOCUMENT_ROWS_LIMIT, 3, Integer::parseInt);
    }

    public Integer getExportationDocumentRowsLimit(){
        return this.exportationDocumentRowsLimit.getOverride();
    }

    public Integer getExportationDocumentExceptionTraceLinesLimit(){
        return this.exportationDocumentExceptionTraceLinesLimit.getOverride();
    }
}

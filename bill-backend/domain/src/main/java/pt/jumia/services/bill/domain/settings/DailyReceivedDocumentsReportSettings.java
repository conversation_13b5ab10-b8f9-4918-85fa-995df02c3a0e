package pt.jumia.services.bill.domain.settings;


import com.neovisionaries.i18n.CountryCode;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import pt.jumia.services.bill.domain.entities.BusinessLine;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class DailyReceivedDocumentsReportSettings {

    private static final String COMMA = ",";
    private static final String REPORT_TIME_FRAME_IN_HOURS = "received_document_report.report_time_frame_in_hours";
    private static final String BUSINESS_LINES_ENABLED_REPORT = "received_document_report.enabled_business_lines";
    private static final String BUSINESS_LINES_COMMUNICATION_EMAILS = "received_document_report.business_lines_communications_emails";
    private static final String COMMUNICATION_EVENT_NAME = "received_document_report.communication_mod_event_name";

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<Integer> reportTimeFrameInHours;

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<String> enabledBusinessLines;

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<String> emailsForBusinessLines;

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<String> communicationEventName;

    public DailyReceivedDocumentsReportSettings(OverallSettings overallSettings) {

        this.reportTimeFrameInHours = overallSettings.createSetting(
                REPORT_TIME_FRAME_IN_HOURS, 72,
                Integer::parseInt);
        this.enabledBusinessLines = overallSettings.createSetting(
                BUSINESS_LINES_ENABLED_REPORT,
                "jumiaEG",
                String::valueOf);
        this.emailsForBusinessLines = overallSettings.createSetting(
                BUSINESS_LINES_COMMUNICATION_EMAILS,
                "<EMAIL>",
                String::valueOf);
        this.communicationEventName = overallSettings.createSetting(
                COMMUNICATION_EVENT_NAME,
                "billReceivedDocumentsDailyReport",
                String::valueOf);
    }

    public Integer getReportTimeFrameInHours() {
        return this.reportTimeFrameInHours.getDefault();
    }

    public List<BusinessLine> getEnabledBusinessLines() {
        String enabledBusinessLines = this.enabledBusinessLines.getDefault();
        return Arrays.stream(enabledBusinessLines.split(COMMA))
                .map(businessLineStr -> {
                    businessLineStr = businessLineStr.trim();
                    return BusinessLine.builder()
                            .shop(businessLineStr.substring(0, businessLineStr.length() - 2))
                            .countryCode(CountryCode.getByAlpha2Code(
                                    businessLineStr.substring(businessLineStr.length() - 2)
                            ))
                            .build();
                })
                .collect(Collectors.toList());
    }

    public List<String> getCommunicationEmailsForBusinessLine(String shop, String countryCode) {
        String emailsOfBusinessLine = this.emailsForBusinessLines.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));

        return Arrays.stream(emailsOfBusinessLine.split(COMMA))
                .map(String::trim)
                .collect(Collectors.toList());
    }

    public String getCommunicationEventName(String shop, String countryCode) {
        return this.communicationEventName.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

}

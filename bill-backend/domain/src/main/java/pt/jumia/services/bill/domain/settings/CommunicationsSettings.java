package pt.jumia.services.bill.domain.settings;

import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;

@Data
public class CommunicationsSettings {

    public static final String INVOICE_EVENT = "communications.event.sales_invoice";
    public static final String CREDIT_NOTE_EVENT = "communications.event.sales_credit_note";
    public static final String CREDIT_MEMO_EVENT = "communications.event.sales_credit_memo";
    public static final String ENABLED = "communications.enabled";

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<String> invoiceEvent;

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<String> creditNoteEvent;

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<String> creditMemoEvent;

    @Getter(AccessLevel.NONE)
    private final OverallSettings.OverridableSetting<Boolean> enabled;

    public CommunicationsSettings(OverallSettings overallSettings) {

        this.invoiceEvent = overallSettings.createSetting(
                INVOICE_EVENT,
                "invoice_created",
                String::valueOf);

        this.creditNoteEvent = overallSettings.createSetting(
                CREDIT_NOTE_EVENT,
                "credit_note_created",
                String::valueOf);

        this.creditMemoEvent = overallSettings.createSetting(
                CREDIT_MEMO_EVENT,
                "credit_memo_created",
                String::valueOf);

        this.enabled = overallSettings.createSetting(
                ENABLED,
                true,
                Boolean::parseBoolean);
    }

    public String getInvoiceEvent(String shop, String countryCode) {
        return this.invoiceEvent.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public String getCreditNoteEvent(String shop, String countryCode) {
        return this.creditNoteEvent.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public String getCreditMemoEvent(String shop, String countryCode) {
        return this.creditMemoEvent.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean isEnabled(String shop, String countryCode) {
        return this.enabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

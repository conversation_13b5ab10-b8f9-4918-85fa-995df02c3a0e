package pt.jumia.services.bill.domain;


public final class Permissions {

    public static final String CAN_ACCESS = "can_access";
    public static final String CAN_MANAGE_DOCUMENTS = "manage_documents";
    public static final String CAN_VIEW_DOCUMENTS = "view_documents";
    public static final String MANAGE_SETTINGS = "manage_settings";
    public static final String RESUBMIT_DOCUMENT = "resubmit_document";
    public static final String ACK_DOCUMENT = "ack_document";
    public static final String CANCEL_DOCUMENT = "cancel_document";
    public static final String DECLINE_REJECTED_DOCUMENT = "decline_rejected_documents";
    public static final String CAN_RETRY_DOCUMENTS = "can_retry_documents";
    public static final String CAN_LIST_RECEIVED_DOCUMENTS = "can_list_received_documents";
    public static final String CAN_CREATE_RECEIVED_DOCUMENTS = "can_create_received_documents";
    public static final String REVIEW_RECEIVED_DOCUMENTS = "review_received_documents";
    public static final String REJECT_RECEIVED_DOCUMENTS = "reject_received_documents";
    public static final String APPROVED_RECEIVED_DOCUMENTS = "approved_received_documents";
    public static final String DECLINE_RECEIVED_DOCUMENTS_CANCELLATION = "decline_received_documents_cancellation";
    public static final String SEND_DOCUMENTS_THROUGH_EMAIL = "send_documents_through_email";
}

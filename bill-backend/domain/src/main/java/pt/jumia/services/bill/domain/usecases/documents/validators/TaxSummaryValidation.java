package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class TaxSummaryValidation implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        List<ValidationError> errors = new ArrayList<>();

        aggregate.getTaxCategoryTotals().forEach(taxCategoryTotal -> {
            BigDecimal total = taxCategoryTotal.getTaxAmount().add(taxCategoryTotal.getNetAmount());
            if (!total.equals(taxCategoryTotal.getTotalAmount())) {
                errors.add(new ValidationError(
                        ErrorCode.TAX_SUMMARY_NOT_VALID,
                        String.format("Total amount in tax is not equal the sum of net and tax amounts in document: %s",
                                taxCategoryTotal.getTaxCategory())
                ));
            }
        });
        return errors;
    }
}

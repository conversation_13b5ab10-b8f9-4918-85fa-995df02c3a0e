package pt.jumia.services.bill.domain.entities.dtos;

import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;

import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class TaxAuthoritiesDetailsUpdateDto {
    private UUID documentId;
    private TaxAuthoritiesDetails taxAuthoritiesDetails;
    private OverrideFields overrideFields;
    private DocumentStatus documentStatus;

}

package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Value;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.UUID;

@Value
@Builder(toBuilder = true)
public class Issuer {

    UUID id;
    IssuerType type;

    //
    // Identification
    //

    String legalName;
    String name;
    String taxIdentificationNumber;
    String businessRegistrationNumber;
    String branch;

    //
    // Contact
    //

    Address address;
    String email;
    String mobilePhone;
    String linePhone;

    //
    // Audit information
    //

    LocalDateTime createdAt;
    String createdBy;

    public Issuer withoutDbField() {
        return this.toBuilder().
                id(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public static class IssuerBuilder {

        public IssuerBuilder generateId() {
            id = UUID.nameUUIDFromBytes(this.generateStringId().getBytes(StandardCharsets.UTF_8));
            return this;
        }

        private String generateStringId() {
            return "Issuer{" +
                    "type=" + type +
                    ", legalName='" + legalName + '\'' +
                    ", name='" + name + '\'' +
                    ", taxIdentificationNumber='" + taxIdentificationNumber + '\'' +
                    ", businessRegistrationNumber='" + businessRegistrationNumber + '\'' +
                    ", branch='" + branch + '\'' +
                    ", address=" + (address != null ? address.generateStringId() : null) +
                    ", email='" + email + '\'' +
                    ", mobilePhone='" + mobilePhone + '\'' +
                    ", linePhone='" + linePhone + '\'' +
                    '}';
        }

    }
}

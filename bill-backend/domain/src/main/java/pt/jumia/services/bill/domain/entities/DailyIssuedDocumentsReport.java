package pt.jumia.services.bill.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class DailyIssuedDocumentsReport {
    private BusinessLine businessLine;
    private RangeReport detailedReport;
    private RangeReport overviewReport;

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RangeReport {
        private LocalDateTime reportRangeStartDate;
        private LocalDateTime reportRangeEndDate;
        private int range;
        private Map<DocumentStatus, Long> statusesMap;
    }
}

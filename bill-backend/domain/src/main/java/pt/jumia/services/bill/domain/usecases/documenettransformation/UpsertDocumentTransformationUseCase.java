package pt.jumia.services.bill.domain.usecases.documenettransformation;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class UpsertDocumentTransformationUseCase {

    private final DocumentTransformationRepository documentTransformationRepository;


    private final CreateDocumentTransformationUseCase createDocumentTransformationUseCase;

    public void execute(UUID documentId, List<DocumentTransformation> documentTransformationGivenList) {

        if (documentTransformationGivenList == null || documentTransformationGivenList.isEmpty()) {
            throw new IllegalArgumentException("document transformation can not be empty or null");
        }

        documentTransformationGivenList.stream()
                .forEach(documentTransformation -> {
                    Optional<DocumentTransformation> optionalDocumentTransformation = documentTransformationRepository
                            .findByDocumentIdAndTypeAndOriginalValue(documentId, documentTransformation.getType(),
                                    documentTransformation.getOriginalValue());
                    if (optionalDocumentTransformation.isEmpty()) {
                        documentTransformation.setDocument(Document.builder().id(documentId).build());
                        createDocumentTransformationUseCase.execute(documentTransformation);
                    } else if (!optionalDocumentTransformation.get().getNewValue().equals(documentTransformation.getNewValue())) {
                        documentTransformationRepository.update(optionalDocumentTransformation.get().getId(),
                                optionalDocumentTransformation.get().toBuilder()
                                .newValue(documentTransformation.getNewValue())
                                .build());
                    }
                });
    }
}

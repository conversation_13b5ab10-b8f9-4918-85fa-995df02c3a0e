package pt.jumia.services.bill.domain.settings;

import lombok.Data;

@Data
public class CancellationSettings {

    public static final String INVOICE_CANCELLATION_ENABLED = "documents.sales_invoice.cancellation_enabled";
    public static final String CREDIT_NOTE_CANCELLATION_ENABLED = "documents.sales_credit_note.cancellation_enabled";
    public static final String CREDIT_MEMO_CANCELLATION_ENABLED = "documents.sales_credit_memo.cancellation_enabled";
    public static final String DEBIT_NOTE_CANCELLATION_ENABLED = "documents.sales_debit_note.cancellation_enabled";
    public static final String PURCHASE_INVOICE_CANCELLATION_ENABLED = "documents.purchase_invoice.cancellation_enabled";
    public static final String PURCHASE_CREDIT_NOTE_CANCELLATION_ENABLED = "documents.purchase_credit_note.cancellation_enabled";
    public static final String PURCHASE_DEBIT_NOTE_CANCELLATION_ENABLED = "documents.purchase_debit_note.cancellation_enabled";

    private final OverallSettings.OverridableSetting<Boolean> invoiceCancellationEnabled;
    private final OverallSettings.OverridableSetting<Boolean> creditNoteCancellationEnabled;
    private final OverallSettings.OverridableSetting<Boolean> creditMemoCancellationEnabled;
    private final OverallSettings.OverridableSetting<Boolean> debitNoteCancellationEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseInvoiceCancellationEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseCreditNoteCancellationEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseDebitNoteCancellationEnabled;

    public CancellationSettings(OverallSettings overallSettings) {

        this.invoiceCancellationEnabled = overallSettings.createSetting(
                INVOICE_CANCELLATION_ENABLED,
                true,
                Boolean::parseBoolean);

        this.creditNoteCancellationEnabled = overallSettings.createSetting(
                CREDIT_NOTE_CANCELLATION_ENABLED,
                true,
                Boolean::parseBoolean);

        this.creditMemoCancellationEnabled = overallSettings.createSetting(
                CREDIT_MEMO_CANCELLATION_ENABLED,
                true,
                Boolean::parseBoolean);

        this.debitNoteCancellationEnabled = overallSettings.createSetting(
                DEBIT_NOTE_CANCELLATION_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseInvoiceCancellationEnabled = overallSettings.createSetting(
                PURCHASE_INVOICE_CANCELLATION_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseCreditNoteCancellationEnabled = overallSettings.createSetting(
                PURCHASE_CREDIT_NOTE_CANCELLATION_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseDebitNoteCancellationEnabled = overallSettings.createSetting(
                PURCHASE_DEBIT_NOTE_CANCELLATION_ENABLED,
                true,
                Boolean::parseBoolean);
    }

    public boolean getInvoiceCancellationEnabled(String shop, String countryCode) {
        return this.invoiceCancellationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getCreditNoteCancellationEnabled(String shop, String countryCode) {
        return this.creditNoteCancellationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getCreditMemoCancellationEnabled(String shop, String countryCode) {
        return this.creditMemoCancellationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getDebitNoteCancellationEnabled(String shop, String countryCode) {
        return this.debitNoteCancellationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseInvoiceCancellationEnabled(String shop, String countryCode){
        return this.purchaseInvoiceCancellationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseCreditNoteCancellationEnabled(String shop, String countryCode){
        return this.purchaseCreditNoteCancellationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseDebitNoteCancellationEnabled(String shop, String countryCode){
        return this.purchaseDebitNoteCancellationEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

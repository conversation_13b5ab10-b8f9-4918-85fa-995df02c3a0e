package pt.jumia.services.bill.domain.exceptions.taxi;

import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

public class TaxiNetworkException extends CodedException {

    private static final long serialVersionUID = 2941703717236621963L;

    private TaxiNetworkException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    private TaxiNetworkException(ErrorCode errorCode, String message, Exception cause) {
        super(errorCode, message, cause);
    }

    public static TaxiNetworkException buildResponseNotOk(int statusCode, String response) {
        return new TaxiNetworkException(ErrorCode.TAXI_REQUEST_FAILED, String.format("%s - %s", statusCode, response));
    }

    public static TaxiNetworkException buildResponseNotOk(int statusCode, String response, Exception e) {
        return new TaxiNetworkException(ErrorCode.TAXI_REQUEST_FAILED, String.format("%s - %s", statusCode, response), e);
    }

    public static TaxiNetworkException buildFailedRequest(String message, Exception cause) {
        return new TaxiNetworkException(ErrorCode.TAXI_REQUEST_FAILED, message, cause);
    }
}

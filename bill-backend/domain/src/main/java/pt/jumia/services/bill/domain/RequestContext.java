package pt.jumia.services.bill.domain;

import pt.jumia.services.acl.lib.RequestUser;

/**
 * The context of the current request. The data is stored at the thread level, so we can access this information
 * at any layer.
 */
public class RequestContext {

    private static final InheritableThreadLocal<RequestUser> USER_THREAD_LOCAL = new InheritableThreadLocal<>();

    public static RequestUser getUser() {
        return USER_THREAD_LOCAL.get();
    }

    public static String getUsername() {
        RequestUser requestUser = USER_THREAD_LOCAL.get();
        return requestUser == null ? null : requestUser.getUsername();
    }

    public static void setUser(RequestUser user) {
        USER_THREAD_LOCAL.set(user);
    }

    public static void clear() {
        USER_THREAD_LOCAL.remove();
    }
}

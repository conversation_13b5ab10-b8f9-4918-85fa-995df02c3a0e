package pt.jumia.services.bill.domain.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.Builder;
import lombok.Value;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.UUID;

@Value
@Builder(toBuilder = true)
public class DeliveryDetails {

    UUID id;
    String approach;
    String packaging;
    LocalDateTime dateValidity;
    String exportPort;
    BigDecimal grossWeight;
    BigDecimal netWeight;
    CountryCode countryOfOrigin;
    String terms;
    String createdBy;
    LocalDateTime createdAt;

    public DeliveryDetails withoutDbField() {
        return this.toBuilder()
                .id(null)
                .grossWeight(grossWeight != null ? grossWeight.setScale(4, RoundingMode.CEILING) : null)
                .netWeight(netWeight != null ? netWeight.setScale(4, RoundingMode.CEILING) : null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public static class DeliveryDetailsBuilder {

        public DeliveryDetailsBuilder generateId() {
            id = UUID.nameUUIDFromBytes(this.generateStringId().getBytes(StandardCharsets.UTF_8));
            return this;
        }

        private String generateStringId() {
            return "DeliveryDetails{" +
                    "approach=" + approach +
                    ", packaging='" + packaging + '\'' +
                    ", exportPort='" + exportPort + '\'' +
                    ", grossWeight='" + grossWeight + '\'' +
                    ", netWeight='" + netWeight + '\'' +
                    ", countryOfOrigin='" + (countryOfOrigin != null ? countryOfOrigin.getAlpha2() : null) + '\'' +
                    ", terms='" + terms + '\'' +
                    '}';
        }

    }
}

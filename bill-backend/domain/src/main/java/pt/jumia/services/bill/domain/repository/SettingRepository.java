package pt.jumia.services.bill.domain.repository;


import pt.jumia.services.bill.domain.entities.Setting;

import java.util.List;
import java.util.Optional;

public interface SettingRepository {
    Setting insert(Setting setting);
    Optional<Setting> findById(long id);
    List<Setting> findByProperty(String property);
    List<Setting> findAll();
    Setting update(long id, Setting setting);
    void deleteById(long id);
}

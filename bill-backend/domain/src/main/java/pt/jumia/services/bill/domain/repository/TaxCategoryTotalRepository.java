package pt.jumia.services.bill.domain.repository;

import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

public interface TaxCategoryTotalRepository {

    Optional<TaxCategoryTotal> findById(UUID id);

    List<TaxCategoryTotal> findByDocumentId(UUID id);

    TaxCategoryTotal save(TaxCategoryTotal taxCategoryTotal);

    int migrateDecimalFields(Long startId, Long endId);

    Map<String, Object> getDecimalFieldsMigrationStatus();

}

package pt.jumia.services.bill.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "api")
public class ApiProperties {
    private boolean swaggerEnabled = true;
    private String selfHost = "http://localhost:8080/";
    private List<String> allowedDomains = List.of(
            "http://localhost:9000", "http://localhost:8080", "http://localhost:3000", "http://localhost:4210");
}

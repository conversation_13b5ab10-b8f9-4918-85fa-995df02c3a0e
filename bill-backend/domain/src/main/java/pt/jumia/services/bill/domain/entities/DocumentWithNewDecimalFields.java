package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentWithNewDecimalFields extends Document {

    private BigDecimal issuedToLocalCurrencyExchangeRate_new;
    private BigDecimal totalAmount_new;
    private BigDecimal netAmount_new;
    private BigDecimal taxAmount_new;
    private BigDecimal discountAmount_new;
    private BigDecimal totalItemsDiscountAmount_new;
    private BigDecimal extraDiscountAmount_new;

    @Builder(builderMethodName = "newDecimalFieldsBuilder")
    public DocumentWithNewDecimalFields(Document document, 
                                       BigDecimal issuedToLocalCurrencyExchangeRate_new,
                                       BigDecimal totalAmount_new,
                                       BigDecimal netAmount_new,
                                       BigDecimal taxAmount_new,
                                       BigDecimal discountAmount_new,
                                       BigDecimal totalItemsDiscountAmount_new,
                                       BigDecimal extraDiscountAmount_new) {
        super(document.toBuilder());
        this.issuedToLocalCurrencyExchangeRate_new = issuedToLocalCurrencyExchangeRate_new;
        this.totalAmount_new = totalAmount_new;
        this.netAmount_new = netAmount_new;
        this.taxAmount_new = taxAmount_new;
        this.discountAmount_new = discountAmount_new;
        this.totalItemsDiscountAmount_new = totalItemsDiscountAmount_new;
        this.extraDiscountAmount_new = extraDiscountAmount_new;
    }

    @Override
    public Document withoutDbField() {
        Document document = super.withoutDbField();
        return DocumentWithNewDecimalFields.newDecimalFieldsBuilder()
                .document(document)
                .issuedToLocalCurrencyExchangeRate_new(issuedToLocalCurrencyExchangeRate_new != null ?
                        issuedToLocalCurrencyExchangeRate_new.setScale(10, RoundingMode.CEILING) : null)
                .totalAmount_new(totalAmount_new != null ? totalAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .netAmount_new(netAmount_new != null ? netAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .taxAmount_new(taxAmount_new != null ? taxAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .discountAmount_new(discountAmount_new != null ?
                        discountAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .totalItemsDiscountAmount_new(totalItemsDiscountAmount_new != null ?
                        totalItemsDiscountAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .extraDiscountAmount_new(extraDiscountAmount_new != null ?
                        extraDiscountAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .build();
    }

    @Override
    public void convertAmountsToPositiveValue() {
        super.convertAmountsToPositiveValue();
        
        this.totalAmount_new = this.totalAmount_new != null && this.totalAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalAmount_new.negate() : this.totalAmount_new;
        this.netAmount_new = this.netAmount_new != null && this.netAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.netAmount_new.negate() : this.netAmount_new;
        this.taxAmount_new = this.taxAmount_new != null && this.taxAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.taxAmount_new.negate() : this.taxAmount_new;
        this.discountAmount_new = this.discountAmount_new != null && this.discountAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.discountAmount_new.negate() : this.discountAmount_new;
        this.totalItemsDiscountAmount_new = this.totalItemsDiscountAmount_new != null && this.totalItemsDiscountAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalItemsDiscountAmount_new.negate() :
                this.totalItemsDiscountAmount_new;
        this.extraDiscountAmount_new = this.extraDiscountAmount_new != null && this.extraDiscountAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.extraDiscountAmount_new.negate() : this.extraDiscountAmount_new;
    }
}

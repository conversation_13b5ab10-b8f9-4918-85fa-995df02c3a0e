package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class TaxAuthoritiesDetails {

    private UUID id;
    private Document document;
    private String submissionId;
    private String taxDocumentNumber;
    private String qrCode;
    private String verificationCode;
    private String internalData;
    private String deviceNumber;
    private String statusCode;
    private String errorCode;
    private String exception;
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;
    private GhDocPdfRequestPayloadInfo docPdfFields;
    private List<DocumentTransformation> documentTransformations;

    public TaxAuthoritiesDetails withoutDbField() {
        return this.toBuilder()
                .id(null)
                .document(null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .updatedBy(null)
                .build();
    }

    public static class TaxAuthoritiesDetailsBuilder {
        public TaxAuthoritiesDetailsBuilder generateId() {
            id = UUID.randomUUID();
            return this;
        }
    }

}

package pt.jumia.services.bill.domain.usecases.documents;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.JudgeRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.exceptions.document.CannotPreviewDocumentException;
import pt.jumia.services.bill.domain.exceptions.judge.JudgeNetworkException;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class DownloadDocumentUseCase {

    private final JudgeRequester judgeRequester;
    private final ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    private final GenerateJudgeDocumentUseCase generateJudgeDocumentUseCase;

    public DocumentFile execute(Document document){

        if (!document.getStatus().isSuccessful()) {
            throw CannotPreviewDocumentException.createInvalidDocumentStatusForPrinting(document);
        }

        return judgeRequester.downloadDocumentBySid(Optional.ofNullable(document.getJudgeSid())
                .orElseGet(() -> {
                    try {
                        DocumentAggregate documentAggregate = readDocumentAggregateUseCase.execute(document.getId());
                        DocumentAggregate updatedDocAggregateWithJudgeSid = generateJudgeDocumentUseCase.execute(documentAggregate);
                        return updatedDocAggregateWithJudgeSid.getDocument().getJudgeSid();
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                }));
    }
}

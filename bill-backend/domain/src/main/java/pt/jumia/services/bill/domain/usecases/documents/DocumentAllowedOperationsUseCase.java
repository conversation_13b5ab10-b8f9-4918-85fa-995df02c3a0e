package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.acl.UserPermissionValidationUseCase;

@Component
@RequiredArgsConstructor
public class DocumentAllowedOperationsUseCase {
    private final OverallSettings overallSettings;
    private final UserPermissionValidationUseCase userPermissionValidationUseCase;

    public Document appendDocumentPossibleOperations(Document document) {
        return document.toBuilder().documentPossibleOperations(
                DocumentPossibleOperations.builder()
                        .canApprove(canApproveThisDocument(document))
                        .canAcknowledge(canAcknowledgeThisDocument(document))
                        .canCancel(canCancelThisDocument(document))
                        .canDeclineDocumentRejection(canDeclineRejectionForThisDocument(document))
                        .canRetryDocuments(checkRetryDocuments(document))
                        .build()
        ).build();
    }

    private boolean canDeclineRejectionForThisDocument(Document document) {
        if (!DocumentStatus.TAX_REJECTED.equals(document.getStatus())) {
            return false;
        }
        if (!this.checkDeclineRejectionSettingsEnabled(document)) {
            return false;
        }
        try {
            userPermissionValidationUseCase.checkCanDeclineDocumentRejectionByCountryCodeOrThrow(
                    RequestContext.getUser(),
                    document.getCountry()
            );
            return true;
        } catch (UserForbiddenException exception) {
            return false;
        }
    }

    private boolean canAcknowledgeThisDocument(Document document) {
        if (DocumentFlow.RETAIL.equals(document.getFlow())) {
            if (!document.getStatus().canTransitionTo(DocumentStatus.TAX_ERROR_ACKED, document.getCountry())) {
                return false;
            }
        }
        try {
            userPermissionValidationUseCase.checkCanAcknowledgeDocumentByCountryCodeOrThrow(
                    RequestContext.getUser(),
                    document.getCountry()
            );
            return true;
        } catch (UserForbiddenException exception) {
            return false;
        }
    }

    private boolean canCancelThisDocument(Document document) {
        if (!DocumentStatus.TAX_SUCCESS.equals(document.getStatus())) {
            return false;
        }
        if (!this.checkCancelSettingsEnabled(document)) {
            return false;
        }
        try {
            userPermissionValidationUseCase.checkCanCancelDocumentByCountryCodeOrThrow(
                    RequestContext.getUser(),
                    document.getCountry()
            );
            return true;
        } catch (UserForbiddenException exception) {
            return false;
        }
    }

    private boolean canApproveThisDocument(Document document) {
        if (!DocumentStatus.TAX_PENDING.equals(document.getStatus())) {
            return false;
        }
        if (!this.checkApproveSettingsEnabled(document)) {
            return false;
        }
        try {
            userPermissionValidationUseCase.checkCanApprovedReceivedDocumentsByCountryCodeOrThrow(
                    RequestContext.getUser(),
                    document.getCountry()
            );
            return true;
        } catch (UserForbiddenException exception) {
            return false;
        }
    }

    private boolean checkCancelSettingsEnabled(Document document) {
        DocumentType type = document.getType();
        switch (type) {
            case SALES_INVOICE:
                return this.overallSettings
                        .getCancellationSettings()
                        .getInvoiceCancellationEnabled(
                                document.getShop(),
                                document.getCountry().getAlpha2());
            case SALES_CREDIT_NOTE:
                return this.overallSettings
                        .getCancellationSettings()
                        .getCreditNoteCancellationEnabled(
                                document.getShop(),
                                document.getCountry().getAlpha2());
            case SALES_CREDIT_MEMO:
                return this.overallSettings
                        .getCancellationSettings()
                        .getCreditMemoCancellationEnabled(
                                document.getShop(),
                                document.getCountry().getAlpha2());
            case SALES_DEBIT_NOTE:
                return this.overallSettings
                        .getCancellationSettings()
                        .getDebitNoteCancellationEnabled(
                                document.getShop(),
                                document.getCountry().getAlpha2());
            case PURCHASE_INVOICE:
                return this.overallSettings
                        .getCancellationSettings()
                        .getPurchaseInvoiceCancellationEnabled(
                                document.getShop(),
                                document.getCountry().getAlpha2());
            case PURCHASE_CREDIT_NOTE:
                return this.overallSettings
                        .getCancellationSettings()
                        .getPurchaseCreditNoteCancellationEnabled(
                                document.getShop(),
                                document.getCountry().getAlpha2());
            case PURCHASE_DEBIT_NOTE:
                return this.overallSettings
                        .getCancellationSettings()
                        .getPurchaseDebitNoteCancellationEnabled(
                                document.getShop(),
                                document.getCountry().getAlpha2());
        }
        return false;
    }

    private boolean checkDeclineRejectionSettingsEnabled(Document document) {
        DocumentType type = document.getType();
        switch (type) {
            case SALES_INVOICE:
                return this.overallSettings.getRejectionSettings()
                        .getInvoiceRejectionDeclineEnabled(document.getShop(),
                                document.getCountry().name());
            case SALES_CREDIT_NOTE:
                return this.overallSettings.getRejectionSettings()
                        .getCreditNoteRejectionDeclineEnabled(document.getShop(),
                                document.getCountry().name());
            case SALES_CREDIT_MEMO:
                return this.overallSettings.getRejectionSettings()
                        .getCreditMemoRejectionDeclineEnabled(document.getShop(),
                                document.getCountry().name());
            case SALES_DEBIT_NOTE:
                return this.overallSettings.getRejectionSettings()
                        .getDebitNoteRejectionDeclineEnabled(document.getShop(),
                                document.getCountry().name());
            case PURCHASE_INVOICE:
                return this.overallSettings.getRejectionSettings()
                        .getPurchaseInvoiceRejectionDeclineEnabled(document.getShop(),
                                document.getCountry().name());
            case PURCHASE_CREDIT_NOTE:
                return this.overallSettings.getRejectionSettings()
                        .getPurchaseCreditNoteRejectionDeclineEnabled(document.getShop(),
                                document.getCountry().name());
            case PURCHASE_DEBIT_NOTE:
                return this.overallSettings.getRejectionSettings()
                        .getPurchaseDebitNoteRejectionDeclineEnabled(document.getShop(),
                                document.getCountry().name());
        }
        return false;
    }

    private boolean checkApproveSettingsEnabled(Document document) {
        DocumentType type = document.getType();
        switch (type) {
            case SALES_INVOICE:
                return this.overallSettings.getApprovalSettings()
                        .getInvoiceApprovalEnabled(document.getShop(),
                                document.getCountry().name());
            case SALES_CREDIT_NOTE:
                return this.overallSettings.getApprovalSettings()
                        .getCreditNoteApprovalEnabled(document.getShop(),
                                document.getCountry().name());
            case SALES_CREDIT_MEMO:
                return this.overallSettings.getApprovalSettings()
                        .getCreditMemoApprovalEnabled(document.getShop(),
                                document.getCountry().name());
            case SALES_DEBIT_NOTE:
                return this.overallSettings.getApprovalSettings()
                        .getDebitNoteApprovalEnabled(document.getShop(),
                                document.getCountry().name());
            case PURCHASE_INVOICE:
                return this.overallSettings.getApprovalSettings()
                        .getPurchaseInvoiceApprovalEnabled(document.getShop(),
                                document.getCountry().name());
            case PURCHASE_CREDIT_NOTE:
                return this.overallSettings.getApprovalSettings()
                        .getPurchaseCreditNoteApprovalEnabled(document.getShop(),
                                document.getCountry().name());
            case PURCHASE_DEBIT_NOTE:
                return this.overallSettings.getApprovalSettings()
                        .getPurchaseDebitNoteApprovalEnabled(document.getShop(),
                                document.getCountry().name());        }
        return false;
    }

    private boolean checkRetryDocuments(Document document) {

        if (!DocumentStatus.getRetryStatuses().contains(document.getStatus())) {
            return false;
        }
        try {
            userPermissionValidationUseCase.checkCanRetryDocumentsByCountryCodeOrThrow(
                    RequestContext.getUser(),
                    document.getCountry()
            );
            return true;
        } catch (UserForbiddenException exception) {
            return false;
        }
    }

}

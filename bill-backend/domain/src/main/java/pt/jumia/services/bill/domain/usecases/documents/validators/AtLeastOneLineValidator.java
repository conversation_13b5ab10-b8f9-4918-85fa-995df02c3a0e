package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

@Component
public class AtLeastOneLineValidator implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        if (CollectionUtils.isEmpty(aggregate.getLines())) {
            return List.of(new ValidationError(
                    ErrorCode.MISSING_REQUIRED_FIELD,
                    "Documents must have at least one line."
            ));
        }
        return List.of();
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class LineTaxCategoryValidation implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        List<ValidationError> errors = new ArrayList<>();

        aggregate.getLines().forEach(documentLine -> {
            if (documentLine.getCategory() == null) {
                errors.add(new ValidationError(
                        ErrorCode.INVALID_TAX_CATEGORY,
                        String.format("Invalid tax category or null for document line: %s", documentLine.getItemCode())
                ));
            } else {
                if (documentLine.getCategory().getTaxAuthorityCode() == null
                        && documentLine.getCategory().getSid() == null) {
                    errors.add(new ValidationError(
                            ErrorCode.INVALID_TAX_CATEGORY,
                            String.format("Invalid tax category or null for document line: %s", documentLine.getItemCode())
                    ));
                }
            }
        });
        return errors;
    }
}

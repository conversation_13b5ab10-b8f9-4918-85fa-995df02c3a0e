package pt.jumia.services.bill.domain.usecases.receiveddocuments;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.usecases.documents.UpdateDocumentUseCase;

@Component
@RequiredArgsConstructor
@Slf4j
public class RejectReceivedDocumentUseCase {

    private final TaxiRequester taxiRequester;
    private final UpdateDocumentUseCase updateDocumentUseCase;

    public Document execute(DocumentAggregate documentAggregate, String rejectionReason) {
        /**
         * this function shall throw an exception if the transition is not allowed
         * if no exception happened, we shall ping the taxi requester
         * with instant update for the document (call back from taxi shall confirm this by passing the same status)
         */
        documentAggregate.getDocument().updateStatus(DocumentStatus.TAX_REJECTED, documentAggregate.getDocument().getCountry());
        taxiRequester.rejectReceivedDocument(documentAggregate, rejectionReason);

        return updateDocumentUseCase.execute(documentAggregate.getDocument().getId(),
                documentAggregate.getDocument(), true);
    }

}

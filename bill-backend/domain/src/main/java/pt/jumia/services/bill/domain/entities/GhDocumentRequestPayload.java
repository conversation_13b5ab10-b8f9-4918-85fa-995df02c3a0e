package pt.jumia.services.bill.domain.entities;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class GhDocumentRequestPayload {

    private String currency;
    private String businessPartnerName;
    private String userName;
    private String flag;
    private String calculationType;
    private String groupReferenceId;
    private String businessPartnerTin;
    private String saleType;
    private String discountType;
    private String reference;
    private String purchaseOrderReference;
    private String invoiceNumber;
    private BigDecimal exchangeRate;
    private BigDecimal totalAmount;
    private BigDecimal voucherAmount;
    private BigDecimal totalVat;
    private BigDecimal totalLevy;
    private BigDecimal discountAmount;
    private List<GhItemDetailDto> items;
    private String transactionDate;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GhItemDetailDto {

        @JsonProperty("itemCode")
        private String itemCode;
        private String itemCategory;
        private String expireDate;
        private String description;
        private BigDecimal quantity;
        private BigDecimal levyAmountA;
        private BigDecimal levyAmountB;
        private BigDecimal levyAmountC;
        private BigDecimal levyAmountD;
        private BigDecimal levyAmountE;
        private BigDecimal discountAmount;
        private String batchCode;
        private BigDecimal unitPrice;
    }
}

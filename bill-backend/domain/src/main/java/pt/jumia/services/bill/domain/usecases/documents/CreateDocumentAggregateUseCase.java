package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.bill.domain.KafkaProducer;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.entities.OtherSplitDocument;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.CreateStatementException;
import pt.jumia.services.bill.domain.exceptions.afroms.AfromsErrorException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.repository.IssuerTemplateRepository;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.documentapilogs.UpsertDocumentApiLogUseCase;
import pt.jumia.services.bill.domain.usecases.documents.processors.DocumentProcessorChain;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationChain;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationException;
import pt.jumia.services.bill.domain.usecases.documents.validators.ValidationError;
import pt.jumia.services.bill.domain.usecases.metrics.MonitoringUseCase;
import pt.jumia.services.bill.domain.usecases.statements.CreateDocumentAggregateListUseCase;
import pt.jumia.services.bill.domain.utils.DecimalFieldsConverter;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class CreateDocumentAggregateUseCase {

    private final DocumentAggregateRepository documentAggregateRepository;
    private final IssuerTemplateRepository issuerTemplateRepository;
    private final DocumentRepository documentRepository;
    private final DocumentValidationChain documentValidationChain;
    private final DocumentProcessorChain documentProcessorChain;
    private final CreateDocumentAggregateListUseCase createDocumentAggregateListUseCase;
    private final OverallSettings overallSettings;
    private final TaxiRequester taxiRequester;
    private final KafkaProducer kafkaProducer;
    private final UpsertDocumentApiLogUseCase upsertDocumentApiLogUseCase;
    private final MonitoringUseCase monitoringUseCase;
    private final UpsertDocumentApiLogUseCase upsertDocumentApiLogUseCase;
    private final KafkaProducer billProducer;
    private final DocumentResendUseCase documentResendUseCase;

    public DocumentAggregate execute(DocumentApiLog documentApiLog, DocumentAggregate documentAggregate) {
        List<ValidationError> errors = documentValidationChain.runAllValidations(documentAggregate);
        if (!CollectionUtils.isEmpty(errors)) {
            upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                    .status(DocumentApiLog.DocumentApiLogStatus.FAILED_VALIDATION_CHAIN)
                    .errors(errors.toString())
                    .build());
            log.warn("Document '{}' has some validation errors {}", documentAggregate.getDocument().getSid(), errors);
            throw new DocumentValidationException(
                    String.format("Document %s has some validation errors", documentAggregate.getDocument().getSid()),
                    errors);
        }

        DocumentStatus originalStatus = documentAggregate.getDocument().getStatus();

        try {
            documentProcessorChain.runBeforeSave(documentAggregate);
        } catch (ConflictOperationException exception) {
            upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                    .status(DocumentApiLog.DocumentApiLogStatus.FAILED_ACKNOWLEDGE)
                    .errors(exception.getMessage())
                    .build());
            throw exception;
        } catch (AfromsErrorException exception) {
            upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                    .status(DocumentApiLog.DocumentApiLogStatus.FAILED_UCR_MAPPING)
                    .errors(exception.getMessage())
                    .build());
            throw exception;
        }
        log.warn("Document '{}' has some validation errors {}", documentAggregate.getDocument().getSid(), errors);
        documentAggregate.setDocument(documentAggregate.getDocument().toBuilder()
                .status(DocumentStatus.NEW)
                .build());

        appendIssuerTemplate(documentAggregate);
        
        // Convert document aggregate to use new decimal fields
        documentAggregate = DecimalFieldsConverter.convertDocumentAggregateToNewDecimalFields(documentAggregate);

        DocumentAggregate savedAggregate;
        if (!DocumentFlow.RECEIVED.equals(documentAggregate.getDocument().getFlow())) {
            savedAggregate = documentAggregateRepository.save(documentAggregate);
            savedAggregate = this.pushDocumentProcess(savedAggregate);
        } else {
            documentAggregate.getDocument().setFlow(DocumentFlow.RECEIVED);
            documentAggregate.getDocument().setStatus(originalStatus);
            savedAggregate = documentAggregateRepository.save(documentAggregate);
        }

        upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                .resultingDocumentsIds(List.of(savedAggregate.getDocument().getId()))
                .status(DocumentApiLog.DocumentApiLogStatus.SAVED)
                .build());

        return savedAggregate;
    }

    @SuppressWarnings("PMD.AvoidLiteralsInIfCondition")
    public List<DocumentAggregate> executeAsStatement(DocumentApiLog documentApiLog, DocumentAggregate documentAggregate) {
        try {
            List<DocumentAggregate> documentAggregates = createDocumentAggregateListUseCase.execute(documentAggregate);
            documentAggregates.forEach(aggregate -> {
                this.documentProcessorChain.runBeforeSave(aggregate);
                this.appendIssuerTemplate(aggregate);
            });
            
            // Convert all document aggregates to use new decimal fields
            documentAggregates = documentAggregates.stream()
                .map(DecimalFieldsConverter::convertDocumentAggregateToNewDecimalFields)
                .collect(Collectors.toList());
                
            List<DocumentAggregate> aggregatesSaved = documentAggregateRepository.saveAll(documentAggregates);
            if (aggregatesSaved.size() == 0) {
                log.error("Can't store all document for statement: {}", documentAggregate.getDocument().getSid());
                upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                        .status(DocumentApiLog.DocumentApiLogStatus.FAILED_SPLITTING)
                        .build());
                throw new CreateStatementException(String.format(
                        "Can't store all document for statement: %s.", documentAggregate.getDocument().getSid()
                ));
            }

            if (aggregatesSaved.size() == 2) {
                aggregatesSaved.get(0).getDocument()
                        .setOtherSplitDocument(new OtherSplitDocument(aggregatesSaved.get(1).getDocument()));
                aggregatesSaved.get(1).getDocument()
                        .setOtherSplitDocument(new OtherSplitDocument(aggregatesSaved.get(0).getDocument()));
                documentRepository.save(aggregatesSaved.get(0).getDocument());
                documentRepository.save(aggregatesSaved.get(1).getDocument());
            }

            upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                    .resultingDocumentsIds(aggregatesSaved.stream()
                            .map(aggregate -> aggregate.getDocument().getId())
                            .collect(Collectors.toList()))
                    .status(DocumentApiLog.DocumentApiLogStatus.SAVED)
                    .build());

            this.pushStatementProcess(aggregatesSaved);
            return aggregatesSaved;
        } catch (DocumentValidationException exception) {
            upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                    .status(DocumentApiLog.DocumentApiLogStatus.FAILED_SPLITTING)
                    .errors(exception.getMessage())
                    .build());
            throw exception;
        } catch (AfromsErrorException exception) {
            upsertDocumentApiLogUseCase.execute(documentApiLog.getId(), documentApiLog.toBuilder()
                    .status(DocumentApiLog.DocumentApiLogStatus.FAILED_UCR_MAPPING)
                    .errors(exception.getMessage())
                    .build());
            throw exception;
        }
    }

    private DocumentAggregate pushDocumentProcess(DocumentAggregate documentAggregate) {

        DocumentStatus responseDocumentStatus = this.documentStatusResponse();
        log.info("Starting push document to TaxI process. [{}]", documentAggregate.getDocument().getSid());
        try {
            // If Kafka setting is enabled.
            if (overallSettings.getKafkaSettings().isPushDocumentEnabled(
                    documentAggregate.getDocument().getShop(),
                    documentAggregate.getDocument().getCountry().getAlpha2())) {
                log.info("Pushing document [{}] through kafka", documentAggregate.getDocument().getSid());
                billProducer.pushDocument(documentAggregate);
            } else {
                log.info("Pushing document [{}] through http", documentAggregate.getDocument().getSid());
                pushDocument(documentAggregate);
            }

        } catch (Exception e) {
            log.info("Re-attempting to push document [{}] after kafka failed", documentAggregate.getDocument().getSid());
            responseDocumentStatus = tryToResendDocumentAfterKafkaFailed(documentAggregate);
        }

        return handleTaxiResponse(documentAggregate, responseDocumentStatus);
    }

    private DocumentStatus tryToResendDocumentAfterKafkaFailed(DocumentAggregate documentAggregate) {
        DocumentStatus responseDocumentStatus = this.documentStatusResponse();
        try {
            documentResendUseCase.executeForNewDocument(documentAggregate.getDocument());
            return responseDocumentStatus;
        } catch (Exception e) {
            log.error("Error pushing document '{}' to taxi", documentAggregate.getDocument().getSid(), e);

            return DocumentStatus.TAXI_INTERNAL_ERROR;
        }

    private void pushStatementProcess(List<DocumentAggregate> documentAggregateList) {
        DocumentStatus responseDocumentStatus = DocumentStatus.TAX_PENDING;
        try {
            pushStatement(documentAggregateList);
        } catch (Exception e) {
            log.error("Error pushing statement '{}' to taxi", documentAggregateList.get(0).getDocument().getSid(), e);
            responseDocumentStatus = DocumentStatus.TAXI_INTERNAL_ERROR;
        }

        for (DocumentAggregate documentAggregate : documentAggregateList) {
            handleTaxiResponse(documentAggregate, responseDocumentStatus);
        }

    }

    private DocumentAggregate handleTaxiResponse(DocumentAggregate documentAggregate,
                                                 DocumentStatus responseDocumentStatus) {
        log.info("Handling Taxi response, document '{}' type: {} marked as '{}'",
                documentAggregate.getDocument().getSid(),
                documentAggregate.getDocument().getType(),
                responseDocumentStatus);
        Document document = documentRepository.updateStatus(documentAggregate.getDocument().getId(), responseDocumentStatus);

        documentAggregate.setDocument(document);

        documentProcessorChain.runAfterSave(documentAggregate);

        monitoringUseCase.recordCreatedDocumentsRequest(documentAggregate.getDocument());

        return documentAggregate;
    }

    private void pushDocument(DocumentAggregate documentAggregate) {
        log.info("Pushing document '{}' to TaxI of type '{}'  ",
                documentAggregate.getDocument().getSid(), documentAggregate.getDocument().getType());
        if (DocumentType.SALES_CREDIT_NOTE.equals(documentAggregate.getDocument().getType()) ||
                DocumentType.PURCHASE_CREDIT_NOTE.equals(documentAggregate.getDocument().getType())) {
            taxiRequester.pushCreditNote(documentAggregate.getDocument().getOriginalDocument().getSid(), documentAggregate);
        } else {
            taxiRequester.pushInvoice(documentAggregate);
        }
    }

    private void pushStatement(List<DocumentAggregate> documentAggregateList) {
        log.info("Pushing statement '{}' to TaxI", documentAggregateList.get(0).getDocument().getSid());

        taxiRequester.pushStatement(documentAggregateList);
    }

    private void appendIssuerTemplate(DocumentAggregate documentAggregate) {
        if (documentAggregate.getDocument().getFlow().equals(DocumentFlow.RECEIVED)) {
            return;
        }
        boolean isOverrideEnabled = overallSettings.getOverrideIssuerSettings().isEnabled(
                documentAggregate.getDocument().getShop(),
                documentAggregate.getDocument().getCountry().getAlpha2()
        );
        if (isOverrideEnabled) {
            Optional<IssuerTemplate> issuerTemplate = issuerTemplateRepository.findByShopAndCountry(
                    documentAggregate.getDocument().getShop(),
                    documentAggregate.getDocument().getCountry()
            );
            if (issuerTemplate.isPresent()) {
                Document document = documentAggregate.getDocument()
                        .toBuilder()
                        .issuer(issuerTemplate.get().convertToIssuer())
                        .build();
                documentAggregate.setDocument(document);
            }
        }
    }

    private DocumentStatus documentStatusResponse() {
        return DocumentStatus.TAX_PENDING;
    }
}

package pt.jumia.services.bill.domain.exceptions.document;

import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.exceptions.CodedException;

import java.text.MessageFormat;

import static pt.jumia.services.bill.domain.exceptions.ErrorCode.DOCUMENT_STATUS_NOT_VALID_FOR_PRINTING;

public class CannotPreviewDocumentException extends CodedException {

    private static final long serialVersionUID = -7611708343328659778L;

    private CannotPreviewDocumentException(String message) {
        super(DOCUMENT_STATUS_NOT_VALID_FOR_PRINTING, message);
    }

    public static CannotPreviewDocumentException createInvalidDocumentStatusForPrinting(Document document) {
        return new CannotPreviewDocumentException(MessageFormat
                .format("Unable to print document [{0}] as it is in an invalid status [{1}]",
                        document.getId(), document.getStatus().name()));
    }
}

package pt.jumia.services.bill.domain.utils;

import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentLineWithNewDecimalFields;
import pt.jumia.services.bill.domain.entities.DocumentWithNewDecimalFields;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotalWithNewDecimalFields;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility class to help with the conversion between old and new decimal fields
 */
public class DecimalFieldsConverter {

    /**
     * Converts a Document to a DocumentWithNewDecimalFields, copying all decimal fields to the new fields
     * @param document The document to convert
     * @return A DocumentWithNewDecimalFields with all decimal fields copied to the new fields
     */
    public static DocumentWithNewDecimalFields convertDocumentToNewDecimalFields(Document document) {
        return DocumentWithNewDecimalFields.newDecimalFieldsBuilder()
                .document(document)
                .issuedToLocalCurrencyExchangeRate_new(document.getIssuedToLocalCurrencyExchangeRate())
                .totalAmount_new(document.getTotalAmount())
                .netAmount_new(document.getNetAmount())
                .taxAmount_new(document.getTaxAmount())
                .discountAmount_new(document.getDiscountAmount())
                .totalItemsDiscountAmount_new(document.getTotalItemsDiscountAmount())
                .extraDiscountAmount_new(document.getExtraDiscountAmount())
                .build();
    }

    /**
     * Converts a DocumentLine to a DocumentLineWithNewDecimalFields, copying all decimal fields to the new fields
     * @param documentLine The document line to convert
     * @return A DocumentLineWithNewDecimalFields with all decimal fields copied to the new fields
     */
    public static DocumentLineWithNewDecimalFields convertDocumentLineToNewDecimalFields(DocumentLine documentLine) {
        return DocumentLineWithNewDecimalFields.newDecimalFieldsBuilder()
                .documentLine(documentLine)
                .quantity_new(documentLine.getQuantity())
                .unitPrice_new(documentLine.getUnitPrice())
                .totalAmount_new(documentLine.getTotalAmount())
                .netAmount_new(documentLine.getNetAmount())
                .totalTaxAmount_new(documentLine.getTotalTaxAmount())
                .discountAmount_new(documentLine.getDiscountAmount())
                .discountRate_new(documentLine.getDiscountRate())
                .build();
    }

    /**
     * Converts a TaxCategoryTotal to a TaxCategoryTotalWithNewDecimalFields, copying all decimal fields to the new fields
     * @param taxCategoryTotal The tax category total to convert
     * @return A TaxCategoryTotalWithNewDecimalFields with all decimal fields copied to the new fields
     */
    public static TaxCategoryTotalWithNewDecimalFields convertTaxCategoryTotalToNewDecimalFields(TaxCategoryTotal taxCategoryTotal) {
        return TaxCategoryTotalWithNewDecimalFields.newDecimalFieldsBuilder()
                .taxCategoryTotal(taxCategoryTotal)
                .taxRate_new(taxCategoryTotal.getTaxRate())
                .taxFixedAmount_new(taxCategoryTotal.getTaxFixedAmount())
                .totalAmount_new(taxCategoryTotal.getTotalAmount())
                .netAmount_new(taxCategoryTotal.getNetAmount())
                .taxAmount_new(taxCategoryTotal.getTaxAmount())
                .build();
    }

    /**
     * Converts a DocumentAggregate to use the new decimal fields for all its components
     * @param documentAggregate The document aggregate to convert
     * @return A DocumentAggregate with all components using the new decimal fields
     */
    public static DocumentAggregate convertDocumentAggregateToNewDecimalFields(DocumentAggregate documentAggregate) {
        DocumentWithNewDecimalFields document = convertDocumentToNewDecimalFields(documentAggregate.getDocument());
        
        List<DocumentLineWithNewDecimalFields> lines = documentAggregate.getLines().stream()
                .map(DecimalFieldsConverter::convertDocumentLineToNewDecimalFields)
                .collect(Collectors.toList());
        
        List<TaxCategoryTotalWithNewDecimalFields> taxCategoryTotals = documentAggregate.getTaxCategoryTotals().stream()
                .map(DecimalFieldsConverter::convertTaxCategoryTotalToNewDecimalFields)
                .collect(Collectors.toList());
        
        return DocumentAggregate.builder()
                .document(document)
                .lines(lines.stream().map(line -> (DocumentLine) line).collect(Collectors.toList()))
                .taxCategoryTotals(taxCategoryTotals.stream().map(tax -> (TaxCategoryTotal) tax).collect(Collectors.toList()))
                .documentTransformations(documentAggregate.getDocumentTransformations())
                .taxAuthoritiesDetails(documentAggregate.getTaxAuthoritiesDetails())
                .build();
    }
}

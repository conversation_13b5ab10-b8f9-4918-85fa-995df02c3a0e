package pt.jumia.services.bill.domain.settings;

import lombok.Data;

@Data
public class KafkaSettings {
    public static final String KAFKA_PUSH_DOCUMENT_ENABLED = "kafka.document.push.enabled";

    private final OverallSettings.OverridableSetting<Boolean> pushDocumentEnabled;

    public KafkaSettings(OverallSettings overallSettings) {
        this.pushDocumentEnabled = overallSettings.createSetting(
                KAFKA_PUSH_DOCUMENT_ENABLED,
                false,
                Boolean::parseBoolean);
    }

    public boolean isPushDocumentEnabled(String shop, String countryCode) {
        return this.pushDocumentEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

package pt.jumia.services.bill.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class DailyReceivedDocumentsReport {
    private BusinessLine businessLine;
    private StatusesReport statusesReport;
    private ReviewReport reviewReport;

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusesReport {
        private LocalDateTime reportRangeStartDate;
        private LocalDateTime reportRangeEndDate;
        private int range;
        private Map<DocumentStatus, Long> statusesMap;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReviewReport {
        private LocalDateTime reportFirstRangeStartDate;
        private LocalDateTime reportSecondRangeStartDate;
        private LocalDateTime reportThirdRangeStartDate;
        private LocalDateTime reportRangeEndDate;
        private Long countOfUnreviewedDocumentsForTheFirstThird;
        private Long countOfUnreviewedDocumentsForTheSecondThird;
        private Long countOfUnreviewedDocumentsForTheThirdThird;
    }
}

package pt.jumia.services.bill.domain.exceptions;

import pt.jumia.services.bill.domain.entities.DocumentStatus;

/**
 * Exception thrown when there is a conflict on the flow execution.
 */
public class ConflictOperationException extends CodedException {

    private static final long serialVersionUID = 1889431907526361976L;

    public ConflictOperationException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public static ConflictOperationException createInvalidAcknowledgeStatus(DocumentStatus documentStatus) {
        return new ConflictOperationException(
                ErrorCode.INVALID_DOCUMENT_STATUS_ACK,
                String.format("Only error statuses can be acknowledge (got '%s').",
                        documentStatus == null ? null : documentStatus.name()));
    }

    public static ConflictOperationException createInvalidDocumentStatus(DocumentStatus documentStatus) {
        return new ConflictOperationException(
                ErrorCode.INVALID_DOCUMENT_STATUS,
                String.format("This document status is invalid, or it has no proper implementation (got '%s').",
                        documentStatus == null ? null : documentStatus.name()));
    }

    public static ConflictOperationException createInvalidCancelStatus(DocumentStatus documentStatus) {
        return new ConflictOperationException(
                ErrorCode.INVALID_DOCUMENT_STATUS_CANCEL,
                String.format("Only success status can be cancelled (got '%s').",
                        documentStatus == null ? null : documentStatus.name()));
    }

    public static ConflictOperationException createInvalidStatusForApproval(DocumentStatus documentStatus) {
        return new ConflictOperationException(
                ErrorCode.INVALID_DOCUMENT_STATUS,
                String.format("Only pending status can be approved (got '%s').",
                        documentStatus == null ? null : documentStatus.name()));
    }

    public static ConflictOperationException createInvalidDeclineRejectionStatus(DocumentStatus documentStatus) {
        return new ConflictOperationException(
                ErrorCode.INVALID_DECLINE_DOCUMENT_REJECTION_CANCEL,
                String.format("Decline Rejection can only be applied on documents of TAX Rejected (got '%s').",
                        documentStatus == null ? null : documentStatus.name()));
    }

    public static ConflictOperationException createInvalidRetryStatus(DocumentStatus documentStatus) {
        return new ConflictOperationException(
                ErrorCode.INVALID_DOCUMENT_STATUS_ACK,
                String.format("Only error statuses can be retried (got '%s').",
                        documentStatus == null ? null : documentStatus.name()));
    }
}

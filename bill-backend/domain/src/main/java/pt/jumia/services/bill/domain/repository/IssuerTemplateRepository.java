package pt.jumia.services.bill.domain.repository;

import com.neovisionaries.i18n.CountryCode;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;

import java.util.Optional;

public interface IssuerTemplateRepository {

    Optional<IssuerTemplate> findById(long id);
    IssuerTemplate save(IssuerTemplate issuerTemplate);
    Optional<IssuerTemplate> findByShopAndCountry(String shop, CountryCode country);
}

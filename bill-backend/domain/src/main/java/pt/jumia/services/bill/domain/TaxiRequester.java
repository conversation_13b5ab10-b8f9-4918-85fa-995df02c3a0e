package pt.jumia.services.bill.domain;

import pt.jumia.services.bill.domain.entities.CancelRequest;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.filters.DocumentRetryFilters;

import java.util.List;

public interface TaxiRequester {

    void pushInvoice(DocumentAggregate documentAggregate);

    void pushCreditNote(String originalDocumentSid, DocumentAggregate documentAggregate);

    void ackError(String entityId);

    void cancelDocument(CancelRequest cancelRequest);

    void declineDocumentRejection(String entityId);

    void rejectReceivedDocument(DocumentAggregate receivedDocument, String rejectionReason);

    void declineReceivedDocumentCancellation(DocumentAggregate receivedDocument);

    void approveReceivedDocument(DocumentAggregate receivedDocument);

    DocumentFile getTaxAuthoritiesPdf(String entityId);

    void bulkRetryDocuments(DocumentRetryFilters documentRetryFilters);

    void pushStatement(List<DocumentAggregate> documentAggregateList);

    void getReceivedDocumentsDetails(List<String> receivedDocumentsIds);
}

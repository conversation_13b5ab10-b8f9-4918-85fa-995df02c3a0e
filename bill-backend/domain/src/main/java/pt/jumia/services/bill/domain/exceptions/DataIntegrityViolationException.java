package pt.jumia.services.bill.domain.exceptions;

import org.apache.commons.lang3.exception.ExceptionUtils;

public class DataIntegrityViolationException extends CodedException {

    private static final long serialVersionUID = -8208631883311721066L;

    private DataIntegrityViolationException(String message, Exception e) {
        super(ErrorCode.DATA_INTEGRITY_VIOLATION, message, e);
    }

    public static DataIntegrityViolationException create(Exception e) {
        return new DataIntegrityViolationException(ExceptionUtils.getStackTrace(e), e);
    }

}

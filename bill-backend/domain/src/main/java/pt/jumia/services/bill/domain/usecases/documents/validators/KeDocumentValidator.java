package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.settings.ValidationSettings;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class KeDocumentValidator implements Validator {

    private final OverallSettings overallSettings;

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        if (DocumentFlow.RECEIVED.equals(aggregate.getDocument().getFlow())) {
            return List.of();
        }
        List<ValidationError> keErrors = new ArrayList<>();
        if (!"KE".equalsIgnoreCase(aggregate.getDocument().getCountry().getAlpha2())) {
            return keErrors;
        }
        ValidationSettings settings = overallSettings.getValidationSettings();
        String shop = aggregate.getDocument().getShop();
        String country = aggregate.getDocument().getCountry().getAlpha2();

        if (settings.getKeTransactionTypeValidation(shop, country) && (aggregate.getDocument()
                .getTransactionType() == null || aggregate.getDocument().getTransactionType().isEmpty())) {
            keErrors.add(new ValidationError(ErrorCode.INVALID_DOCUMENT_TRANSACTION_TYPE,
                    "No transaction type for document"));
        }

        if (settings.getKeTransactionProgressValidation(shop, country) && (aggregate.getDocument()
                .getTransactionProgress() == null || aggregate.getDocument().getTransactionProgress().isEmpty())) {
            keErrors.add(new ValidationError(ErrorCode.INVALID_DOCUMENT_TRANSACTION_PROGRESS,
                    "No transaction progress for document"));
        }

        if (settings.getKeLinesPackageUnitValidation(shop, country)) {
            keErrors.addAll(validatorDocumentLines(aggregate.getLines()));
        }
        return keErrors;
    }

    private List<ValidationError> validatorDocumentLines(List<DocumentLine> lines) {
        List<ValidationError> missingPackageUnit = new ArrayList<>();
        lines.forEach(line -> {
            if (line.getUnitOfPackage() == null || line.getUnitOfPackage().isEmpty()) {
                missingPackageUnit.add(new ValidationError(
                        ErrorCode.INVALID_LINE_PACKAGE,
                        String.format("No Package unit for item with code %s and name %s",
                                line.getItemCode(), line.getItemName())
                ));
            }
        });

        return missingPackageUnit;
    }
}

package pt.jumia.services.bill.domain.usecases.receiveddocuments;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.usecases.documents.UpdateDocumentUseCase;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeclineReceivedDocumentCancellationUseCase {

    private final TaxiRequester taxiRequester;
    private final UpdateDocumentUseCase updateDocumentUseCase;

    public Document execute(DocumentAggregate documentAggregate) {

        documentAggregate.getDocument().updateStatus(DocumentStatus.TAX_SUCCESS, documentAggregate.getDocument().getCountry());
        taxiRequester.declineReceivedDocumentCancellation(documentAggregate);

        return updateDocumentUseCase.execute(documentAggregate.getDocument().getId(),
                documentAggregate.getDocument(), true);
    }

}

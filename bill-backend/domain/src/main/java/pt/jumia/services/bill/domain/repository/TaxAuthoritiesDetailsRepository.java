package pt.jumia.services.bill.domain.repository;

import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;

import java.util.Optional;
import java.util.UUID;

public interface TaxAuthoritiesDetailsRepository {

    Optional<TaxAuthoritiesDetails> findById(UUID id);
    Optional<TaxAuthoritiesDetails> findByDocumentId(UUID id);
    TaxAuthoritiesDetails save(TaxAuthoritiesDetails taxAuthoritiesDetails);

}

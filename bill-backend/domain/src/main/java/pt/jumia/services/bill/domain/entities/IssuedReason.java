package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Value;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.UUID;

@Value
@Builder(toBuilder = true)
public class IssuedReason {

    UUID id;
    IssuedReasonCode code;
    String notes;
    String createdBy;
    LocalDateTime createdAt;

    public IssuedReason withoutDbField() {
        return this.toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public static class IssuedReasonBuilder {

        public IssuedReasonBuilder generateId() {
            id = UUID.nameUUIDFromBytes(this.generateStringId().getBytes(StandardCharsets.UTF_8));
            return this;
        }

        private String generateStringId() {
            return "IssuedReason{" +
                    "code=" + (code != null ? code.name() : null) +
                    ", notes='" + notes + '\'' +
                    '}';
        }

    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.List;

@Component
@RequiredArgsConstructor
public class DocumentDuplicateValidator implements Validator {

    private final DocumentRepository documentRepository;

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        if (documentRepository.existsBySidInErrors(aggregate.getDocument().getSid())) {
            return List.of(new ValidationError(
                    ErrorCode.DUPLICATE_DOCUMENT,
                    String.format("Repeated document with sid '%s'", aggregate.getDocument().getSid())
            ));
        }

        return List.of();
    }
}

package pt.jumia.services.bill.domain.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.Builder;
import lombok.Data;

@Data
@Builder(toBuilder = true)
public class Address {

    private CountryCode country;
    private String region;
    private String city;
    private String street;
    private String buildingNumber;
    private String floor;
    private String postalCode;
    private String additionalInformation;

    protected String generateStringId() {
        return "Address{" +
                "country='" + (country != null ? country.getAlpha2() : null) + '\'' +
                ", region='" + region + '\'' +
                ", city='" + city + '\'' +
                ", street='" + street + '\'' +
                ", buildingNumber='" + buildingNumber + '\'' +
                ", floor='" + floor + '\'' +
                ", postalCode='" + postalCode + '\'' +
                ", additionalInformation='" + additionalInformation + '\'' +
                '}';
    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentWithNewDecimalFields;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.utils.DecimalFieldsConverter;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

@Component
@RequiredArgsConstructor
@Slf4j
public class UpdateDocumentUseCase {

    private final DocumentRepository documentRepository;

    public Document execute(UUID id, Document document, boolean changeUpdateData) {
        // Convert document to use new decimal fields if it's not already using them
        if (!(document instanceof DocumentWithNewDecimalFields)) {
            document = DecimalFieldsConverter.convertDocumentToNewDecimalFields(document);
        }
        
        return documentRepository.update(id, document.toBuilder()
                .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
                .updatedBy(RequestContext.getUsername())
                .build(), changeUpdateData);
    }
}

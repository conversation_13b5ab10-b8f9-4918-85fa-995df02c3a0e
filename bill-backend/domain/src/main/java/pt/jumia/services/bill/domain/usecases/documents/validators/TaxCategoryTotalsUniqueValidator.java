package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class TaxCategoryTotalsUniqueValidator implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        List<TaxCategoryTotal> duplicates = aggregate.getTaxCategoryTotals().stream()
                .collect(Collectors.groupingBy(taxCategoryTotal -> taxCategoryTotal.getDocument().getId().toString() +
                                taxCategoryTotal.getTaxCategory().toString() +
                                (taxCategoryTotal.getTaxFixedAmount() != null ?
                                        taxCategoryTotal.getTaxFixedAmount().toString() : "") +
                                (taxCategoryTotal.getTaxRate() != null ? taxCategoryTotal.getTaxRate().toString() : "")
                        , Collectors.toList()))
                .values()
                .stream()
                .filter(i -> i.size() > 1)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        if (duplicates.size() > 0) {
            return List.of(new ValidationError(
                    ErrorCode.DUPLICATE_ENTRY,
                    String.format("Tax category should unique with tax fixed amount and tax rate '%s'", duplicates.stream()
                            .map(taxCategoryTotal -> String.format(
                                    "Tax fixed amount: (%s) and Tax rate: (%s) for Tax category (%s)",
                                    taxCategoryTotal.getTaxFixedAmount() != null ?
                                            taxCategoryTotal.getTaxFixedAmount().toString() : "null",
                                    taxCategoryTotal.getTaxRate() != null ? taxCategoryTotal.getTaxRate().toString() : "null",
                                    taxCategoryTotal.getTaxCategory()))
                            .collect(Collectors.joining(" is duplicate with ")))
            ));
        }

        return List.of();
    }
}

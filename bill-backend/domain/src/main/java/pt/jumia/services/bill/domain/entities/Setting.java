package pt.jumia.services.bill.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;
import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class Setting {

    public enum Type {
        DEFAULT, OVERRIDE
    }

    @Nullable
    private Long id;

    private String property;

    private Type type;

    @Nullable
    private String overrideKey;

    @Nullable
    private String description;

    private String value;

    @Nullable
    private LocalDateTime createdAt;

    private String createdBy;

    @Nullable
    private LocalDateTime updatedAt;

    private String updatedBy;

    public Setting withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdBy(null)
                .createdAt(null)
                .updatedBy(null)
                .updatedAt(null)
                .build();
    }

    public boolean isTypeOverride() {
        return Type.OVERRIDE.equals(type);
    }

    public boolean isOverrideKeyEmpty() {
        return StringUtils.isBlank(overrideKey);
    }
}

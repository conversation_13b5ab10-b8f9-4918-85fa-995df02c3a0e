package pt.jumia.services.bill.domain.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Getter
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@Builder(toBuilder = true)
public class User {

    private final String username;

    @Builder.Default
    private final boolean canAccess = false;

    @Builder.Default
    private final boolean canManageSettings = false;

    @Builder.Default
    private final Map<CountryCode, List<String>>  countriesPermissionsList = Collections.emptyMap();

}


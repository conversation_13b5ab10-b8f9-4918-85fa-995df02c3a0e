package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.SettingNotActiveException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.settings.OverallSettings;

@Component
@RequiredArgsConstructor
public class DeclineDocumentRejectionUseCase {

    private final DocumentRepository documentRepository;
    private final TaxiRequester taxiRequester;
    private final OverallSettings overallSettings;

    public Document execute(Document document) {
        if (DocumentStatus.TAX_SUCCESS.equals(document.getStatus())) {
            return document;
        } else if (!DocumentStatus.TAX_REJECTED.equals(document.getStatus())) {
            throw ConflictOperationException.createInvalidDeclineRejectionStatus(document.getStatus());
        }
        checkDeclineRejectionSettingsEnabledOrThrow(document);

        taxiRequester.declineDocumentRejection(document.getId().toString());

        return documentRepository.save(document.toBuilder().status(DocumentStatus.TAX_SUCCESS).build());
    }

    private void checkDeclineRejectionSettingsEnabledOrThrow(Document document) {
        DocumentType type = document.getType();
        boolean isEnabled = false;
        switch (type) {
            case SALES_INVOICE:
                isEnabled = this.overallSettings.getRejectionSettings().getInvoiceRejectionDeclineEnabled(document.getShop(),
                        document.getCountry().name());
                break;
            case SALES_CREDIT_NOTE:
                isEnabled = this.overallSettings.getRejectionSettings().getCreditNoteRejectionDeclineEnabled(document.getShop(),
                        document.getCountry().name());
                break;
            case SALES_CREDIT_MEMO:
                isEnabled = this.overallSettings.getRejectionSettings().getCreditMemoRejectionDeclineEnabled(document.getShop(),
                        document.getCountry().name());
                break;
            case SALES_DEBIT_NOTE:
                isEnabled = this.overallSettings.getRejectionSettings().getDebitNoteRejectionDeclineEnabled(document.getShop(),
                        document.getCountry().name());
                break;
            case PURCHASE_INVOICE:
                isEnabled = this.overallSettings.getRejectionSettings().getPurchaseInvoiceRejectionDeclineEnabled(document.getShop(),
                        document.getCountry().name());
                break;
            case PURCHASE_CREDIT_NOTE:
                isEnabled = this.overallSettings.getRejectionSettings().getPurchaseCreditNoteRejectionDeclineEnabled(document.getShop(),
                        document.getCountry().name());
                break;
            case PURCHASE_DEBIT_NOTE:
                isEnabled = this.overallSettings.getRejectionSettings().getPurchaseDebitNoteRejectionDeclineEnabled(document.getShop(),
                        document.getCountry().name());
                break;
        }

        if (!isEnabled) {
            throw SettingNotActiveException.settingNotActiveException(
                    String.format("Decline Rejection for Document Type {}", type.name())
            );
        }
    }

}

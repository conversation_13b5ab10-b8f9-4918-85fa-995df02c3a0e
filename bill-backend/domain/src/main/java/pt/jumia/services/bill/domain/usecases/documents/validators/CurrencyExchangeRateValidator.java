package pt.jumia.services.bill.domain.usecases.documents.validators;

import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

@Component
@RequiredArgsConstructor
public class CurrencyExchangeRateValidator implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        List<ValidationError> errors = new ArrayList<>();
        Currency currentCountryCurrency = Currency.getInstance(new Locale("en",
                String.valueOf(aggregate.getDocument().getCountry())));
        String currencySymbolValue = aggregate.getDocument().getCurrency().getCurrencyCode();
        BigDecimal currencyExchangeRate = aggregate.getDocument().getIssuedToLocalCurrencyExchangeRate();

        if (!currentCountryCurrency.getSymbol().equals(currencySymbolValue)) {
            if (currencyExchangeRate == null) {
                errors.add(new ValidationError(
                        ErrorCode.INVALID_CURRENCY_EXCHANGE_RATE,
                        "Currency exchange rate must be provided"
                ));
            } else if (currencyExchangeRate.compareTo(BigDecimal.ZERO) <= 0) {
                if (!aggregate.getDocument().getCountry().equals(CountryCode.UG)) {
                    errors.add(new ValidationError(
                            ErrorCode.INVALID_CURRENCY_EXCHANGE_RATE,
                            "Currency exchange rate should be greater than zero"
                    ));
                }
            }
        }
        return errors;
    }
}

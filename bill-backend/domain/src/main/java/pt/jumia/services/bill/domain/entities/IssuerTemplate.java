package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Value;

import javax.annotation.Nullable;
import java.time.LocalDateTime;

@Value
@Builder(toBuilder = true)
public class IssuerTemplate {

    @Nullable
    Long id;
    IssuerType type;
    String shop;

    //
    // Identification
    //

    String legalName;
    String name;
    String taxIdentificationNumber;
    String businessRegistrationNumber;
    String branch;

    //
    // Contact
    //

    Address address;
    String email;
    String mobilePhone;
    String linePhone;

    //
    // Audit information
    //

    LocalDateTime createdAt;
    String createdBy;
    LocalDateTime updatedAt;
    String updatedBy;

    public Issuer convertToIssuer() {
        return Issuer.builder()
                .type(this.type)
                .legalName(this.legalName)
                .name(this.name)
                .taxIdentificationNumber(this.taxIdentificationNumber)
                .businessRegistrationNumber(this.businessRegistrationNumber)
                .branch(this.branch)
                .address(this.address)
                .email(this.email)
                .mobilePhone(this.mobilePhone)
                .linePhone(this.linePhone)
                .createdAt(this.createdAt)
                .createdBy(this.createdBy)
                .generateId()
                .build();
    }

    public IssuerTemplate withoutDbField() {
        return this.toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .updatedBy(null)
                .build();
    }
}

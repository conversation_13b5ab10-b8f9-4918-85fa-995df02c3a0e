package pt.jumia.services.bill.domain.entities;

import com.neovisionaries.i18n.CountryCode;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.exceptions.document.InvalidDocumentTransitionException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class Document {

    public enum Details {
        ISSUER,
        RECEIVER,
        TAX_AUTHORITIES_DETAILS,
        DELIVERY,
        PAYMENT,
        ISSUED_REASON,
        ORIGINAL_DOCUMENT,
        OTHER_SPLIT_DOCUMENT,
        DOCUMENT_TRANSFORMATION
    }

    public enum SortingFields {
        CREATED_AT,
        UPDATED_AT,
        RECEIVED_DATE,
        ISSUE_DATE
    }

    //
    // Internal fields
    //

    private UUID id;
    @Builder.Default
    private DocumentStatus status = DocumentStatus.NEW;
    private String judgeSid;

    //
    // General information
    //

    private CountryCode country;
    private String shop;
    private DocumentType type;
    private String sid;
    // https://confluence.jumia.com/display/AFRFINIT/1.+Invoice+Module
    private DocumentFlow flow;
    private String generatedBy;
    private String referenceNumber;
    private String purchaseReferenceNumber;
    private LocalDateTime issuedDate;
    private Currency currency;
    private BigDecimal issuedToLocalCurrencyExchangeRate;
    private Receiver receiver;
    private Issuer issuer;
    private DocumentId originalDocument;
    private IssuedReason issuedReason;
    private String notes;
    private LocalDateTime receivedDate;


    //
    // Summary
    //

    private int lineCount;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalItemsDiscountAmount;
    private BigDecimal extraDiscountAmount;

    //
    // Audit information
    //

    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    private PaymentDetails payment;
    private DeliveryDetails delivery;

    private String sourceType;
    private String postingGroup;
    private OtherSplitDocument otherSplitDocument;

    private DocumentPossibleOperations documentPossibleOperations;

    @Builder.Default
    private Boolean reviewed = false;
    private LocalDateTime reviewedAt;
    private String reviewedBy;

    private String transactionType;

    private String transactionProgress;

    public Document withoutDbField() {
        return this.toBuilder()
                .id(null)
                .status(null)
                .issuer(issuer != null ? issuer.withoutDbField() : null)
                .receiver(receiver != null ? receiver.withoutDbField() : null)
                .issuedReason(issuedReason != null ? issuedReason.withoutDbField() : null)
                .originalDocument(originalDocument != null ? originalDocument.withoutDbField() : null)
                .otherSplitDocument(otherSplitDocument != null ? otherSplitDocument.withoutDbField() :
                        null)
                .payment(payment != null ? payment.withoutDbField() : null)
                .delivery(delivery != null ? delivery.withoutDbField() : null)
                .issuedToLocalCurrencyExchangeRate(issuedToLocalCurrencyExchangeRate != null ?
                        issuedToLocalCurrencyExchangeRate.setScale(6, RoundingMode.CEILING) : null)
                .totalAmount(totalAmount != null ? totalAmount.setScale(6, RoundingMode.CEILING) : null)
                .netAmount(netAmount != null ? netAmount.setScale(6, RoundingMode.CEILING) : null)
                .taxAmount(taxAmount != null ? taxAmount.setScale(6, RoundingMode.CEILING) : null)
                .discountAmount(discountAmount != null ?
                        discountAmount.setScale(6, RoundingMode.CEILING) : null)
                .totalItemsDiscountAmount(totalItemsDiscountAmount != null ?
                        totalItemsDiscountAmount.setScale(6, RoundingMode.CEILING) : null)
                .extraDiscountAmount(extraDiscountAmount != null ?
                        extraDiscountAmount.setScale(6, RoundingMode.CEILING) : null)
                .createdAt(null)
                .createdBy(null)
                .updatedAt(null)
                .updatedBy(null)
                .build();
    }

    public static class DocumentBuilder {

        public DocumentBuilder generateId() {
            id = UUID.randomUUID();
            return this;
        }

    }

    public void convertAmountsToPositiveValue() {
        this.totalAmount = this.totalAmount != null && this.totalAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalAmount.negate() : this.totalAmount;
        this.netAmount = this.netAmount != null && this.netAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.netAmount.negate() : this.netAmount;
        this.taxAmount = this.taxAmount != null && this.taxAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.taxAmount.negate() : this.taxAmount;
        this.discountAmount = this.discountAmount != null && this.discountAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.discountAmount.negate() : this.discountAmount;
        this.totalItemsDiscountAmount = this.totalItemsDiscountAmount != null && this.totalItemsDiscountAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalItemsDiscountAmount.negate() :
                this.totalItemsDiscountAmount;
        this.extraDiscountAmount = this.extraDiscountAmount != null && this.extraDiscountAmount.compareTo(BigDecimal.ZERO) < 0 ?
                this.extraDiscountAmount.negate() : this.extraDiscountAmount;
    }

    public boolean isPositive() {
        return this.totalAmount.compareTo(BigDecimal.ZERO) >= 0;
    }

    public Document updateStatus(DocumentStatus newStatus, CountryCode country) {
        if (this.getStatus().canTransitionTo(newStatus, country)) {
            this.reviewed = this.status.equals(newStatus);
            this.status = newStatus;
            return this;
        }

        throw InvalidDocumentTransitionException.createInvalidReceivedDocumentStatusTransition(
                this,
                newStatus
        );
    }

}

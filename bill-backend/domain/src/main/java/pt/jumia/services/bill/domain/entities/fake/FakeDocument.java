package pt.jumia.services.bill.domain.entities.fake;

import com.neovisionaries.i18n.CountryCode;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import java.time.LocalDateTime;
import java.math.BigDecimal;
import java.util.Currency;
import java.util.UUID;

public interface FakeDocument {



    Document DOCUMENT_1 = Document.builder()
            .id(UUID.randomUUID())
            .status(DocumentStatus.NEW)
            .judgeSid(null)
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("1234")
            .flow(DocumentFlow.RETAIL)
            .generatedBy("NAV")
            .issuedDate(LocalDateTime.now())
            .currency(Currency.getInstance("UGX"))
            .receiver(Receiver.builder()
                    .type(ReceiverType.CUSTOMER)
                    .legalName("Some receiver")
                    .address(Address.builder()
                            .street("Testing street")
                            .build())
                    .generateId()
                    .build())
            .issuer(Issuer.builder()
                    .type(IssuerType.BUSINESS)
                    .legalName("Some issuer")
                    .email("<EMAIL>")
                    .address(Address.builder()
                            .street("Testing street")
                            .build())
                    .generateId()
                    .build())
            .lineCount(1)
            .totalAmount(new BigDecimal("120.00"))
            .netAmount(new BigDecimal("100.00"))
            .taxAmount(new BigDecimal("20.00"))
            .discountAmount(new BigDecimal("0.00"))
            .originalDocument(null)
            .notes(null)
            .generateId()
            .build();
}

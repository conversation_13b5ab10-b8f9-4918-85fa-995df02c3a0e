package pt.jumia.services.bill.domain.usecases.documents.processors;

import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;

import java.util.List;

@Component
public class DocumentProcessorChain {

    private final List<Processor> beforeSaveProcessors;
    private final List<Processor> afterSaveProcessors;

    public DocumentProcessorChain(
            PopulateOriginalDocumentProcessor populateOriginalDocumentProcessor,
            AutoAckDocumentProcessor autoAckDocumentProcessor,
            MoveErrorDocumentsToRetriedProcessor moveErrorDocumentsToRetriedProcessor,
            AfromsProcessor afromsProcessor
    ) {
        this.beforeSaveProcessors = List.of(
                populateOriginalDocumentProcessor,
                afromsProcessor,
                autoAckDocumentProcessor);
        this.afterSaveProcessors = List.of(moveErrorDocumentsToRetriedProcessor);
    }

    public void runBeforeSave(DocumentAggregate aggregate) {
        beforeSaveProcessors.forEach(processor -> processor.process(aggregate));
    }

    public void runAfterSave(DocumentAggregate aggregate) {
        afterSaveProcessors.forEach(processor -> processor.process(aggregate));
    }
}

package pt.jumia.services.bill.domain.usecases.documents;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.JudgeRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.GhDocPdfRequestPayloadInfo;
import pt.jumia.services.bill.domain.entities.dtos.OverrideFields;

import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class GenerateJudgeDocumentUseCase {
    private final JudgeRequester judgeRequester;
    private final DocumentRepository documentRepository;

    public DocumentAggregate execute(DocumentAggregate documentAggregate) throws JsonProcessingException {
        log.info("Generating PDF for document '{}'", documentAggregate.getDocument().getSid());
        String judgeSid = judgeRequester.generateNewDocumentByCode(documentAggregate);
        return returnUpdatedDocument(documentAggregate, judgeSid);
    }
    public DocumentAggregate executeWithAllDocFields(DocumentAggregate documentAggregate, GhDocPdfRequestPayloadInfo docPdfFields) {
        log.info("Generating Gh PDF for document '{}'", documentAggregate.getDocument().getSid());
        String judgeSid = judgeRequester.generateNewDocumentByCodeWithAllDocFields(documentAggregate, docPdfFields);
        return returnUpdatedDocument(documentAggregate, judgeSid);
    }
    public DocumentAggregate execute(DocumentAggregate documentAggregate,
                                     OverrideFields overrideFields) {
        log.info("Generating PDF with override fields for document  '{}'", documentAggregate.getDocument().getSid());

        return returnUpdatedDocument(documentAggregate, returnAddOverridesAndGenerateJudgeSid(
                documentAggregate, overrideFields, null
        ));
    }



    public DocumentAggregate executeWithAllDocFields(DocumentAggregate documentAggregate,
                                                     OverrideFields overrideFields, GhDocPdfRequestPayloadInfo docPdfFields) {
        log.info("Generating PDF with override fields for document '{}'", documentAggregate.getDocument().getSid());
        return returnUpdatedDocument(documentAggregate, returnAddOverridesAndGenerateJudgeSid(
                documentAggregate, overrideFields, docPdfFields
        ));
    }

    private String returnAddOverridesAndGenerateJudgeSid(DocumentAggregate documentAggregate,
                                                         OverrideFields overrideFields, GhDocPdfRequestPayloadInfo docPdfFields) {
        DocumentAggregate documentAggregateWithOverrrides = DocumentAggregate.builder()
                .document(documentAggregate.getDocument())
                .lines(documentAggregate.getLines())
                .taxAuthoritiesDetails(documentAggregate.getTaxAuthoritiesDetails())
                .build();
        if (overrideFields.getBuyerLegalName() != null) {

            documentAggregateWithOverrrides.setDocument(documentAggregateWithOverrrides
                    .getDocument()
                    .toBuilder()
                    .receiver(documentAggregateWithOverrrides.
                            getDocument()
                            .getReceiver()
                            .toBuilder()
                            .name(overrideFields.getBuyerLegalName())
                            .build())
                    .build());
        }

        if (overrideFields.getInvoiceNumberInsideTaxAuthorities() != null) {
            documentAggregateWithOverrrides.setTaxAuthoritiesDetails(documentAggregateWithOverrrides
                    .getTaxAuthoritiesDetails()
                    .toBuilder()
                    .taxDocumentNumber(overrideFields.getInvoiceNumberInsideTaxAuthorities())
                    .build());
        }

        if (overrideFields.getTaxCategoriesMap() != null) {

            List<DocumentLine> updatedLines = new ArrayList<>();
            documentAggregate.getLines().forEach(documentLine -> {
                DocumentLine updatedLine = documentLine;

                StringBuilder updatedTaxCategoryName = new StringBuilder();

                documentLine.getAppliedTaxes().forEach(appliedTax -> {
                    if (overrideFields.getTaxCategoriesMap().containsKey(appliedTax.getTaxCategory().name())) {
                        updatedTaxCategoryName.append(overrideFields
                                .getTaxCategoriesMap()
                                .get(appliedTax.getTaxCategory().name()) + ". ");
                    }
                });

                if (updatedTaxCategoryName.length() != 0) {
                    updatedLines.add(documentLine.toBuilder()
                            .category(documentLine
                                    .getCategory()
                                    .toBuilder()
                                    .name(updatedTaxCategoryName.toString())
                                    .build())
                            .build());
                } else {
                    updatedLines.add(documentLine);
                }

            });
            documentAggregateWithOverrrides.setLines(updatedLines);
        }

        return docPdfFields != null ?
                judgeRequester.generateNewDocumentByCodeWithAllDocFields(documentAggregate, docPdfFields) :
                judgeRequester.generateNewDocumentByCode(documentAggregate);
    }

    private DocumentAggregate returnUpdatedDocument(DocumentAggregate documentAggregate,
                                                    String judgeSid) {
        Document documentUpdates = documentRepository.save(documentAggregate.getDocument()
                .toBuilder()
                .judgeSid(judgeSid)
                .build());

        return documentAggregate.toBuilder().document(documentUpdates).build();
    }
}

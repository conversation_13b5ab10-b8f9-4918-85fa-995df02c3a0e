package pt.jumia.services.bill.domain.entities;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class GhDocSubmissionInfoPayload {
    protected UUID id;
    protected String country;
    protected String shop;
    protected DocumentType type;
    protected String sid;
    protected String currency;
    protected String generatedBy;
    protected String referenceNumber;
    protected BigDecimal issuedToLocalCurrencyExchangeRate;
    protected Issuer issuer;
    protected Receiver receiver;
    protected String purchaseReferenceNumber;
    protected LocalDateTime issuedDate;
    protected LocalDateTime receivedDate;
    protected String notes;
    protected int lineCount;
    protected List<Line> lines;
    protected BigDecimal totalAmount;
    protected BigDecimal netAmount;
    protected BigDecimal taxAmount;
    protected BigDecimal discountAmount;
    protected BigDecimal totalItemsDiscountAmount;
    protected BigDecimal extraDiscountAmount;
    protected List<TaxCategoryTotal> taxCategoryTotals;
    protected Payment payment;
    protected Delivery delivery;
    protected String status;
    protected String transactionType;
    protected String transactionProgress;
    protected TaxAuthoritySubmissionDetails taxAuthoritySubmissionDetails;


    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Line {

        protected UUID id;
        protected int position;
        protected BigDecimal quantity;
        protected String unitOfMeasure;
        protected String itemCode;
        protected String itemName;
        protected String itemType;
        protected Category category;
        protected BigDecimal unitPrice;
        protected BigDecimal totalAmount;
        protected BigDecimal netAmount;
        protected BigDecimal netAmountIssued;
        protected BigDecimal totalTaxAmount;
        protected List<AppliedTax> appliedTaxes;
        protected Discount discount;
        protected BigDecimal valueDifference;
        protected String unitOfPackage;
        protected BigDecimal itemsDiscount;

        public boolean hasDiscount() {
            return this.discount != null && this.discount.hasDiscount();
        }

        public boolean isDeemed() {
            return false; // TODO: check this later. Returning true because we're currently sending deemed flag as 1 in UG script implementation.
        }

        public boolean isExciseDuty() {
            return false; // TODO: check this later. Returning false because we're currently  sending excise flag as 2 in UG script implementation.
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Category {

            private String sid;
            private String name;
            private String taxAuthorityCode;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AppliedTax {

            protected String taxCategory;
            protected BigDecimal taxRate;
            protected BigDecimal taxFixedAmount;
            protected BigDecimal taxAmount;
        }

        @Data
        @Builder(toBuilder = true)
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Discount {
            protected BigDecimal amount;
            protected BigDecimal rate;

            public boolean hasDiscount() {
                return this.amount != null && this.amount.compareTo(BigDecimal.ZERO) > 0;
            }
        }
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaxCategoryTotal {

        protected UUID id;
        protected TaxCategory taxCategory;
        protected BigDecimal taxRate;
        protected BigDecimal taxFixedAmount;
        protected BigDecimal totalAmount;
        protected BigDecimal netAmount;
        protected BigDecimal taxAmount;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Payment {
        protected String bankName;
        protected String bankAddress;
        protected String bankAccountNo;
        protected String bankAccountIBAN;
        protected String swiftCode;
        protected String terms;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Delivery {
        protected String approach;
        protected String packaging;
        protected String dateValidity;
        protected String exportPort;
        protected String countryOfOrigin;
        protected BigDecimal grossWeight;
        protected BigDecimal netWeight;
        protected String terms;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaxAuthoritySubmissionDetails {
        protected Long sequentialNumber;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Receiver {

        protected UUID id;
        protected String type;
        protected String legalName;
        protected String name;
        protected String nationalIdentificationNumber;
        protected String taxIdentificationNumber;
        protected String businessRegistrationNumber;
        protected Address address;
        protected String email;
        protected String mobilePhone;
        protected String linePhone;

        protected String createdAt;
        protected String createdBy;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Address {

        private String country;
        private String region;
        private String city;
        private String street;
        private String buildingNumber;
        private String floor;
        private String postalCode;
        private String additionalInformation;
    }

    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Issuer {

        protected UUID id;
        protected String type;
        protected String legalName;
        protected String name;
        protected String taxIdentificationNumber;
        protected String businessRegistrationNumber;
        protected String branch;
        protected Address address;
        protected String email;
        protected String mobilePhone;
        protected String linePhone;
        protected String createdAt;
        protected String createdBy;

    }

}

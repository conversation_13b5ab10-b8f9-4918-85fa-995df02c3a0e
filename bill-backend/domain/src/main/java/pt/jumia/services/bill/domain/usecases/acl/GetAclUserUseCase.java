package pt.jumia.services.bill.domain.usecases.acl;

import com.neovisionaries.i18n.CountryCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.AccessController;
import pt.jumia.services.bill.domain.Permissions;
import pt.jumia.services.bill.domain.entities.User;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;
import pt.jumia.services.bill.domain.properties.AclProperties;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Use case responsible for getting the user, which includes his permissions (fetch from ACL Service)
 */
@Component
@Slf4j
public class GetAclUserUseCase {

    private static final String APPLICATION_KEY = "APPLICATION";
    private static final String COUNTRY_KEY = "COUNTRY";

    private final AccessController accessController;
    private final boolean skipAcl;
    private final String appCode;

    @Autowired
    public GetAclUserUseCase(AccessController accessController, AclProperties aclProperties) {
        this.accessController = accessController;
        this.skipAcl = aclProperties.isSkip();
        this.appCode = aclProperties.getAppName();
    }

    public User execute(RequestUser requestUser) {
        if (skipAcl) {
            return mockUserWithAllPermissions();
        }
        return getUserOrThrow(requestUser);
    }

    private User getUserOrThrow(RequestUser requestUser) {
        try {
            return getUser(requestUser);
        } catch (AclErrorException e) {
            log.error("Error retrieving user: {}", ExceptionUtils.getStackTrace(e));
            throw UserForbiddenException.createFromAclErrorException(e);
        }
    }

    private User getUser(RequestUser requestUser) throws AclErrorException {
        Map<String, Map<String, List<String>>> permissionsInApp = accessController.getPermissions(requestUser);

        Map<String, List<String>> permissionsInCountriesAsString = CollectionUtils.isEmpty(permissionsInApp)  ?
                Collections.emptyMap() : permissionsInApp.get(COUNTRY_KEY);
        Map<CountryCode, List<String>> permissionsInCountries = CollectionUtils.isEmpty(permissionsInCountriesAsString) ?
                Collections.emptyMap() :
                permissionsInCountriesAsString.entrySet().stream()
                        .filter(entry ->
                                EnumUtils.isValidEnum(CountryCode.class, entry.getKey()))
                        .collect(Collectors.toMap(e -> CountryCode.valueOf(e.getKey()), Map.Entry::getValue));

        List<String> appPermissions = getAppPermissions(permissionsInApp);

        return User.builder()
                .username(requestUser.getUsername())
                .canAccess(appPermissions.contains(Permissions.CAN_ACCESS))
                .canManageSettings(appPermissions.contains(Permissions.MANAGE_SETTINGS))
                .countriesPermissionsList(permissionsInCountries)
                .build();
    }

    private List<String> getAppPermissions(Map<String, Map<String, List<String>>> permissionsInApp) {
        if (permissionsInApp != null && permissionsInApp.containsKey(APPLICATION_KEY)) {
            Map<String, List<String>> applications = permissionsInApp.get(APPLICATION_KEY);
            if (applications.containsKey(appCode)) {
                return applications.get(appCode);
            }
        }
        return Collections.emptyList();
    }

    private User mockUserWithAllPermissions() {
        return User.builder()
                .username("acl.skipped.user")
                .canAccess(true)
                .build();
    }
}

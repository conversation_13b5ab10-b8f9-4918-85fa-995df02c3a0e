package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.CancelRequest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.SettingNotActiveException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;
import pt.jumia.services.bill.domain.settings.CancellationSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;

import java.util.Locale;

@Component
@RequiredArgsConstructor
public class CancelDocumentUseCase {

    private final DocumentRepository documentRepository;
    private final TaxiRequester taxiRequester;
    private final OverallSettings overallSettings;

    public Document execute(Document document, CancelRequest cancelRequest) {

        if (DocumentStatus.TAX_CANCELLED.equals(document.getStatus())) {
            return document;
        } else if (!DocumentStatus.TAX_SUCCESS.equals(document.getStatus())) {
            throw ConflictOperationException.createInvalidCancelStatus(document.getStatus());
        }

        checkCancelEnabled(document);
        taxiRequester.cancelDocument(cancelRequest);

        return documentRepository.save(document.toBuilder().status(DocumentStatus.TAX_CANCELLED).build());
    }

    private void checkCancelEnabled(Document document) {
        DocumentType type = document.getType();
        boolean isEnabled = false;
        String msg = "";

        switch (type) {
            case SALES_INVOICE:
                isEnabled = this.overallSettings.getCancellationSettings().getInvoiceCancellationEnabled(document.getShop().toLowerCase(Locale.US),
                        document.getCountry().getAlpha2());
                msg = CancellationSettings.INVOICE_CANCELLATION_ENABLED;
                break;
            case SALES_CREDIT_NOTE:
                isEnabled = this.overallSettings.getCancellationSettings().getCreditNoteCancellationEnabled(document.getShop().toLowerCase(Locale.US),
                        document.getCountry().getAlpha2());
                msg = CancellationSettings.CREDIT_NOTE_CANCELLATION_ENABLED;
                break;
            case SALES_CREDIT_MEMO:
                isEnabled = this.overallSettings.getCancellationSettings().getCreditMemoCancellationEnabled(document.getShop().toLowerCase(Locale.US),
                        document.getCountry().getAlpha2());
                msg = CancellationSettings.CREDIT_MEMO_CANCELLATION_ENABLED;
                break;
            case SALES_DEBIT_NOTE:
                isEnabled = this.overallSettings.getCancellationSettings().getDebitNoteCancellationEnabled(document.getShop().toLowerCase(Locale.US),
                        document.getCountry().getAlpha2());
                msg = CancellationSettings.DEBIT_NOTE_CANCELLATION_ENABLED;
                break;
            case PURCHASE_INVOICE:
                isEnabled = this.overallSettings.getCancellationSettings().getPurchaseInvoiceCancellationEnabled(
                        document.getShop().toLowerCase(Locale.US),
                        document.getCountry().getAlpha2());
                msg = CancellationSettings.PURCHASE_INVOICE_CANCELLATION_ENABLED;
                break;
            case PURCHASE_CREDIT_NOTE:
                isEnabled = this.overallSettings.getCancellationSettings().getPurchaseCreditNoteCancellationEnabled(
                        document.getShop().toLowerCase(Locale.US),
                        document.getCountry().getAlpha2());
                msg = CancellationSettings.PURCHASE_CREDIT_NOTE_CANCELLATION_ENABLED;
                break;
            case PURCHASE_DEBIT_NOTE:
                isEnabled = this.overallSettings.getCancellationSettings().getPurchaseDebitNoteCancellationEnabled(
                        document.getShop().toLowerCase(Locale.US),
                        document.getCountry().getAlpha2());
                msg = CancellationSettings.PURCHASE_DEBIT_NOTE_CANCELLATION_ENABLED;
                break;
        }

        if (!isEnabled) {
            throw SettingNotActiveException.settingNotActiveException(msg);
        }
    }
}

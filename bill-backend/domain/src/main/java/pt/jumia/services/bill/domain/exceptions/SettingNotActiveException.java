package pt.jumia.services.bill.domain.exceptions;

public class SettingNotActiveException extends CodedException {

    private static final long serialVersionUID = 1889431907526361976L;

    public SettingNotActiveException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public static SettingNotActiveException settingNotActiveException(String setting) {
        return new SettingNotActiveException(
                ErrorCode.SETTING_NOT_ACTIVE,
                String.format("Setting for '%s' is disabled.",
                        setting));
    }
}

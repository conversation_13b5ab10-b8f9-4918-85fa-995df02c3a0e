package pt.jumia.services.bill.domain.usecases.report;

import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.CommunicationsRequester;
import pt.jumia.services.bill.domain.entities.BusinessLine;
import pt.jumia.services.bill.domain.entities.DailyReceivedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentsUseCase;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class DailyReceivedDocumentsReportUseCase {
    private final ReadDocumentsUseCase readDocumentsUseCase;
    private final CommunicationsRequester communicationsRequester;
    private final OverallSettings overallSettings;

    public void generateReport(LocalDateTime reportRequestTime) {
        List<BusinessLine> enabledBusinessLines = overallSettings.getDailyReceivedDocumentsReportSettings()
                .getEnabledBusinessLines();
        int dailyReportTimeFrame = overallSettings.getDailyReceivedDocumentsReportSettings()
                .getReportTimeFrameInHours();
        LocalDateTime fullReportStart = reportRequestTime.minusHours(dailyReportTimeFrame);

        for (BusinessLine businessLine : enabledBusinessLines) {
            sendEmailsForReportBusinessLineSubscribers(
                    DailyReceivedDocumentsReport.builder()
                            .businessLine(businessLine)
                            .statusesReport(generateReportForStatuses(businessLine.getCountryCode(),
                                    businessLine.getShop(),
                                    fullReportStart,
                                    reportRequestTime,
                                    dailyReportTimeFrame))
                            .reviewReport(generateReviewReport(businessLine.getCountryCode(),
                                    businessLine.getShop(),
                                    fullReportStart,
                                    reportRequestTime,
                                    dailyReportTimeFrame))
                            .build()
            );
        }
    }

    private void sendEmailsForReportBusinessLineSubscribers(DailyReceivedDocumentsReport dailyReceivedDocumentsReport) {
        BusinessLine businessLine = dailyReceivedDocumentsReport.getBusinessLine();
        List<String> emails = overallSettings.getDailyReceivedDocumentsReportSettings()
                .getCommunicationEmailsForBusinessLine(
                        businessLine.getShop(),
                        businessLine.getCountryCode().getAlpha2()
                );
        emails.forEach(email -> communicationsRequester.sendEmailByReceivedDocumentsReportToReceiver(
                dailyReceivedDocumentsReport, email));
    }

    private DailyReceivedDocumentsReport.StatusesReport generateReportForStatuses(CountryCode countryCode,
                                                                                  String shop,
                                                                                  LocalDateTime reportRangeStartDate,
                                                                                  LocalDateTime reportRangeEndDate,
                                                                                  int range) {
        Map<DocumentStatus, Long> statusesMap = new HashMap<DocumentStatus, Long>();
        List<DocumentStatus> receivedDocumentStatus = DocumentStatus.getReceivedDocumentsStatusForDailyEmail();
        for (DocumentStatus documentStatus : receivedDocumentStatus) {
            DocumentFilter filters = DocumentFilter
                    .builder()
                    .countryCode(countryCode)
                    .flow(DocumentFlow.RECEIVED)
                    .shop(shop)
                    .updatedAtFrom(reportRangeStartDate)
                    .updatedAtTo(reportRangeEndDate)
                    .status(documentStatus)
                    .build();
            if (!documentStatus.equals(DocumentStatus.TAX_CANCELLED)) {
                filters = filters.toBuilder().reviewed(true).build();
            }
            statusesMap.put(documentStatus, readDocumentsUseCase.executeCountAll(filters));
        }

        return DailyReceivedDocumentsReport.StatusesReport.builder()
                .reportRangeStartDate(reportRangeStartDate)
                .reportRangeEndDate(reportRangeEndDate)
                .statusesMap(statusesMap)
                .range(range)
                .build();
    }

    private DailyReceivedDocumentsReport.ReviewReport generateReviewReport(CountryCode countryCode,
                                                                           String shop,
                                                                           LocalDateTime reportRangeStartDate,
                                                                           LocalDateTime reportRangeEndDate,
                                                                           int range) {
        LocalDateTime startOfSecondRange = reportRangeEndDate.minusHours(range * 2L / 3L);
        LocalDateTime startOfThirdRange = reportRangeEndDate.minusHours(range / 3L);

        return DailyReceivedDocumentsReport.ReviewReport.builder()
                .reportFirstRangeStartDate(reportRangeStartDate)
                .reportSecondRangeStartDate(startOfSecondRange)
                .reportThirdRangeStartDate(startOfThirdRange)
                .reportRangeEndDate(reportRangeEndDate)
                .countOfUnreviewedDocumentsForTheFirstThird(readDocumentsUseCase
                        .executeCountAll(DocumentFilter
                                .builder()
                                .countryCode(countryCode)
                                .flow(DocumentFlow.RECEIVED)
                                .shop(shop)
                                .receivedDateFrom(reportRangeStartDate)
                                .receivedDateTo(startOfSecondRange)
                                .reviewed(false)
                                .build()))
                .countOfUnreviewedDocumentsForTheSecondThird(readDocumentsUseCase
                        .executeCountAll(DocumentFilter
                                .builder()
                                .countryCode(countryCode)
                                .flow(DocumentFlow.RECEIVED)
                                .shop(shop)
                                .receivedDateFrom(startOfSecondRange)
                                .receivedDateTo(startOfThirdRange)
                                .reviewed(false)
                                .build()))
                .countOfUnreviewedDocumentsForTheThirdThird(readDocumentsUseCase
                        .executeCountAll(DocumentFilter
                                .builder()
                                .countryCode(countryCode)
                                .flow(DocumentFlow.RECEIVED)
                                .shop(shop)
                                .receivedDateFrom(startOfThirdRange)
                                .receivedDateTo(reportRangeEndDate)
                                .reviewed(false)
                                .build()))
                .build();
    }
}

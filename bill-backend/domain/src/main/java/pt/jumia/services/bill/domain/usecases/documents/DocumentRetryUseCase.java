package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.filters.DocumentRetryFilters;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;

@Component
@RequiredArgsConstructor
@Slf4j
public class DocumentRetryUseCase {

    private final DocumentAggregateRepository documentAggregateRepository;

    private final TaxiRequester taxiRequester;

    public void execute(Document document) {
        if (DocumentStatus.getTaxiError().contains(document.getStatus())) {
            this.resubmit(document);
        } else if (DocumentStatus.getTaxError().contains(document.getStatus())) {
            this.retry(document);
        } else {
            throw ConflictOperationException.createInvalidRetryStatus(document.getStatus());
        }
    }

    private void retry(Document document) {
        taxiRequester.bulkRetryDocuments(DocumentRetryFilters.builder()
                .relatedEntityId(document.getId())
                .build());
    }

    private void resubmit(Document document) {
        DocumentAggregate documentAggregate = documentAggregateRepository.findByDocumentId(document.getId())
                .orElseThrow(() -> EntityNotFoundException.createNotFound(Document.class, document.getId()));

        log.info("Pushing document '{}' to TaxI", documentAggregate.getDocument().getSid());
        if (DocumentType.SALES_CREDIT_NOTE.equals(documentAggregate.getDocument().getType())
        || DocumentType.PURCHASE_CREDIT_NOTE.equals(documentAggregate.getDocument().getType())) {
            taxiRequester.pushCreditNote(documentAggregate.getDocument().getOriginalDocument().getSid(), documentAggregate);
        } else {
            taxiRequester.pushInvoice(documentAggregate);
        }
    }
}

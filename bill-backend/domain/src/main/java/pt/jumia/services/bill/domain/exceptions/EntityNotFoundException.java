package pt.jumia.services.bill.domain.exceptions;

import java.text.MessageFormat;
import java.util.UUID;
import org.apache.commons.lang3.exception.ExceptionUtils;


/**
 * Exception thrown when we cannot find an entity
 */
public class EntityNotFoundException extends CodedException {

    private static final long serialVersionUID = 1024381488382429855L;

    public static EntityNotFoundException createNotFound(Class<?> clazz, long id) {
        return new EntityNotFoundException(MessageFormat.format("{0} with id {1} not found", clazz.getName(), id));
    }

    public static EntityNotFoundException createNotFound(Class<?> clazz, UUID id) {
        return new EntityNotFoundException(MessageFormat.format("{0} with id {1} not found", clazz.getName(), id));
    }
    public static EntityNotFoundException createNotFoundBySid(Class<?> clazz, String sid) {
        return new EntityNotFoundException(MessageFormat.format("{0} with sid {1} not found", clazz.getName(), sid));
    }

    public static EntityNotFoundException createNotFoundByProperty(Class<?> clazz, String property) {
        return new EntityNotFoundException(MessageFormat.format("{0} with property {1} not found", clazz.getName(), property));
    }

    public static EntityNotFoundException createNotFound(Class<?> clazz, String field, Object id) {
        return new EntityNotFoundException(MessageFormat.format("{0} with {1} {2} not found", clazz.getSimpleName(), field, id));
    }

    public static EntityNotFoundException create(Exception e) {
        return new EntityNotFoundException(ExceptionUtils.getStackTrace(e), e);
    }

    private EntityNotFoundException(String message, Exception e) {
        super(ErrorCode.DATA_INTEGRITY_VIOLATION, message, e);
    }

    private EntityNotFoundException(String message) {
        super(ErrorCode.ENTITY_NOT_FOUND, message);
    }
}

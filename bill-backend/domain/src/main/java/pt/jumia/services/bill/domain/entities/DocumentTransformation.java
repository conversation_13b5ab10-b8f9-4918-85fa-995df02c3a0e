package pt.jumia.services.bill.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import javax.annotation.Nullable;
import java.time.LocalDateTime;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class DocumentTransformation {

    public enum EntityType {
        ISSUE_DATE_CHANGE,
        ITEM_NAME_CHANGE,
        ITEM_CODE_GENERATION
    }

    @Nullable
    private Long id;

    private Document document;

    private String originalValue;

    private String newValue;

    private EntityType type;
    @Nullable
    private LocalDateTime createdAt;
    @Nullable
    private LocalDateTime updatedAt;

    public DocumentTransformation withoutDbFields() {
        return toBuilder()
                .id(null)
                .createdAt(null)
                .updatedAt(null)
                .build();
    }


}

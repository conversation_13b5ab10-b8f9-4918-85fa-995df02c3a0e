package pt.jumia.services.bill.domain.usecases.documents.processors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.caches.AfromsCache;
import pt.jumia.services.bill.domain.entities.AfromsProductDetails;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.ItemType;

@Slf4j
@Component
@RequiredArgsConstructor
public class AfromsProcessor implements Processor {

    private final AfromsCache afromsCache;

    @Override
    public void process(DocumentAggregate aggregate) {
        aggregate.getLines().forEach(documentLine -> {
            if (documentLine.getItemType().equals(ItemType.PRODUCT) && (documentLine.getCategory() == null ||
                    (documentLine.getCategory() != null && documentLine.getCategory().getSid() == null &&
                            documentLine.getCategory().getTaxAuthorityCode() == null))) {
                AfromsProductDetails afromsProductDetails = getAfromsProductDetails(documentLine);
                documentLine.setCategory(Category.builder()
                        .sid(afromsProductDetails.getUcrId())
                        .name(afromsProductDetails.getUcrPath())
                        .build());
                documentLine.setItemName(afromsProductDetails.getProductName());
            }
        });
    }

    private AfromsProductDetails getAfromsProductDetails(DocumentLine documentLine) {
        return afromsCache.getAfromsCacheProductDetails(
                documentLine.getDocument().getCountry(),
                documentLine.getDocument().getShop(),
                documentLine.getItemCode(),
                documentLine.getSkuVariant()
        );
    }
}

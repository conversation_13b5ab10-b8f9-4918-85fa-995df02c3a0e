package pt.jumia.services.bill.domain.entities.dtos;

import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Data
@Builder(toBuilder = true)
public class AdjustableDocumentPartDto {

    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    @Builder.Default
    private List<TaxCategoryTotal> taxCategoryTotals = new ArrayList<>();
    @Builder.Default
    private HashMap<TaxCategory, TaxCategoryTotal> taxCategoryTotalHashMap = new HashMap<>();

    public void addTaxCategoryTotal(List<DocumentLine.AppliedTax> newTaxCategoryTotals) {
        newTaxCategoryTotals.forEach(newTaxCategoryTotal -> {
            if (taxCategoryTotalHashMap.containsKey(newTaxCategoryTotal.getTaxCategory())) {
                TaxCategoryTotal existsTaxCategoryTotal = taxCategoryTotalHashMap.get(newTaxCategoryTotal.getTaxCategory());
                existsTaxCategoryTotal.setTaxAmount(existsTaxCategoryTotal.getTaxAmount().add(newTaxCategoryTotal.getTaxAmount()));
            } else {
                TaxCategoryTotal taxCategoryTotal = TaxCategoryTotal.builder()
                        .generateId()
                        .taxCategory(newTaxCategoryTotal.getTaxCategory())
                        .taxRate(newTaxCategoryTotal.getTaxRate())
                        .taxAmount(newTaxCategoryTotal.getTaxAmount())
                        .taxFixedAmount(newTaxCategoryTotal.getTaxFixedAmount()).build();
                taxCategoryTotalHashMap.put(newTaxCategoryTotal.getTaxCategory(), taxCategoryTotal);
                taxCategoryTotals.add(taxCategoryTotal);
            }
        });
    }

    public void addTotalAmount(BigDecimal newTotalAmount) {
        this.totalAmount = this.totalAmount == null ? newTotalAmount : this.totalAmount.add(newTotalAmount);
    }

    public void addNetAmount(BigDecimal newNetAmount) {
        this.netAmount = this.netAmount == null ? newNetAmount : this.netAmount.add(newNetAmount);
    }

    public void addTaxAmount(BigDecimal newTaxAmount) {
        this.taxAmount = this.taxAmount == null ? newTaxAmount : this.taxAmount.add(newTaxAmount);
    }

    public void addDiscountAmount(BigDecimal newDiscountAmount) {
        this.discountAmount = this.discountAmount == null ? newDiscountAmount : this.discountAmount.add(newDiscountAmount);
    }

    public void appendLine(DocumentLine documentLine) {
        this.addTotalAmount(documentLine.getTotalAmount());
        this.addNetAmount(documentLine.getNetAmount());
        this.addTaxAmount(documentLine.getTotalTaxAmount());
        if (documentLine.getDiscount() != null) {
            this.addDiscountAmount(documentLine.getDiscount().getAmount());
        }
        this.addTaxCategoryTotal(documentLine.getAppliedTaxes());
    }

}

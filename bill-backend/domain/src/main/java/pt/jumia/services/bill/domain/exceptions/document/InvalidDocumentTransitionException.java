package pt.jumia.services.bill.domain.exceptions.document;

import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.text.MessageFormat;

public class InvalidDocumentTransitionException extends CodedException {

    private static final long serialVersionUID = 8372555446767318190L;

    private InvalidDocumentTransitionException(String message) {
        super(ErrorCode.INVALID_DOCUMENT_STATUS_TRANSITION, message);
    }

    public static InvalidDocumentTransitionException createInvalidDocumentStatusTransition(Document document,
                                                                                           DocumentStatus newStatus) {
        return new InvalidDocumentTransitionException(MessageFormat
                .format("Document id: {0} - sid: {1} is being moved from status: {2} to status: {3}," +
                                " and this is invalid operation",
                        document.getId(), document.getSid(), document.getStatus().name(), newStatus.name()));
    }

    public static InvalidDocumentTransitionException createInvalidReceivedDocumentStatusTransition(
            Document receivedDocument,
            DocumentStatus newStatus) {
        return new InvalidDocumentTransitionException(MessageFormat
                .format("ReceivedDocument id: {0} - issued by: {1} at: {2}," +
                                "is being moved from status: {3} to status: {4}," +
                                " and this is invalid operation",
                        receivedDocument.getId(), receivedDocument.getIssuer().getName(), receivedDocument.getIssuedDate(),
                        receivedDocument.getStatus().name(), newStatus.name()));
    }
}

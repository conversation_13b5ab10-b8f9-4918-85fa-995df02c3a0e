package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DocumentLineWithNewDecimalFields extends DocumentLine {

    private BigDecimal quantity_new;
    private BigDecimal unitPrice_new;
    private BigDecimal totalAmount_new;
    private BigDecimal netAmount_new;
    private BigDecimal totalTaxAmount_new;
    private BigDecimal discountAmount_new;
    private BigDecimal discountRate_new;

    @Builder(builderMethodName = "newDecimalFieldsBuilder")
    public DocumentLineWithNewDecimalFields(DocumentLine documentLine,
                                          BigDecimal quantity_new,
                                          BigDecimal unitPrice_new,
                                          BigDecimal totalAmount_new,
                                          BigDecimal netAmount_new,
                                          BigDecimal totalTaxAmount_new,
                                          BigDecimal discountAmount_new,
                                          BigDecimal discountRate_new) {
        super(documentLine.toBuilder());
        this.quantity_new = quantity_new;
        this.unitPrice_new = unitPrice_new;
        this.totalAmount_new = totalAmount_new;
        this.netAmount_new = netAmount_new;
        this.totalTaxAmount_new = totalTaxAmount_new;
        this.discountAmount_new = discountAmount_new;
        this.discountRate_new = discountRate_new;
    }

    @Override
    public DocumentLine withoutDbField() {
        DocumentLine documentLine = super.withoutDbField();
        return DocumentLineWithNewDecimalFields.newDecimalFieldsBuilder()
                .documentLine(documentLine)
                .quantity_new(quantity_new != null ? quantity_new.setScale(10, RoundingMode.CEILING) : null)
                .unitPrice_new(unitPrice_new != null ? unitPrice_new.setScale(10, RoundingMode.CEILING) : null)
                .totalAmount_new(totalAmount_new != null ? totalAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .netAmount_new(netAmount_new != null ? netAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .totalTaxAmount_new(totalTaxAmount_new != null ? totalTaxAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .discountAmount_new(discountAmount_new != null ? discountAmount_new.setScale(10, RoundingMode.CEILING) : null)
                .discountRate_new(discountRate_new != null ? discountRate_new.setScale(10, RoundingMode.CEILING) : null)
                .build();
    }

    @Override
    public void convertAmountsToPositiveValue() {
        super.convertAmountsToPositiveValue();
        
        this.quantity_new = this.quantity_new != null && this.quantity_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.quantity_new.negate() : this.quantity_new;
        this.unitPrice_new = this.unitPrice_new != null && this.unitPrice_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.unitPrice_new.negate() : this.unitPrice_new;
        this.totalAmount_new = this.totalAmount_new != null && this.totalAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalAmount_new.negate() : this.totalAmount_new;
        this.netAmount_new = this.netAmount_new != null && this.netAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.netAmount_new.negate() : this.netAmount_new;
        this.totalTaxAmount_new = this.totalTaxAmount_new != null && this.totalTaxAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.totalTaxAmount_new.negate() : this.totalTaxAmount_new;
        this.discountAmount_new = this.discountAmount_new != null && this.discountAmount_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.discountAmount_new.negate() : this.discountAmount_new;
        this.discountRate_new = this.discountRate_new != null && this.discountRate_new.compareTo(BigDecimal.ZERO) < 0 ?
                this.discountRate_new.negate() : this.discountRate_new;
    }
}

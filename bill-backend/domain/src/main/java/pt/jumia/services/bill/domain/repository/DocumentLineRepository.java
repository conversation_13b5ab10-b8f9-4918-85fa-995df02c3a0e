package pt.jumia.services.bill.domain.repository;

import pt.jumia.services.bill.domain.entities.DocumentLine;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

public interface DocumentLineRepository {

    Optional<DocumentLine> findById(UUID id);
    List<DocumentLine> findByDocumentId(UUID id);
    DocumentLine save(DocumentLine documentLine);

    int migrateDecimalFields(Long startId, Long endId);

    Map<String, Object> getDecimalFieldsMigrationStatus();

}

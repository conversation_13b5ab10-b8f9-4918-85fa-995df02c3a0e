package pt.jumia.services.bill.domain.entities;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class GhDocPdfRequestPayloadInfo {

    private GhDocSubmissionInfoPayload originalRequest;
    private GhDocumentRequestPayload requestInfo;
    private GhDocumentResponsePayload responseTaxAuthorities;

    public GhDocPdfRequestPayloadInfo toEntity() {
        return GhDocPdfRequestPayloadInfo
                .builder()
                .originalRequest(originalRequest)
                .requestInfo(requestInfo)
                .responseTaxAuthorities(responseTaxAuthorities)
                .build();
    }

}

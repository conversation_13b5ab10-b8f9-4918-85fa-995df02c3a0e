package pt.jumia.services.bill.domain.usecases.documentapilogs;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.DocumentApiLog;
import pt.jumia.services.bill.domain.repository.DocumentApiLogRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class UpsertDocumentApiLogUseCase {

    private final DocumentApiLogRepository documentApiLogRepository;

    public DocumentApiLog execute(DocumentApiLog documentApiLog) {
        return documentApiLogRepository.insert(
                documentApiLog.toBuilder()
                        .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                        .createdBy(RequestContext.getUsername())
                        .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
                        .updatedBy(RequestContext.getUsername())
                        .build()
        );
    }

    public DocumentApiLog execute(long id, DocumentApiLog documentApiLog) {
        Optional<DocumentApiLog> optionalDocumentLog = documentApiLogRepository.findById(id);
        if (optionalDocumentLog.isEmpty()) {
            return this.execute(documentApiLog);
        } else {
            return documentApiLogRepository.update(optionalDocumentLog.get().getId(),
                    documentApiLog.toBuilder()
                            .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
                            .updatedBy(RequestContext.getUsername())
                            .build());
        }
    }
}

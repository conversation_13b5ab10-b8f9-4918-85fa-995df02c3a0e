package pt.jumia.services.bill.domain.usecases.acl;

import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.Permissions;
import pt.jumia.services.bill.domain.entities.User;
import pt.jumia.services.bill.domain.exceptions.UserForbiddenException;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class UserPermissionValidationUseCase {

    private final GetAclUserUseCase getAclUserUseCase;

    public void checkCanAccessOrThrow(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanAccess()) {
            throw UserForbiddenException.createCannotAccess();
        }
    }

    public void checkCanManageSettingsOrThrow(RequestUser requestUser) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.isCanManageSettings()) {
            throw UserForbiddenException.createDontHavePermission(aclUser.getUsername(), Permissions.MANAGE_SETTINGS);
        }
    }

    public void checkCanResubmitDocumentByCountryOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.RESUBMIT_DOCUMENT);
    }

    public void checkCanViewDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.CAN_VIEW_DOCUMENTS);
    }

    public void checkCanManageDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.CAN_MANAGE_DOCUMENTS);
    }

    public void checkCanSendDocumentsThroughEmailByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.SEND_DOCUMENTS_THROUGH_EMAIL);
    }

    public void checkCanAcknowledgeDocumentByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.ACK_DOCUMENT);
    }

    public void checkCanCancelDocumentByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.CANCEL_DOCUMENT);
    }

    public void checkCanDeclineDocumentRejectionByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.DECLINE_REJECTED_DOCUMENT);
    }

    public void checkCanRetryDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.CAN_RETRY_DOCUMENTS);
    }

    public void checkCanListReceivedDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.CAN_LIST_RECEIVED_DOCUMENTS);
    }

    public void checkCanCreateReceivedDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.CAN_CREATE_RECEIVED_DOCUMENTS);
    }

    public void checkCanReviewReceivedDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.REVIEW_RECEIVED_DOCUMENTS);
    }

    public void checkCanRejectReceivedDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.REJECT_RECEIVED_DOCUMENTS);
    }

    public void checkCanApprovedReceivedDocumentsByCountryCodeOrThrow(RequestUser requestUser, CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode, Permissions.APPROVED_RECEIVED_DOCUMENTS);
    }

    public void checkCanDeclineReceivedDocumentsCancellationByCountryCodeOrThrow(RequestUser requestUser,
                                                                                 CountryCode countryCode) {
        this.checkUserHasPermissionByCountryOrThrow(requestUser, countryCode,
                Permissions.DECLINE_RECEIVED_DOCUMENTS_CANCELLATION);
    }

    public List<CountryCode> getCountriesCanViewDocumentsOrThrow(RequestUser requestUser) throws UserForbiddenException {
        return this.getCountriesByPermissionOrThrow(requestUser, Permissions.CAN_VIEW_DOCUMENTS);
    }

    public List<CountryCode> getCountriesCanViewReceivedDocumentsOrThrow(RequestUser requestUser) throws UserForbiddenException {
        return this.getCountriesByPermissionOrThrow(requestUser, Permissions.CAN_LIST_RECEIVED_DOCUMENTS);
    }

    private void checkUserHasPermissionByCountryOrThrow(RequestUser requestUser, CountryCode countryCode,
                                                        String permission) {
        User aclUser = getAclUserUseCase.execute(requestUser);
        if (!aclUser.getCountriesPermissionsList().containsKey(countryCode) ||
                !aclUser.getCountriesPermissionsList().get(countryCode).contains(permission)) {
            throw UserForbiddenException.createDontHavePermission(aclUser.getUsername(), permission);
        }
    }

    private List<CountryCode> getCountriesByPermissionOrThrow(RequestUser requestUser, String permissions) throws UserForbiddenException {
        User aclUser = getAclUserUseCase.execute(requestUser);
        List<CountryCode> countriesWithCanViewPermission = aclUser.getCountriesPermissionsList()
                .entrySet().stream()
                .filter(entry -> entry.getValue().contains(permissions))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (countriesWithCanViewPermission.size() == 0) {
            throw UserForbiddenException.createDontHavePermission(aclUser.getUsername(), permissions);
        }
        return countriesWithCanViewPermission;
    }
}

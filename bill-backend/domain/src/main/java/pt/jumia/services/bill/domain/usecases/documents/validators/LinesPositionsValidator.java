package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Component
@RequiredArgsConstructor
public class LinesPositionsValidator implements Validator {
    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        List<ValidationError> errors = new ArrayList<>();
        HashSet<Integer> existingLinesPositions = new HashSet<>();

        aggregate.getLines().forEach((documentLine -> {
            /**
             * Add in hashSet returns false when it adds a repeated value
             */
            if (!existingLinesPositions.add(documentLine.getPosition())) {
                errors.add(new ValidationError(
                        ErrorCode.INCORRECT_LINE_POSITION_VALUE,
                        String.format("Line Position (%s) is repeated",
                                documentLine.getPosition())
                ));
            }
        }));

        return errors;
    }
}

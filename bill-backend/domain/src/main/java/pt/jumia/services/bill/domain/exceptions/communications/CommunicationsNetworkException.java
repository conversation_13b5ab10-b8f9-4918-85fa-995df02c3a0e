package pt.jumia.services.bill.domain.exceptions.communications;

import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

public class CommunicationsNetworkException extends CodedException {

    private static final long serialVersionUID = 5060635639606771696L;

    private CommunicationsNetworkException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    private CommunicationsNetworkException(ErrorCode errorCode, String message, Exception cause) {
        super(errorCode, message, cause);
    }

    public static CommunicationsNetworkException buildResponseNotOk(int statusCode, String response) {
        return new CommunicationsNetworkException(ErrorCode.COMMUNICATIONS_REQUEST_FAILED, String.format("%s - %s", statusCode, response));
    }

    public static CommunicationsNetworkException buildResponseNotOk(int statusCode, String response, Exception e) {
        return new CommunicationsNetworkException(ErrorCode.COMMUNICATIONS_REQUEST_FAILED, String.format("%s - %s", statusCode, response), e);
    }

    public static CommunicationsNetworkException buildFailedRequest(String message, Exception cause) {
        return new CommunicationsNetworkException(ErrorCode.COMMUNICATIONS_REQUEST_FAILED, message, cause);
    }

    public static CommunicationsNetworkException buildFailedRequest(String message) {
        return new CommunicationsNetworkException(ErrorCode.COMMUNICATIONS_REQUEST_FAILED, message);
    }
}

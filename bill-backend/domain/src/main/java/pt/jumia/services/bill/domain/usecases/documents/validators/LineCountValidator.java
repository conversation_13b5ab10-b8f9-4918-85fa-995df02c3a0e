package pt.jumia.services.bill.domain.usecases.documents.validators;

import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

@Component
public class LineCountValidator implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        if (aggregate.getDocument().getLineCount() != aggregate.getLines().size()) {
            return List.of(new ValidationError(
                    ErrorCode.INCORRECT_LINE_COUNT,
                    String.format("Invalid line count. Expected %s lines but got %s",
                            aggregate.getDocument().getLineCount(), aggregate.getLines().size())
            ));
        }

        return List.of();
    }
}

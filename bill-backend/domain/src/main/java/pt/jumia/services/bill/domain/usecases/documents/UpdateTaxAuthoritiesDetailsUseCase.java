package pt.jumia.services.bill.domain.usecases.documents;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.dtos.TaxAuthoritiesDetailsUpdateDto;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.exceptions.document.InvalidDocumentTransitionException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.metrics.MonitoringUseCase;
import pt.jumia.services.bill.domain.utils.DecimalFieldsConverter;

import java.util.Locale;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateTaxAuthoritiesDetailsUseCase {

    private final GenerateJudgeDocumentUseCase generateJudgeDocumentUseCase;
    private final DocumentAggregateRepository documentAggregateRepository;
    private final SendEmailDocumentToReceiverUseCase sendEmailDocumentToReceiverUseCase;
    private final MonitoringUseCase monitoringUseCase;

    private final OverallSettings overallSettings;

    public void execute(TaxAuthoritiesDetailsUpdateDto taxAuthoritiesDetailsUpdateDto) throws JsonProcessingException {

        DocumentAggregate documentAggregate = documentAggregateRepository
                .findByDocumentId(taxAuthoritiesDetailsUpdateDto.getDocumentId())
                .orElseThrow(() ->
                        EntityNotFoundException.createNotFound(DocumentAggregate.class,
                                taxAuthoritiesDetailsUpdateDto.getDocumentId()));

        boolean statusChanged = !documentAggregate.getDocument()
                .getStatus()
                .equals(taxAuthoritiesDetailsUpdateDto.getDocumentStatus());

        boolean canTransitionStatus = documentAggregate.getDocument().getStatus()
                .canTransitionTo(taxAuthoritiesDetailsUpdateDto.getDocumentStatus(), documentAggregate.getDocument().getCountry());

        if (statusChanged && !canTransitionStatus) {
            log.error("Invalid Document Status Update: Trying to update document: '{}' from: '{}' to: '{}'",
                    documentAggregate.getDocument().getSid(),
                    documentAggregate.getDocument().getStatus().name(),
                    taxAuthoritiesDetailsUpdateDto.getDocumentStatus().name()
            );
            throw InvalidDocumentTransitionException.createInvalidDocumentStatusTransition(
                    documentAggregate.getDocument(),
                    taxAuthoritiesDetailsUpdateDto.getDocumentStatus()
            );
        }

        if (documentAggregate.getTaxAuthoritiesDetails() != null) {
            documentAggregate.setTaxAuthoritiesDetails(
                    merge(documentAggregate.getTaxAuthoritiesDetails(),
                            taxAuthoritiesDetailsUpdateDto.getTaxAuthoritiesDetails())
            );
        } else {
            documentAggregate.setTaxAuthoritiesDetails(taxAuthoritiesDetailsUpdateDto
                    .getTaxAuthoritiesDetails()
                    .toBuilder()
                    .document(documentAggregate.getDocument())
                    .build());
        }
        documentAggregate.getDocument().setStatus(taxAuthoritiesDetailsUpdateDto.getDocumentStatus());

        // Convert document aggregate to use new decimal fields
        documentAggregate = DecimalFieldsConverter.convertDocumentAggregateToNewDecimalFields(documentAggregate);

        documentAggregateRepository.save(documentAggregate);

        monitoringUseCase.recordTaxUpdatesRequest(documentAggregate.getDocument());

        boolean generatePdf = this.overallSettings.getJudgeSettings().isAutomaticPdfGenerationEnabled(
                documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                documentAggregate.getDocument().getCountry().getAlpha2());

        log.info("Handling tax authority update for document with ID'{}', marking document as '{}' " +
                        "(generate PDF: {}, status changed: {}, successful status: {})",
                taxAuthoritiesDetailsUpdateDto.getDocumentId(),
                taxAuthoritiesDetailsUpdateDto.getDocumentStatus(),
                generatePdf,
                statusChanged,
                documentAggregate.getDocument().getStatus().isSuccessful());

        boolean isGhDocument = documentAggregate.getDocument().getCountry().equals(CountryCode.GH);

        if (generatePdf && statusChanged && documentAggregate.getDocument().getStatus().isSuccessful()) {
            DocumentAggregate documentAggregateUpdated;
            if (taxAuthoritiesDetailsUpdateDto.getOverrideFields() != null) {
                documentAggregateUpdated = isGhDocument ? generateJudgeDocumentUseCase
                        .executeWithAllDocFields(documentAggregate, taxAuthoritiesDetailsUpdateDto.getOverrideFields(),
                                taxAuthoritiesDetailsUpdateDto.getTaxAuthoritiesDetails().getDocPdfFields())
                        : generateJudgeDocumentUseCase.execute(documentAggregate, taxAuthoritiesDetailsUpdateDto.getOverrideFields());
            } else {
                documentAggregateUpdated = isGhDocument ? generateJudgeDocumentUseCase
                        .executeWithAllDocFields(documentAggregate, taxAuthoritiesDetailsUpdateDto.getTaxAuthoritiesDetails().getDocPdfFields())
                        : generateJudgeDocumentUseCase.execute(documentAggregate);
            }
            sendEmailDocumentToReceiverUseCase.execute(documentAggregateUpdated);
        }

    }

    private TaxAuthoritiesDetails merge(TaxAuthoritiesDetails existing, TaxAuthoritiesDetails updated) {
        return existing.toBuilder()
                .submissionId(updated.getSubmissionId())
                .taxDocumentNumber(updated.getTaxDocumentNumber())
                .qrCode(updated.getQrCode())
                .internalData(updated.getInternalData())
                .verificationCode(updated.getVerificationCode())
                .deviceNumber(updated.getDeviceNumber())
                .statusCode(updated.getStatusCode())
                .errorCode(updated.getErrorCode())
                .exception(updated.getException())
                .build();
    }

}

package pt.jumia.services.bill.domain.usecases.documents.processors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;
import pt.jumia.services.bill.domain.usecases.documents.AckDocumentUseCase;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class AutoAckDocumentProcessor implements Processor {

    private final DocumentAggregateRepository documentAggregateRepository;

    private final AckDocumentUseCase ackDocumentUseCase;

    @Override
    public void process(DocumentAggregate aggregate) {

        DocumentFilter filter = DocumentFilter.builder()
                .sid(aggregate.getDocument().getSid())
                .statuses(DocumentStatus.getErrorStatuses())
                .include(List.of(Document.Details.values()))
                .build();

        documentAggregateRepository.findAll(filter)
                .forEach(documentInError -> {
                    log.info("Acknowledge document '{}'", documentInError.getDocument().getId());
                    ackDocumentUseCase.execute(documentInError.getDocument());
                });
    }
}

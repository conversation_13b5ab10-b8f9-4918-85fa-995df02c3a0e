package pt.jumia.services.bill.domain.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtils {

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_TIME_FORMATTER_SHORT_VERSION = DateTimeFormatter.
            ofPattern("MM/dd/yyyy HH:mm:ss");

    public static LocalDateTime parse(String strDateTime) {
        if (strDateTime == null) {
            return null;
        }

        return LocalDateTime.parse(strDateTime, DATE_TIME_FORMATTER);
    }

    public static String format(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }

        return localDateTime.format(DATE_TIME_FORMATTER);
    }


}

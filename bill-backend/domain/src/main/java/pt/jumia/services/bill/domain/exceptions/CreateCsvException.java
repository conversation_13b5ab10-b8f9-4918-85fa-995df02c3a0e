package pt.jumia.services.bill.domain.exceptions;

public class CreateCsvException extends CodedException {
    private static final long serialVersionUID = -8208631883311721066L;

    public CreateCsvException(String message) {
        super(ErrorCode.CREATE_CSV_FILE, message);
    }

    public CreateCsvException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public static CreateCsvException dataIsEmpty() {
        return new CreateCsvException(ErrorCode.CREATE_CSV_FILE_DATA_EMPTY, "No data to create CSV file.");
    }
}

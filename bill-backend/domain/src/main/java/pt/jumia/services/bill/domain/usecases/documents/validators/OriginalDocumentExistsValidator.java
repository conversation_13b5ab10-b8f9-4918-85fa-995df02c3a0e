package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class OriginalDocumentExistsValidator implements Validator {

    private final DocumentRepository documentRepository;

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {
        if (!aggregate.getDocument().getType().isOriginalDocumentRequired()) {
            return List.of();
        }

        List<ValidationError> errors = new ArrayList<>();

        if (aggregate.getDocument().getOriginalDocument() == null) {
            return List.of(new ValidationError(
                    ErrorCode.INVALID_ORIGINAL_DOCUMENT,
                    "Missing original document identifier"
            ));
        }

        UUID id = aggregate.getDocument().getOriginalDocument().getId();
        String sid = aggregate.getDocument().getOriginalDocument().getSid();

        if (id == null && sid == null) {
            return List.of(new ValidationError(
                    ErrorCode.INVALID_ORIGINAL_DOCUMENT,
                    "Missing original document identifier"
            ));
        }

        if (id != null) {
            Document originalDocument = documentRepository.findById(id).orElse(null);
            if (originalDocument == null) {
                errors.add(new ValidationError(
                        ErrorCode.INVALID_ORIGINAL_DOCUMENT,
                        String.format("No original document found with id %s", id)));
            } else {
                if (!originalDocument.getStatus().isSuccessful()) {
                    errors.add(new ValidationError(
                            ErrorCode.INVALID_ORIGINAL_DOCUMENT,
                            String.format("No successful original document found with id %s", id)));
                }
            }
        }

        if (sid != null && documentRepository.findDocumentIdBySidInSuccessStatus(sid).isEmpty()) {
            errors.add(new ValidationError(
                    ErrorCode.INVALID_ORIGINAL_DOCUMENT,
                    String.format("No successful original document found with sid %s", sid)));
        }

        return errors;
    }

}

package pt.jumia.services.bill.domain;

/**
 * Defines the mode in which the given component should be used.
 */
public interface Profiles {

    /**
     * Used in production
     */
    String PROD = "default";

    /**
     * Uses fake clients for every situation (network requests, database, ...)
     */
    String FAKE_CLIENTS = "fakeClients";

    /**
     * Populates the database with fake entities, on the application startup.
     */
    String DB_SETUP = "dbSetup";

    /**
     * Merges test properties (application-test.yml) into main properties, for endpoints tests.
     */
    String TEST = "test";
}

package pt.jumia.services.bill.domain.entities;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.UUID;

@Data
@AllArgsConstructor
@Builder(toBuilder = true)
public class OtherSplitDocument {

    private UUID id;
    private DocumentStatus status;
    private DocumentType type;
    private String sid;

    public OtherSplitDocument (Document document) {
        this.id = document.getId();
        this.sid = document.getSid();
        this.type = document.getType();
        this.status = document.getStatus();
    }

    public OtherSplitDocument withoutDbField() {
        return this.toBuilder()
                .id(null)
                .build();
    }
}

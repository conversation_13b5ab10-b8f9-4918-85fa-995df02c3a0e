package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SummaryEqualLinesValidator implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        BigDecimal netAmountSum = aggregate.getLines().stream()
                .map(DocumentLine::getNetAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        List<ValidationError> errors = new ArrayList<>();

        if (!aggregate.getDocument().getNetAmount().equals(netAmountSum)) {
            errors.add(new ValidationError(
                    ErrorCode.NET_AMOUNT_NOT_EQUAL_NET_AMOUNT_LINES,
                    "Net amount not equal the sum of net amount in document lines."
            ));
        }

        BigDecimal totalAmountSum = aggregate.getLines().stream()
                .map(DocumentLine::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (!aggregate.getDocument().getTotalAmount().equals(totalAmountSum)) {
            errors.add(new ValidationError(
                    ErrorCode.TOTAL_AMOUNT_NOT_EQUAL_TOTAL_AMOUNT_LINES,
                    "Total amount not equal the sum of total amount in document lines."
            ));
        }

        BigDecimal taxAmountSum = aggregate.getLines().stream()
                .map(DocumentLine::getTotalTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (!aggregate.getDocument().getTaxAmount().equals(taxAmountSum)) {
            errors.add(new ValidationError(
                    ErrorCode.TAX_AMOUNT_NOT_EQUAL_TAX_AMOUNT_LINES,
                    "Tax amount not equal the sum of Tax amount in document lines."
            ));
        }

        return errors;
    }
}

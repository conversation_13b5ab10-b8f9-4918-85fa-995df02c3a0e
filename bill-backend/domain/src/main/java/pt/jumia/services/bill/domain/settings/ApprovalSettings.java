package pt.jumia.services.bill.domain.settings;

import lombok.Data;

@Data
public class ApprovalSettings {

    public static final String INVOICE_APPROVE_RECEIVED_DOCUMENTS_ENABLED = "documents.sales_invoice.approve_received_documents_enabled";
    public static final String CREDIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED = "documents.sales_credit_note.approve_received_documents_enabled";
    public static final String CREDIT_MEMO_APPROVE_RECEIVED_DOCUMENTS_ENABLED = "documents.sales_credit_memo.approve_received_documents_enabled";
    public static final String DEBIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED = "documents.sales_debit_note.approve_received_documents_enabled";
    public static final String PURCHASE_INVOICE_APPROVE_RECEIVED_DOCUMENTS_ENABLED = "documents.purchase_invoice.approve_received_documents_enabled";
    public static final String PURCHASE_CREDIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED = "documents.purchase_credit_note" +
                                                                                        ".approve_received_documents_enabled";
    public static final String PURCHASE_DEBIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED = "documents.purchase_debit_note" +
                                                                                        ".approve_received_documents_enabled";

    private final OverallSettings.OverridableSetting<Boolean> invoiceApprovalReceivedDocumentsEnabled;
    private final OverallSettings.OverridableSetting<Boolean> creditNoteApprovalReceivedDocumentsEnabled;
    private final OverallSettings.OverridableSetting<Boolean> creditMemoApprovalReceivedDocumentsEnabled;
    private final OverallSettings.OverridableSetting<Boolean> debitNoteApprovalReceivedDocumentsEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseInvoiceApprovalReceivedDocumentsEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseCreditNoteApprovalReceivedDocumentsEnabled;
    private final OverallSettings.OverridableSetting<Boolean> purchaseDebitNoteApprovalReceivedDocumentsEnabled;

    public ApprovalSettings(OverallSettings overallSettings) {

        this.invoiceApprovalReceivedDocumentsEnabled = overallSettings.createSetting(
                INVOICE_APPROVE_RECEIVED_DOCUMENTS_ENABLED,
                true,
                Boolean::parseBoolean);

        this.creditNoteApprovalReceivedDocumentsEnabled = overallSettings.createSetting(
                CREDIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED,
                true,
                Boolean::parseBoolean);

        this.creditMemoApprovalReceivedDocumentsEnabled = overallSettings.createSetting(
                CREDIT_MEMO_APPROVE_RECEIVED_DOCUMENTS_ENABLED,
                true,
                Boolean::parseBoolean);

        this.debitNoteApprovalReceivedDocumentsEnabled = overallSettings.createSetting(
                DEBIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseInvoiceApprovalReceivedDocumentsEnabled = overallSettings.createSetting(
                PURCHASE_INVOICE_APPROVE_RECEIVED_DOCUMENTS_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseCreditNoteApprovalReceivedDocumentsEnabled = overallSettings.createSetting(
                PURCHASE_CREDIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED,
                true,
                Boolean::parseBoolean);

        this.purchaseDebitNoteApprovalReceivedDocumentsEnabled = overallSettings.createSetting(
                PURCHASE_DEBIT_NOTE_APPROVE_RECEIVED_DOCUMENTS_ENABLED,
                true,
                Boolean::parseBoolean);
    }

    public boolean getInvoiceApprovalEnabled(String shop, String countryCode) {
        return this.invoiceApprovalReceivedDocumentsEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getCreditNoteApprovalEnabled(String shop, String countryCode) {
        return this.creditNoteApprovalReceivedDocumentsEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getCreditMemoApprovalEnabled(String shop, String countryCode) {
        return this.creditMemoApprovalReceivedDocumentsEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getDebitNoteApprovalEnabled(String shop, String countryCode) {
        return this.debitNoteApprovalReceivedDocumentsEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseInvoiceApprovalEnabled(String shop, String countryCode) {
        return this.purchaseInvoiceApprovalReceivedDocumentsEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseCreditNoteApprovalEnabled(String shop, String countryCode) {
        return this.purchaseCreditNoteApprovalReceivedDocumentsEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getPurchaseDebitNoteApprovalEnabled(String shop, String countryCode) {
        return this.purchaseDebitNoteApprovalReceivedDocumentsEnabled.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }
}

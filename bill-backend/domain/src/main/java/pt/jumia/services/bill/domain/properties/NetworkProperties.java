package pt.jumia.services.bill.domain.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.dtos.AfromsShopConfig;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Data
@Component
@ConfigurationProperties(prefix = "network")
public class NetworkProperties {

    private boolean logging = true;
    private int readTimeout = 30;
    private Judge judge = new Judge();
    private Taxi taxi = new Taxi();
    private Communications communications = new Communications();
    private Afroms afroms = new Afroms();

    @Data
    public static class Judge {
        private String url = "https://judge-api-dev.jumia.services/";
        private String username = "bill";
        private String password = "ya7aOtW3go5eNYU2Ry5B8fTbXcNvbYAknOYQRbAnlTQtZ2QHqHtSkpjN76bqjPMW";
        private Map<String, String> invoiceCodes;
        private Map<String, String> creditNoteCodes;
    }

    @Data
    public static class Taxi {
        private String url = "http://localhost:8080/";
        private String username = "fake-user";
        private String password = "fake-pass";
    }

    @Data
    public static class Communications {
        private String url = "https://communications-staging.jumia.services";
        private String username = "bill";
        private String password = "ya7aOtW3go5eNYU2Ry5B8fTbXcNvbYAknOYQRbAnlTQtZ2QHqHtSkpjN76bqjPMW";
        private String event = "event_name";
        private Boolean enabled = true;
    }

    @Data
    public static class Afroms {
        private Cache cache = new Cache();
        private List<AfromsShopConfig> shops;

        public Optional<AfromsShopConfig> getAfromsShopConfig(String country, String shop) {
            if (country != null && shop != null) {
                return this.shops
                        .stream()
                        .filter(config ->
                                config.getCountry().equalsIgnoreCase(country)
                                        &&
                                        config.getShop().equalsIgnoreCase(shop))
                        .findFirst();
            } else {
                return Optional.empty();
            }

        }
    }

    @Data
    public static class Cache {
        private long maxSize = 50;
        private long ttlMinutes = 60;
    }
}

package pt.jumia.services.bill.domain.exceptions;

import java.text.MessageFormat;
import java.util.UUID;

public class DuplicatedEntryException extends CodedException {

    private static final long serialVersionUID = -5611605069143402283L;

    private DuplicatedEntryException(String message) {
        super(ErrorCode.DUPLICATE_ENTRY, message);
    }

    public static DuplicatedEntryException duplicated(Class<?> clazz, UUID id) {
        return new DuplicatedEntryException(MessageFormat.format(
                "{0}: duplicate key value violates unique constraint, entity already exist with {1}",
                clazz.getName(), id));
    }
}

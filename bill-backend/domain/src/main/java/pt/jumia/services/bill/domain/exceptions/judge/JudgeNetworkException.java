package pt.jumia.services.bill.domain.exceptions.judge;

import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

public class JudgeNetworkException extends CodedException {

    private static final long serialVersionUID = 9185587841681442941L;

    private JudgeNetworkException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    private JudgeNetworkException(ErrorCode errorCode, String message, Exception cause) {
        super(errorCode, message, cause);
    }

    public static JudgeNetworkException buildResponseNotOk(int statusCode, String response) {
        return new JudgeNetworkException(ErrorCode.JUDGE_REQUEST_FAILED, String.format("%s - %s", statusCode, response));
    }

    public static JudgeNetworkException buildResponseNotOk(int statusCode, String response, Exception e) {
        return new JudgeNetworkException(ErrorCode.JUDGE_REQUEST_FAILED, String.format("%s - %s", statusCode, response), e);
    }

    public static JudgeNetworkException buildFailedRequest(String message, Exception cause) {
        return new JudgeNetworkException(ErrorCode.JUDGE_REQUEST_FAILED, message, cause);
    }
}

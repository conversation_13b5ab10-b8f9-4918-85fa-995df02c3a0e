package pt.jumia.services.bill.domain.entities.filters;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.OrderDirection;

@Data
@Builder(toBuilder = true)
@AllArgsConstructor
public class DocumentSortFilters {
    @Builder.Default
    private Document.SortingFields field = Document.SortingFields.CREATED_AT;
    @Builder.Default
    private OrderDirection direction = OrderDirection.DESC;
}

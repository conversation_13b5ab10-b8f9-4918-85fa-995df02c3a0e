package pt.jumia.services.bill.domain.exceptions;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public abstract class CodedException extends RuntimeException {

    private static final long serialVersionUID = -7611708343328659778L;

    private final ErrorCode errorCode;

    public CodedException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public CodedException(ErrorCode errorCode, String message, Exception exception) {
        super(message, exception);
        this.errorCode = errorCode;
    }
}

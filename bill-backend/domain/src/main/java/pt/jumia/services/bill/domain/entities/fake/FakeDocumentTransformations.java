package pt.jumia.services.bill.domain.entities.fake;

import pt.jumia.services.bill.domain.entities.DocumentTransformation;

import java.util.List;

public interface FakeDocumentTransformations {

    DocumentTransformation ITEM_CODE_GENERATION_1 = DocumentTransformation.builder()
            .type(DocumentTransformation.EntityType.ITEM_CODE_GENERATION)
            .originalValue("item-1-original-value")
            .newValue("item-1-new-value")
            .build();

    DocumentTransformation ITEM_CODE_GENERATION_2 = DocumentTransformation.builder()
            .type(DocumentTransformation.EntityType.ITEM_CODE_GENERATION)
            .originalValue("item-2-original-value")
            .newValue("item-2-new-value")
            .build();

    List<DocumentTransformation> ALL = List.of(
            ITEM_CODE_GENERATION_1,
            ITEM_CODE_GENERATION_2
    );
}

package pt.jumia.services.bill.domain.entities.dtos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class OverrideFields {
    /**
     * Those set of fields are used to pass some values for overriding them in Judge template
     * generated by <PERSON>, this differs from country to another and extendable with extra fields
     * for example: if Kenya someday requested adding a field we do need to save it, however,
     * we will send this object with nullable values in all values except the one we need to pass
     * Overriding logic is provided at Bill->GenerateJudgeDocumentUseCase->returnAddOverridesAndGenerateJudgeSid
     * regardless the business line provided, it just checks for the non-null values and applies them
     * without saving over the pre-saved document aggregate
     */
    //Buyer legal name as mentioned in the Tax Authorities
    private String buyerLegalName;
    //Invoice Number as per the tax Authorities (for example UG needs this to be visible in The Template)
    private String invoiceNumberInsideTaxAuthorities;
    //Map containing the Line -> Category -> Sid as key and the new mapped value of it
    private Map<String, String> taxCategoriesMap;
}

package pt.jumia.services.bill.domain.exceptions;

import java.text.MessageFormat;

public class ReceiverTypeException extends CodedException {

    private static final long serialVersionUID = 4013536954508957126L;

    public ReceiverTypeException(String message) {
        super(ErrorCode.RECEIVER_TYPE, message);
    }

    public ReceiverTypeException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public static ReceiverTypeException notImplemented(String receiverType) {
        return new ReceiverTypeException(ErrorCode.RECEIVER_TYPE_NOT_IMPLEMENT,
                MessageFormat.format("Receiver type '{0}' is not implemented yet.", receiverType));
    }
}

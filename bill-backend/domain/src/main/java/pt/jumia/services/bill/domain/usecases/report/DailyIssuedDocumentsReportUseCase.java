package pt.jumia.services.bill.domain.usecases.report;


import com.neovisionaries.i18n.CountryCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.CommunicationsRequester;
import pt.jumia.services.bill.domain.entities.BusinessLine;
import pt.jumia.services.bill.domain.entities.DailyIssuedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentsUseCase;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class DailyIssuedDocumentsReportUseCase {

    private final ReadDocumentsUseCase readDocumentsUseCase;
    private final CommunicationsRequester communicationsRequester;
    private final OverallSettings overallSettings;

    public void generateDailyReport(LocalDateTime reportRequestTime) {

        List<BusinessLine> enabledBusinessLines = overallSettings.getDailyIssuedDocumentsReportSettings().getEnabledBusinessLines();

        for (BusinessLine businessLine : enabledBusinessLines) {
            int detailedReportHoursRange = overallSettings.getDailyIssuedDocumentsReportSettings()
                    .getDetailedReportTimeFrameInHours(businessLine.getShop().concat(
                            businessLine.getCountryCode().getAlpha2()));
            int overviewReportDaysRange = overallSettings.getDailyIssuedDocumentsReportSettings()
                    .getOverviewReportTimeFrameInDays(businessLine.getShop().concat(
                            businessLine.getCountryCode().getAlpha2()));

            LocalDateTime dayReportStart = reportRequestTime.minusHours(detailedReportHoursRange);
            LocalDateTime rangeReportStart = reportRequestTime.minusDays(overviewReportDaysRange);

            sendEmailsForReportBusinessLineSubscribers(DailyIssuedDocumentsReport
                    .builder()
                    .businessLine(businessLine)
                    .detailedReport(generateRangeReportForAllStatuses(
                            businessLine.getCountryCode(),
                            businessLine.getShop(),
                            dayReportStart,
                            reportRequestTime,
                            detailedReportHoursRange))
                    .overviewReport(generateRangeReportForAllStatuses(
                            businessLine.getCountryCode(),
                            businessLine.getShop(),
                            rangeReportStart,
                            reportRequestTime,
                            overviewReportDaysRange))
                    .build());
        }
    }

    private void sendEmailsForReportBusinessLineSubscribers(DailyIssuedDocumentsReport dailyIssuedDocumentsReport) {
        BusinessLine businessLine = dailyIssuedDocumentsReport.getBusinessLine();
        List<String> emails = overallSettings.getDailyIssuedDocumentsReportSettings().getCommunicationEmailsForBusinessLine(
                businessLine.getShop(),
                businessLine.getCountryCode().getAlpha2()
        );
        emails.forEach(email -> communicationsRequester.sendEmailByIssuedDocumentsReportToReceiver(dailyIssuedDocumentsReport, email));
    }

    private DailyIssuedDocumentsReport.RangeReport generateRangeReportForAllStatuses(CountryCode countryCode,
                                                                                     String shop,
                                                                                     LocalDateTime reportRangeStartDate,
                                                                                     LocalDateTime reportRangeEndDate,
                                                                                     int range) {

        Map<DocumentStatus, Long> statusesMap = new HashMap<DocumentStatus, Long>();
        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            statusesMap.put(documentStatus, readDocumentsUseCase.executeCountAll(DocumentFilter
                    .builder()
                    .countryCode(countryCode)
                    .shop(shop)
                    .flow(DocumentFlow.RETAIL)
                    .updatedAtFrom(reportRangeStartDate)
                    .updatedAtTo(reportRangeEndDate)
                    .status(documentStatus)
                    .build()));
        }
        return DailyIssuedDocumentsReport.RangeReport.builder()
                .reportRangeStartDate(reportRangeStartDate)
                .reportRangeEndDate(reportRangeEndDate)
                .statusesMap(statusesMap)
                .range(range)
                .build();
    }

}

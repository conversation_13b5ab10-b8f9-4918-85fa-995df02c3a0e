package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.math.BigDecimal;
import java.util.List;

@Component
@RequiredArgsConstructor
public class DiscountEqualDiscountLinesValidator implements Validator{

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        BigDecimal sum = aggregate.getLines().stream()
                .map(documentLine -> documentLine.getDiscount().getAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (!aggregate.getDocument().getDiscountAmount().equals(sum)) {
            return List.of(new ValidationError(
                    ErrorCode.DISCOUNT_NOT_EQUAL_DISCOUNT_LINES,
                    "Discount amount not equal the sum of discounts in document lines."
            ));
        }

        return List.of();
    }
}

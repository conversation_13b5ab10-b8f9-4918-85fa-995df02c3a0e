package pt.jumia.services.bill.domain.caches;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.neovisionaries.i18n.CountryCode;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import lombok.EqualsAndHashCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pt.jumia.services.bill.domain.AfromsRequester;
import pt.jumia.services.bill.domain.entities.AfromsProductDetails;
import pt.jumia.services.bill.domain.properties.NetworkProperties;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
public class AfromsCache {

    private static final String CACHE_METRIC_NAME = "afroms.product_details.cache";

    private final AfromsRequester afromsRequester;
    private final Cache<<PERSON>acheK<PERSON>, AfromsProductDetails> cache;

    @Autowired
    public AfromsCache(
            AfromsRequester afromsRequester,
            NetworkProperties networkProperties,
            MeterRegistry meterRegistry
    ) {
        this.afromsRequester = afromsRequester;
        this.cache = CaffeineCacheMetrics.monitor(meterRegistry, Caffeine.newBuilder()
                .recordStats()
                .maximumSize(networkProperties.getAfroms().getCache().getMaxSize())
                .expireAfterAccess(networkProperties.getAfroms().getCache().getTtlMinutes(), TimeUnit.MINUTES)
                .expireAfterWrite(networkProperties.getAfroms().getCache().getTtlMinutes(), TimeUnit.MINUTES)
                .build(), CACHE_METRIC_NAME);
    }

    public AfromsProductDetails getAfromsCacheProductDetails(
            CountryCode countryCode,
            String shop,
            String itemCode,
            String variantId
    ) {
        CacheKey key = CacheKey.of(itemCode + countryCode, variantId == null ? "" : variantId);

        Optional<AfromsProductDetails> optionalAfrOmsProductDetails = Optional.ofNullable(this.cache.getIfPresent(key));
        if (optionalAfrOmsProductDetails.isPresent()) {
            return optionalAfrOmsProductDetails.get();
        }

        AfromsProductDetails afromsProductDetails = afromsRequester.getProductDetails(countryCode.getAlpha2(),
                shop,
                variantId == null ? itemCode : String.format("%s-%s", itemCode, variantId));
        this.cache.put(key, afromsProductDetails);

        return afromsProductDetails;
    }

    @EqualsAndHashCode
    private static class CacheKey {
        private final String ucrId;
        private final String ucrPath;

        private CacheKey(String ucrId, String ucrPath) {
            this.ucrId = ucrId;
            this.ucrPath = ucrPath;
        }

        public static CacheKey of(String ucrId, String ucrPath) {
            return new CacheKey(ucrId, ucrPath);
        }
    }
}

package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class DocumentLineAppliedTaxFieldsValidation implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        List<ValidationError> errors = new ArrayList<>();

        aggregate.getLines().forEach(line -> {
            line.getAppliedTaxes().forEach(tax -> {
            if (tax.getTaxFixedAmount() == null && tax.getTaxRate() == null) {
                errors.add(new ValidationError(
                        ErrorCode.INVALID_LINE_APPLIED_TAX,
                        "Tax fixed amount or tax rate must be provided"
                ));
            }
            });
        });
        return errors;
    }
}

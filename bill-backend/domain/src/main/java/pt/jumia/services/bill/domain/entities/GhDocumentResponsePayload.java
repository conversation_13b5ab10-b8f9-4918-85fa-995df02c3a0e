package pt.jumia.services.bill.domain.entities;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class GhDocumentResponsePayload {

    private GhDocumentResponse response;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GhDocumentResponse {
        private String distributorTin;
        private GhMessageResponseDto message;
        private String qrCode;
        private String status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GhMessageResponseDto {

        private String num;
        private String ysdcid;
        private String ysdcrecnum;
        private String ysdcintdata;
        private String ysdcregsig;
        private String ysdcmrc;
        private String ysdcmrctim;
        private String ysdctime;
        private String flag;
        private String ysdcitems;
    }
}

package pt.jumia.services.bill.domain.repository;

import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

public interface DocumentRepository {

    List<Document> findAll(DocumentFilter documentAggregatesFilters);

    List<Document> findAll(DocumentFilter documentAggregatesFilters,
                           DocumentSortFilters sortFilters,
                           PageFilters pageFilters);

    long count(DocumentFilter documentAggregatesFilters);

    boolean existsById(UUID id);

    Optional<Document> findById(UUID id);

    boolean existsBySidInErrors(String sid);

    Optional<DocumentId> findDocumentIdBySidInSuccessStatus(String sid);

    Document save(Document document);

    Document updateStatus(UUID documentId, DocumentStatus status);

    Document update(UUID id, Document document, boolean changeUpdateData);

    int migrateDecimalFields(Long startId, Long endId);

    int migrateAuditDecimalFields(Long startId, Long endId);

    Map<String, Object> getDecimalFieldsMigrationStatus();
}

package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.IssuerTemplate;
import pt.jumia.services.bill.domain.repository.IssuerTemplateRepository;

@Component
@RequiredArgsConstructor
@Slf4j
public class CreateIssuerTemplateUseCase {

    private final IssuerTemplateRepository issuerTemplateRepository;

    public IssuerTemplate execute(IssuerTemplate issuerTemplate) {
        return issuerTemplateRepository.save(issuerTemplate);
    }

}

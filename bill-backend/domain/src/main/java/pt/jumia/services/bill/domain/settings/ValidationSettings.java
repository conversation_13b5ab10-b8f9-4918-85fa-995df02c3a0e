package pt.jumia.services.bill.domain.settings;

import lombok.Data;

@Data
public class ValidationSettings {

    public static final String KE_LINES_PACKAGE_UNIT_VALIDATION = "documents.validation.ke.lines-package-unit";
    public static final String KE_TRANSACTION_TYPE_VALIDATION = "documents.validation.ke.transaction-type";
    public static final String KE_TRANSACTION_PROGRESS_VALIDATION = "documents.validation.ke.transaction-progress";

    private final OverallSettings.OverridableSetting<Boolean> keLinesPackageUnitValidation;
    private final OverallSettings.OverridableSetting<Boolean> keTransactionTypeValidation;
    private final OverallSettings.OverridableSetting<Boolean> keTransactionProgressValidation;

    public ValidationSettings(OverallSettings overallSettings) {

        this.keLinesPackageUnitValidation = overallSettings.createSetting(
                KE_LINES_PACKAGE_UNIT_VALIDATION,
                true,
                Boolean::parseBoolean);

        this.keTransactionTypeValidation = overallSettings.createSetting(
                KE_TRANSACTION_TYPE_VALIDATION,
                true,
                Boolean::parseBoolean);
        this.keTransactionProgressValidation = overallSettings.createSetting(
                KE_TRANSACTION_PROGRESS_VALIDATION,
                true,
                Boolean::parseBoolean);

    }

    public boolean getKeLinesPackageUnitValidation(String shop, String countryCode) {
        return this.keLinesPackageUnitValidation.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getKeTransactionTypeValidation(String shop, String countryCode) {
        return this.keTransactionTypeValidation.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

    public boolean getKeTransactionProgressValidation(String shop, String countryCode) {
        return this.keTransactionProgressValidation.getOverride(
                OverallSettings.OverridableSetting.generateShopCountryOverrideKey(shop, countryCode));
    }

}

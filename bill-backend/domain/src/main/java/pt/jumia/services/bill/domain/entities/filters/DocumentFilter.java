package pt.jumia.services.bill.domain.entities.filters;

import com.neovisionaries.i18n.CountryCode;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.ReceiverType;

import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class DocumentFilter {

    private List<Document.Details> include;
    private UUID id;
    private String sid;
    private String taxDocumentNumber;
    private DocumentFlow flow;
    private DocumentStatus status;
    private List<DocumentStatus> statuses;
    private String referenceNumber;
    private String issuerTin;
    private String issuerLegalName;
    private String receiverTin;
    private LocalDateTime createdAtFrom;
    private LocalDateTime createdAtTo;
    private LocalDateTime updatedAtFrom;
    private LocalDateTime updatedAtTo;
    private LocalDateTime issuedDateFrom;
    private LocalDateTime issuedDateTo;
    private CountryCode receiverCountry;
    private String receiverName;
    private ReceiverType receiverType;
    private List<DocumentTransformation.EntityType> documentTransformationTypes;
    private Currency currency;
    private CountryCode countryCode;
    private String shop;
    private List<CountryCode> countryCodes;
    private DocumentType type;
    private LocalDateTime receivedDateFrom;
    private LocalDateTime receivedDateTo;
    private Boolean reviewed;

}

package pt.jumia.services.bill.domain.usecases.settings;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.SettingRepository;

import java.util.List;
import java.util.Optional;

@Component
@AllArgsConstructor
public class ReadSettingUseCase {

    private final SettingRepository settingRepository;

    public List<Setting> fetchAll() {
        return settingRepository.findAll();
    }

    public Setting fetchById(long id) {

        Optional<Setting> optSetting = settingRepository.findById(id);
        if (!optSetting.isPresent()) {
            throw EntityNotFoundException.createNotFound(Setting.class, id);
        }
        return optSetting.get();
    }
}

package pt.jumia.services.bill.domain.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Some JSON utilities
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JsonUtils {

    private final ObjectMapper objectMapper;

    public String toJsonOrNull(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize json", e);
            return null;
        }
    }

    public String toPrettyJsonOrNull(Object object) {
        try {
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize json", e);
            return null;
        }
    }

    public <T> T fromJsonOrNull(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize json", e);
            return null;
        }
    }

    public <T> T fromJsonOrNull(String json, TypeReference<T> valueTypeRef) {
        try {
            return objectMapper.readValue(json, valueTypeRef);
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize json", e);
            return null;
        }
    }

    public <T> List<T> fromJsonListOrNull(String list, Class<T> clazz){
        try {
            return objectMapper.readerForListOf(clazz).readValue(list);
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize json", e);
            return null;
        }
    }

}

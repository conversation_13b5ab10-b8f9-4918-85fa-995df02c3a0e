package pt.jumia.services.bill.domain.usecases.settings;


import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.repository.SettingRepository;
import pt.jumia.services.bill.domain.settings.OverallSettings;

import java.util.List;

@Component
@AllArgsConstructor
public class ReloadSettingsUseCase {

    private final SettingRepository settingRepository;
    private final OverallSettings overallSettings;

    public void execute() {
        List<Setting> settings = settingRepository.findAll();
        this.overallSettings.refreshAllSettings(settings);
    }

    public void execute(List<Setting> settings) {
        overallSettings.refreshAllSettings(settings);
    }
}

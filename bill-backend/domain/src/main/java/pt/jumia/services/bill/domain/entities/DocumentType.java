package pt.jumia.services.bill.domain.entities;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum DocumentType {

    SALES_INVOICE(false, false),
    SALES_CREDIT_NOTE(false, false),
    SALES_CREDIT_MEMO(false, false),
    SALES_DEBIT_NOTE(false, false),
    PURCHASE_INVOICE(false, false),
    PURCHASE_CREDIT_NOTE(false, false),
    PURCHASE_DEBIT_NOTE(false, false);

    private final boolean issuedReasonRequired;
    private final boolean originalDocumentRequired;

}

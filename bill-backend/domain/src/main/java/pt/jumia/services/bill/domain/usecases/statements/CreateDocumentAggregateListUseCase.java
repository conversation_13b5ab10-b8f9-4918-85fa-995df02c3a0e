package pt.jumia.services.bill.domain.usecases.statements;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.dtos.AdjustableDocumentPartDto;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationChain;
import pt.jumia.services.bill.domain.usecases.documents.validators.DocumentValidationException;
import pt.jumia.services.bill.domain.usecases.documents.validators.ValidationError;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class CreateDocumentAggregateListUseCase {

    private final DocumentValidationChain documentValidationChain;
    private final OverallSettings overallSettings;

    public List<DocumentAggregate> execute(DocumentAggregate documentAggregate) {
        validateDocumentsBeforeSplitting(documentAggregate);

        boolean containsPositiveLines = false;
        boolean containsNegativeLines = false;
        List<DocumentLine> lines = documentAggregate.getLines();
        for (DocumentLine line : lines) {
            if (line.isPositive()) {
                containsPositiveLines = true;
            } else {
                containsNegativeLines = true;
            }

            if (containsPositiveLines && containsNegativeLines) {
                return this.split(documentAggregate);
            }
        }

        DocumentType documentType = containsPositiveLines ? DocumentType.SALES_INVOICE :
                getNegativeDocumentType(documentAggregate);

        setTypeAndValidate(documentAggregate, documentType);
        return List.of(documentAggregate);
    }

    private List<DocumentAggregate> split(DocumentAggregate documentAggregate) {
        List<DocumentAggregate> splitDocuments = new ArrayList<>();
        DocumentType negativeValuesDocumentType = getNegativeDocumentType(documentAggregate);

        AdjustableDocumentPartDto adjustableDocumentPartDtoForPositiveLines = AdjustableDocumentPartDto.builder().build();
        AdjustableDocumentPartDto adjustableDocumentPartDtoForNegativeLines = AdjustableDocumentPartDto.builder().build();

        List<DocumentLine> positiveLines = new ArrayList<>();
        List<DocumentLine> negativeLines = new ArrayList<>();

        documentAggregate.getLines().forEach(documentLine -> {
            if (documentLine.isPositive()) {
                DocumentLine newPositiveLine = documentLine.toBuilder().position(positiveLines.size()).build();
                positiveLines.add(newPositiveLine);
                adjustableDocumentPartDtoForPositiveLines.appendLine(documentLine);
            } else {
                DocumentLine newNegativeLine = documentLine.toBuilder().position(negativeLines.size()).build();
                negativeLines.add(newNegativeLine);
                adjustableDocumentPartDtoForNegativeLines.appendLine(documentLine);
            }
        });

        splitDocuments.add(buildDocumentAggregate(documentAggregate, DocumentType.SALES_INVOICE, positiveLines,
                adjustableDocumentPartDtoForPositiveLines));
        splitDocuments.add(buildDocumentAggregate(documentAggregate, negativeValuesDocumentType, negativeLines,
                adjustableDocumentPartDtoForNegativeLines));

        assertAmounts(documentAggregate, adjustableDocumentPartDtoForPositiveLines,
                adjustableDocumentPartDtoForNegativeLines);
        return splitDocuments;
    }

    private DocumentType getNegativeDocumentType(DocumentAggregate documentAggregate) {
        return overallSettings.getDocumentTypeSettings().getNegativeDocumentType(
                documentAggregate.getDocument().getShop(),
                documentAggregate.getDocument().getCountry().getAlpha2());
    }

    private void validateDocumentsBeforeSplitting(DocumentAggregate documentAggregate) {
        List<ValidationError> errors = documentValidationChain.runPreSplitValidations(documentAggregate);
        if (!CollectionUtils.isEmpty(errors)) {
            log.warn("Statement '{}' has some validation errors {}", documentAggregate.getDocument().getSid(), errors);
            throw new DocumentValidationException(
                    String.format("Statement %s has some validation errors", documentAggregate.getDocument().getSid()),
                    errors);
        }
    }

    private void validateDocumentsAfterSplitting(DocumentAggregate splitDocuments) {
        List<ValidationError> errors = documentValidationChain.runPostSplitValidations(splitDocuments);
        if (!CollectionUtils.isEmpty(errors)) {
            log.warn("Document '{}' has some validation errors {} after splitting",
                    splitDocuments.getDocument().getSid(), errors);
            throw new DocumentValidationException(
                    String.format("Document %s has some validation errors after splitting",
                            splitDocuments.getDocument().getSid()),
                    errors);
        }
    }

    private void assertAmounts(DocumentAggregate originalDocumentAggregate, AdjustableDocumentPartDto positiveDocument,
                               AdjustableDocumentPartDto negativeDocument) {
        if (positiveDocument.getTotalAmount().add(negativeDocument.getTotalAmount())
                .compareTo(originalDocumentAggregate.getDocument().getTotalAmount()) != 0
                || positiveDocument.getNetAmount().add(negativeDocument.getNetAmount())
                .compareTo(originalDocumentAggregate.getDocument().getNetAmount()) != 0
                || positiveDocument.getTaxAmount().add(negativeDocument.getTaxAmount())
                .compareTo(originalDocumentAggregate.getDocument().getTaxAmount()) != 0) {

            log.warn("Document '{}' has wrong calculation after splitting", originalDocumentAggregate.getDocument().getSid());
            throw DocumentValidationException.invalidAmountsAfterSplitting(originalDocumentAggregate);
        }
    }

    private void setTypeAndValidate(DocumentAggregate documentAggregate, DocumentType documentType) {
        Document document = documentAggregate.getDocument().toBuilder()
                .status(DocumentStatus.NEW)
                .type(documentType)
                .sid(documentType.equals(DocumentType.SALES_INVOICE) ?
                        "I-" + documentAggregate.getDocument().getSid() :
                        "C-" + documentAggregate.getDocument().getSid())
                .build();
        documentAggregate.setDocument(document);

        if (!document.isPositive()) {
            documentAggregate.convertAmountsToPositiveValue();
        }
        validateDocumentsAfterSplitting(documentAggregate);
    }

    private DocumentAggregate buildDocumentAggregate(DocumentAggregate documentAggregate, DocumentType documentType, List<DocumentLine> documentLines,
                                                     AdjustableDocumentPartDto adjustableDocumentPartDto) {
        Document document = documentAggregate.getDocument().toBuilder()
                .lineCount(documentLines.size())
                .totalAmount(adjustableDocumentPartDto.getTotalAmount())
                .netAmount(adjustableDocumentPartDto.getNetAmount())
                .taxAmount(adjustableDocumentPartDto.getTaxAmount())
                .discountAmount(adjustableDocumentPartDto.getDiscountAmount())
                .generateId()
                .build();

        for (DocumentLine documentLine : documentLines) {
            documentLine.setDocument(document);
        }

        for (TaxCategoryTotal taxCategoryTotal : adjustableDocumentPartDto.getTaxCategoryTotals()) {
            taxCategoryTotal.setDocument(document);
            taxCategoryTotal.setTotalAmount(adjustableDocumentPartDto.getTotalAmount());
            taxCategoryTotal.setNetAmount(adjustableDocumentPartDto.getNetAmount());
        }

        DocumentAggregate newDocumentAggregate = DocumentAggregate
                .builder()
                .document(document)
                .lines(documentLines)
                .taxCategoryTotals(adjustableDocumentPartDto.getTaxCategoryTotals())
                .taxAuthoritiesDetails(documentAggregate.getTaxAuthoritiesDetails())
                .build();

        setTypeAndValidate(newDocumentAggregate, documentType);
        return newDocumentAggregate;
    }
}

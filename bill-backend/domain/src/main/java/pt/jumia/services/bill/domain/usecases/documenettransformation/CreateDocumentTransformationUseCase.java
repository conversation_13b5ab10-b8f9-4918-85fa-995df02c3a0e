package pt.jumia.services.bill.domain.usecases.documenettransformation;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
@Component
@RequiredArgsConstructor
public class CreateDocumentTransformationUseCase {

    private final DocumentTransformationRepository documentTransformationRepository;

    public DocumentTransformation execute(DocumentTransformation documentTransformation) {

        if (documentTransformation == null) {
            throw new IllegalArgumentException(" document transformation can not be null.");
        }

        return documentTransformationRepository.insert(
                documentTransformation.toBuilder()
                        .createdAt(LocalDateTime.now(ZoneOffset.UTC))
                        .updatedAt(LocalDateTime.now(ZoneOffset.UTC))
                        .build()

        );
    }
}

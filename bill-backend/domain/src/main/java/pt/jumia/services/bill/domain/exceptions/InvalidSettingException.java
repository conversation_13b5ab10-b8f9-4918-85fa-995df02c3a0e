package pt.jumia.services.bill.domain.exceptions;

public class InvalidSettingException extends CodedException {

    private static final long serialVersionUID = 4890832690320514951L;

    private InvalidSettingException(String message) {
        super(ErrorCode.INVALID_SETTING, message);
    }

    public static InvalidSettingException build(String message) {
        return new InvalidSettingException(message);
    }
}

package pt.jumia.services.bill.domain.entities.filters;

import com.neovisionaries.i18n.CountryCode;
import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder(toBuilder = true)
public class DocumentRetryFilters {

    private String id;
    private UUID relatedEntityId;
    private String relatedEntityCode;
    private DocumentType type;
    private String shop;
    private CountryCode country;
    private List<DocumentStatus> statuses;
    private Integer statusCode;
    private String errorCode;
    private Integer nDays;
    private LocalDateTime createdAtFrom;
    private LocalDateTime createdAtTo;
    private LocalDateTime issuedDateFrom;
    private LocalDateTime issuedDateTo;
}

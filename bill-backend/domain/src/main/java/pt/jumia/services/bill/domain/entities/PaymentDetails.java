package pt.jumia.services.bill.domain.entities;

import lombok.Builder;
import lombok.Value;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.UUID;

@Value
@Builder(toBuilder = true)
public class PaymentDetails {

    UUID id;
    String bankName;
    String bankAddress;
    String bankAccountNo;
    String bankAccountIBAN;
    String swiftCode;
    String terms;
    String createdBy;
    LocalDateTime createdAt;

    public PaymentDetails withoutDbField() {
        return this.toBuilder()
                .id(null)
                .createdAt(null)
                .createdBy(null)
                .build();
    }

    public static class PaymentDetailsBuilder {

        public PaymentDetailsBuilder generateId() {
            id = UUID.nameUUIDFromBytes(this.generateStringId().getBytes(StandardCharsets.UTF_8));
            return this;
        }

        private String generateStringId() {
            return "PaymentDetails{" +
                    "bankName=" + bankName +
                    ", bankAddress='" + bankAddress + '\'' +
                    ", bankAccountNo='" + bankAccountNo + '\'' +
                    ", bankAccountIBAN='" + bankAccountIBAN + '\'' +
                    ", swiftCode='" + swiftCode + '\'' +
                    ", terms='" + terms + '\'' +
                    '}';
        }

    }
}

package pt.jumia.services.bill.domain.exceptions.document;

import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.text.MessageFormat;

public class MissingDocumentPdfException extends CodedException {

    private static final long serialVersionUID = 8372555446767318190L;

    private MissingDocumentPdfException(String message) {
        super(ErrorCode.MISSING_DOCUMENT_PDF, message);
    }

    public static MissingDocumentPdfException isNull(Document document) {
        return new MissingDocumentPdfException(MessageFormat.format("Document {0} has no available PDF", document.getSid()));
    }
}

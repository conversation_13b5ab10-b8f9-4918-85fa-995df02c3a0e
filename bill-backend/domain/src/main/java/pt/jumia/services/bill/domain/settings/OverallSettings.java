package pt.jumia.services.bill.domain.settings;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.exceptions.InvalidSettingException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

@Component
@Slf4j
@Getter
public class OverallSettings {

    @Getter(AccessLevel.NONE)
    private final List<OverridableSetting<?>> settings = new ArrayList<>();

    private final CommunicationsSettings communicationsSettings;
    private final JudgeSettings judgeSettings;
    private final ExportationSettings exportationSettings;
    private final DailyIssuedDocumentsReportSettings dailyIssuedDocumentsReportSettings;
    private final DailyReceivedDocumentsReportSettings dailyReceivedDocumentsReportSettings;
    private final OverrideIssuerSettings overrideIssuerSettings;
    private final CancellationSettings cancellationSettings;
    private final RejectionSettings rejectionSettings;
    private final ApprovalSettings approvalSettings;
    private final DocumentTypeSettings documentTypeSettings;
    private final KafkaSettings kafkaSettings;
    private final ValidationSettings validationSettings;

    public OverallSettings() {
        this.communicationsSettings = new CommunicationsSettings(this);
        this.judgeSettings = new JudgeSettings(this);
        this.exportationSettings = new ExportationSettings(this);
        this.dailyIssuedDocumentsReportSettings = new DailyIssuedDocumentsReportSettings(this);
        this.dailyReceivedDocumentsReportSettings = new DailyReceivedDocumentsReportSettings(this);
        this.overrideIssuerSettings = new OverrideIssuerSettings(this);
        this.cancellationSettings = new CancellationSettings(this);
        this.rejectionSettings = new RejectionSettings(this);
        this.approvalSettings = new ApprovalSettings(this);
        this.documentTypeSettings = new DocumentTypeSettings(this);
        this.kafkaSettings = new KafkaSettings(this);
        this.validationSettings = new ValidationSettings(this);
    }

    public <E> OverridableSetting<E> createSetting(String property, E defaultValue, Function<String, E> parseFunction) {
        OverridableSetting<E> setting = new OverridableSetting<>(property, defaultValue, parseFunction);
        this.settings.add(setting);
        return setting;
    }

    public void refreshAllSettings(List<Setting> settings) {
        this.settings.forEach(OverridableSetting::reset);
        log.info("Caching settings: {}", settings);
        settings.forEach(this::setSetting);
    }

    public void validateSetting(Setting setting) {
        for (OverridableSetting<?> overridableSetting : this.settings) {
            if (overridableSetting.isValid(setting)) {
                return;
            }
        }

        throw InvalidSettingException.build(String.format("Unhandled setting %s", setting));
    }

    public void setSetting(Setting setting) {
        for (OverridableSetting<?> overridableSetting : this.settings) {
            if (overridableSetting.putIfMatches(setting)) {
                return;
            }
        }

        log.warn("Unhandled setting {}", setting);
    }

    public static class OverridableSetting<E> {
        private final String property;
        private final E fallback;
        private final Function<String, E> parseFunction;
        private E defaultValue;
        private Map<String, E> overrides = new HashMap<>();

        private OverridableSetting(String property, E defaultValue, Function<String, E> parseFunction) {
            this.property = property;
            this.fallback = defaultValue;
            this.defaultValue = defaultValue;
            this.parseFunction = parseFunction;
        }

        public E getDefault() {
            return defaultValue;
        }

        public static String generateShopCountryOverrideKey(String shop, String country) {
            return shop.toLowerCase(Locale.ENGLISH) + country.toUpperCase(Locale.ENGLISH);
        }

        public static String generateShopDocumentTypeOverrideKey(String shop, DocumentType documentType) {
            return shop.toLowerCase(Locale.ENGLISH) + documentType.name();
        }

        /**
         * Iterates over all override keys and returns the value associated with the first
         *
         * @param overrideKeys the override keys to iterate over (in precedence order)
         * @return the setting value
         */
        E getOverride(String... overrideKeys) {
            if (overrideKeys != null) {
                for (String overrideKey : overrideKeys) {
                    E settingValue = overrides.get(overrideKey);
                    if (settingValue != null) {
                        return settingValue;
                    }
                }
            }
            return defaultValue;
        }

        List<String> getOverrideKeys() {
            return new ArrayList<>(overrides.keySet());
        }

        Optional<E> parse(Setting setting) {
            if (this.property.equals(setting.getProperty())) {
                return Optional.of(this.parseFunction.apply(setting.getValue()));
            }

            return Optional.empty();
        }

        boolean isValid(Setting setting) {
            try {
                return this.parse(setting).isPresent();
            } catch (Exception e) {
                log.info("Invalid setting value {}: {}", setting, e);
                throw InvalidSettingException.build(String.format("Invalid setting value '%s'", setting.getValue()));
            }
        }

        boolean putIfMatches(Setting setting) {
            if (this.property.equals(setting.getProperty())) {
                E value = this.parseFunction.apply(setting.getValue());
                this.putSetting(setting, value);

                return true;
            }

            return false;
        }

        void putSetting(Setting setting, E value) {
            if (setting.isTypeOverride()) {
                this.overrides.put(setting.getOverrideKey(), value);
            } else {
                this.defaultValue = value;
            }
        }

        private void reset() {
            this.defaultValue = this.fallback;
            this.overrides = new HashMap<>();
        }
    }
}

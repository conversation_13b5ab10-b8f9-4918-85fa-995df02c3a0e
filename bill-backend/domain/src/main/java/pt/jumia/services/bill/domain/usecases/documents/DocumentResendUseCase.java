package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentAggregateRepository;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class DocumentResendUseCase {

    private final DocumentAggregateRepository documentAggregateRepository;
    private final TaxiRequester taxiRequester;

    public void execute(Document document) {

        if (DocumentStatus.getResendStatuses().contains(document.getStatus())) {
            log.info("Resending document sid: [{}]", document.getSid());
            executeImpl(document);
        }
    }

    public void executeForNewDocument(Document document) {

        if (document.getStatus().equals(DocumentStatus.NEW)) {
            executeImpl(document);
        }
    }

    private void executeImpl(Document document) {

            DocumentAggregate documentAggregate = documentAggregateRepository.findByDocumentId(document.getId())
                    .orElseThrow(() -> EntityNotFoundException.createNotFound(Document.class, document.getId()));

            log.info("Resending [{}] to TaxI", documentAggregate.getDocument().getSid());

            if (List.of(DocumentType.SALES_INVOICE, DocumentType.PURCHASE_INVOICE, DocumentType.SALES_CREDIT_MEMO, DocumentType.SALES_DEBIT_NOTE)
                    .contains(documentAggregate.getDocument().getType())) {
                taxiRequester.pushInvoice(documentAggregate);
            } else {
                taxiRequester.pushCreditNote(documentAggregate.getDocument().getOriginalDocument().getSid(), documentAggregate);
        }
    }
}

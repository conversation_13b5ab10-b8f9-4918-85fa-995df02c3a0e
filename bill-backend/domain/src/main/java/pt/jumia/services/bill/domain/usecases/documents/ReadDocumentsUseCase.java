package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.entities.filters.DocumentSortFilters;
import pt.jumia.services.bill.domain.entities.filters.PageFilters;
import pt.jumia.services.bill.domain.exceptions.EntityNotFoundException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class ReadDocumentsUseCase {
    private final DocumentRepository documentRepository;

    public Document readDocumentById(UUID documentId) {
        return documentRepository.findById(documentId)
                .orElseThrow(() -> EntityNotFoundException.createNotFound(Document.class, documentId));
    }

    public Document readDocumentBySidAndSuccessStatus(String documentSid) {
        List<DocumentStatus> successfulStatuses = new ArrayList<>();

        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            if (documentStatus.isSuccessful()) {
                successfulStatuses.add(documentStatus);
            }
        }

        return readLastDocumentWithSidAndSpecificStatuses(documentSid, successfulStatuses);
    }


    public Document readDocumentInNonFinalErrorStatusesBySid(String documentSid) {
        List<DocumentStatus> nonFinalErrorStatuses = new ArrayList<>();

        for (DocumentStatus documentStatus : DocumentStatus.values()) {
            if (!DocumentStatus.TAX_ERROR_RETRIED.equals(documentStatus)
                    && !DocumentStatus.TAX_ERROR_ACKED.equals(documentStatus)) {
                nonFinalErrorStatuses.add(documentStatus);
            }
        }

        return readLastDocumentWithSidAndSpecificStatuses(documentSid, nonFinalErrorStatuses);
    }

    private Document readLastDocumentWithSidAndSpecificStatuses(String documentSid, List<DocumentStatus> filteredStatuses) {

        DocumentFilter documentFilter = DocumentFilter.builder()
                .sid(documentSid)
                .statuses(filteredStatuses)
                .build();

        List<Document> documents = documentRepository.findAll(documentFilter);
        if (documents.isEmpty()) {
            throw EntityNotFoundException.createNotFoundBySid(Document.class,
                    documentSid);
        }
        return documents.stream().findFirst().get();
    }

    public List<Document> execute(DocumentFilter filter,
                                  DocumentSortFilters sortFilters,
                                  PageFilters pageFilters) {
        return documentRepository.findAll(
                filter,
                sortFilters,
                pageFilters
        );
    }

    public Long executeCountAll(DocumentFilter documentFilters) {
        return documentRepository.count(documentFilters);
    }
}

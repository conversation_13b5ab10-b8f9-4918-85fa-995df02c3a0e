package pt.jumia.services.bill.domain.usecases.documents.processors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.filters.DocumentFilter;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class MoveErrorDocumentsToRetriedProcessor implements Processor {

    private final DocumentRepository documentRepository;

    @Override
    public void process(DocumentAggregate aggregate) {
        DocumentFilter filter = DocumentFilter.builder()
                .sid(aggregate.getDocument().getSid())
                .status(DocumentStatus.TAX_ERROR_ACKED)
                .include(List.of(Document.Details.values()))
                .build();

        documentRepository.findAll(filter).stream()
                .filter(documentInError -> !documentInError.getId().equals(aggregate.getDocument().getId()))
                .forEach(documentInError -> {
                    log.info("Moving document '{}' to 'TAX_ERROR_RETRIED'", documentInError.getSid());
                    documentInError.setStatus(DocumentStatus.TAX_ERROR_RETRIED);
                    documentRepository.save(documentInError);
                });
    }
}

package pt.jumia.services.bill.domain.entities;

import lombok.Getter;

import java.util.Set;

@Getter
public enum IssuedReasonCode {
    DAMAGED_PRODUCTS(Set.of(DocumentType.SALES_CREDIT_NOTE, DocumentType.PURCHASE_CREDIT_NOTE)),
    PURCHASE_CANCELLED(Set.of(DocumentType.SALES_CREDIT_NOTE, DocumentType.PURCHASE_CREDIT_NOTE)),
    INVOICE_ERRORS(Set.of(DocumentType.SALES_CREDIT_NOTE, DocumentType.PURCHASE_CREDIT_NOTE)),
    INVOICE_WAIVE(Set.of(DocumentType.SALES_CREDIT_NOTE, DocumentType.PURCHASE_CREDIT_NOTE)),
    OTHERS(Set.of(DocumentType.SALES_CREDIT_NOTE, DocumentType.PURCHASE_CREDIT_NOTE), true);

    private final Set<DocumentType> supportedByDocuments;
    private final boolean notesRequired;

    IssuedReasonCode(Set<DocumentType> supportedByDocuments) {
        this.supportedByDocuments = supportedByDocuments;
        this.notesRequired = false;
    }

    IssuedReasonCode(Set<DocumentType> supportedByDocuments, boolean notesRequired) {
        this.supportedByDocuments = supportedByDocuments;
        this.notesRequired = notesRequired;
    }

    public boolean supportedByDocumentType(DocumentType documentType) {
        return getSupportedByDocuments().contains(documentType);
    }
}

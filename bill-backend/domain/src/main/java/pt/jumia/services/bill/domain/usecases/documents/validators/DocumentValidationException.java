package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.CodedException;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.List;

@Setter
@Getter
@ToString
@EqualsAndHashCode(callSuper = true)
public class DocumentValidationException extends CodedException {

    private static final long serialVersionUID = 552150152180288854L;

    private List<ValidationError> errors;

    public DocumentValidationException(String message, List<ValidationError> errors) {
        super(ErrorCode.INVALID_DOCUMENT, message);
        this.errors = errors;
    }

    public DocumentValidationException(ErrorCode errorCode, String message) {
        super(errorCode, message);
    }

    public static DocumentValidationException invalidAmountsAfterSplitting(DocumentAggregate documentAggregate) {
        return new DocumentValidationException(ErrorCode.AMOUNTS_AFTER_SPLITTING_NOT_EQUAL_TO_ORIGINAL_AMOUNTS,
            String.format("Document %s has wrong calculation after splitting",
               documentAggregate.getDocument().getSid()));
    }
}

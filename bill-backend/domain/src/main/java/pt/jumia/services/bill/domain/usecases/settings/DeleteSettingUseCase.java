package pt.jumia.services.bill.domain.usecases.settings;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.DataEventsNotificator;
import pt.jumia.services.bill.domain.entities.Setting;
import pt.jumia.services.bill.domain.repository.SettingRepository;

@Component
@AllArgsConstructor
public class DeleteSettingUseCase {

    private final ReadSettingUseCase readSettingUseCase;
    private final SettingRepository settingRepository;
    private final DataEventsNotificator dataEventsNotificator;

    public Setting execute(long id) {

        Setting setting = readSettingUseCase.fetchById(id);
        if (!setting.isTypeOverride()) {
            throw new IllegalArgumentException("Only settings of type OVERRIDE can be deleted.");
        }

        settingRepository.deleteById(id);
        dataEventsNotificator.notifySettingChanges();
        return setting;
    }
}

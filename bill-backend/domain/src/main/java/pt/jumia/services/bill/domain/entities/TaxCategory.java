package pt.jumia.services.bill.domain.entities;

import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
public enum TaxCategory {
    EXEMPT(true),
    VAT_GENERAL(true),
    ZERO_RATE(true),
    EXCISE_RATE(true),
    EXCISE_AMOUNT(false),
    SERVICE_RATE(true),
    SERVICE_AMOUNT(false),
    OTHER_RATE(true),
    OTHER_AMOUNT(false),
    MISSING_VALUE_REVERSED_MAPPING(false),
    NO_TAX(true);

    private final boolean ratedTax;

    private boolean supportsRate() {
        return ratedTax;
    }

    private boolean supportsFixedAmount() {
        return !ratedTax;
    }
}

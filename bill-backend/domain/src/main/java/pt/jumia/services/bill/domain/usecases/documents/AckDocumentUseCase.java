package pt.jumia.services.bill.domain.usecases.documents;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.exceptions.ConflictOperationException;
import pt.jumia.services.bill.domain.repository.DocumentRepository;

@Component
@RequiredArgsConstructor
public class AckDocumentUseCase {

    private final DocumentRepository documentRepository;

    private final TaxiRequester taxiRequester;

    public Document execute(Document document) {

        if (DocumentStatus.TAX_ERROR_ACKED.equals(document.getStatus())) {
            return document;
        } else if (!document.getStatus().canTransitionTo(DocumentStatus.TAX_ERROR_ACKED, document.getCountry())) {
            throw ConflictOperationException.createInvalidAcknowledgeStatus(document.getStatus());
        }
        taxiRequester.ackError(document.getId().toString());
        return documentRepository.save(document.toBuilder().status(DocumentStatus.TAX_ERROR_ACKED).build());
    }
}

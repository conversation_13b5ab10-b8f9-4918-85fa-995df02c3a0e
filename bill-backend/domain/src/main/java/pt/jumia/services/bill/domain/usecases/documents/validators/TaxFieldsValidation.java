package pt.jumia.services.bill.domain.usecases.documents.validators;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.exceptions.ErrorCode;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class TaxFieldsValidation implements Validator {

    @Override
    public List<ValidationError> validate(DocumentAggregate aggregate) {

        List<ValidationError> errors = new ArrayList<>();

        aggregate.getTaxCategoryTotals().forEach(taxCategoryTotal -> {

            if (taxCategoryTotal.getTaxFixedAmount() == null && taxCategoryTotal.getTaxRate() == null) {
                errors.add(new ValidationError(
                        ErrorCode.INVALID_TAX_CATEGORY,
                        "Tax fixed amount or tax rate must be provided "
                ));
            }
        });
        return errors;
    }
}

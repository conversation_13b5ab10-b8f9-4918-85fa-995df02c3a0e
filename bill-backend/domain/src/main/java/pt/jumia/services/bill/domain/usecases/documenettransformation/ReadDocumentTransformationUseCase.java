package pt.jumia.services.bill.domain.usecases.documenettransformation;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.repository.DocumentTransformationRepository;

import java.util.List;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class ReadDocumentTransformationUseCase {

    private final DocumentTransformationRepository documentTransformationRepository;

    public List<DocumentTransformation> readByDocumentId(UUID documentId) {
        return documentTransformationRepository.findByDocumentId(documentId);
    }
}

package pt.jumia.services.bill.domain.exceptions;

import pt.jumia.services.acl.lib.AclErrorException;

import java.text.MessageFormat;

/**
 * Exception thrown when user is not authorized execute tasks
 *
 */
public class UserForbiddenException extends CodedException {

    private static final long serialVersionUID = -8652215746483266031L;

    public static UserForbiddenException create(String msg) {
        return new UserForbiddenException(msg);
    }

    public static UserForbiddenException createFromAclErrorException(AclErrorException aclErrorException) {
        return new UserForbiddenException(aclErrorException.getMessage());
    }

    public static UserForbiddenException createCannotAccess() {
        return new UserForbiddenException("User does not have permission to access Bill");
    }

    public static UserForbiddenException createCannotManageDocuments(String username) {
        return new UserForbiddenException(MessageFormat.format("User {0} cannot manage documents", username));
    }

    public static UserForbiddenException createCannotViewDocuments(String username) {
        return new UserForbiddenException(MessageFormat.format("User {0} cannot view documents", username));
    }

    public static UserForbiddenException createDontHavePermission(String username, String permissionCode) {
        return new UserForbiddenException(String.format("The user '%s' does not have permission '%s' on Bill", username, permissionCode));
    }

    public static UserForbiddenException createCannotAccess(String username) {
        return new UserForbiddenException(String.format("The user '%s' does not have permission to access Bill", username));
    }

    private UserForbiddenException(String message) {
        super(ErrorCode.FORBIDDEN, message);
    }


}

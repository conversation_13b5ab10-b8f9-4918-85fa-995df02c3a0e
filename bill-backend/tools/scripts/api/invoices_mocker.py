#!/usr/bin/env python
import argparse
import json
import logging
import sys
import copy
import decimal
from utils import bill
from datetime import datetime


DECIMAL_FORMATTER = "{:.4f}"


# logging config
logging.basicConfig(
    format="%(asctime)s %(name)s %(levelname)-8s %(message)s",
    level=logging.INFO,
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger("invoices_mocker")

# arguments parser
parser = argparse.ArgumentParser(
    description="Bill - <PERSON>ript to create invoices on Bill through api calls.",
    formatter_class=argparse.ArgumentDefaultsHelpFormatter,
)

parser.add_argument("--country", default="UG", help="Country code in capital case")
parser.add_argument("--shop", default="jumia", help="Shop name")
parser.add_argument(
    "--n-invoices", type=int, default=1, help="Number of invoices to generate"
)
parser.add_argument(
    "--n-lines", type=int, default=1, help="Number of invoice lines do generate"
)
parser.add_argument(
    "--base-sid",
    default="fake-invoices-script",
    help="Base invoice SID. If generating multiple invoices, they will have consecutive suffix ids",
)
parser.add_argument(
    "--receiver-email",
    default="<EMAIL>",
    help="Receiver email address",
)
parser.add_argument(
    "--receiver-mobile-phone",
    default="+256999999999",
    help="Receiver mobile phone number",
)
parser.add_argument(
    "--line-quantity", default="1.0000", help="Quantity on an invoice line"
)
parser.add_argument(
    "--line-unit-price", default="3000.0000", help="Invoice line unit price"
)
parser.add_argument("--line-tax-rate", default="0.1800", help="Invoice line tax rate")


def generate_invoice_lines(
    idx, quantity, unit_price, net_amount_per_unit, tax_amount_per_unit, tax_rate
):
    return {
        "position": idx,
        "quantity": DECIMAL_FORMATTER.format(quantity),
        "unitOfMeasure": "NOT_APPLICABLE",
        "itemCode": "KA141GR1M7GH2NAFAMZ",
        "itemName": "KA141GR1M7GH2NAFAMZ",
        "category": {"taxAuthorityCode": "50161901"},
        "unitPrice": DECIMAL_FORMATTER.format(unit_price),
        "totalAmount": DECIMAL_FORMATTER.format(quantity * unit_price),
        "netAmount": DECIMAL_FORMATTER.format(quantity * net_amount_per_unit),
        "totalTaxAmount": DECIMAL_FORMATTER.format(quantity * tax_amount_per_unit),
        "appliedTaxes": [
            {
                "taxCategory": "VAT_GENERAL",
                "taxRate": DECIMAL_FORMATTER.format(tax_rate),
                "taxAmount": DECIMAL_FORMATTER.format(tax_amount_per_unit),
            }
        ],
    }


def generate_invoice(args, idx):
    line_quantity = decimal.Decimal(args.line_quantity)
    line_tax_rate = decimal.Decimal(args.line_tax_rate)
    line_unit_price = decimal.Decimal(args.line_unit_price)
    line_net_amount_per_unit = line_unit_price / (1 + line_tax_rate)
    line_tax_amount_per_unit = line_unit_price - line_net_amount_per_unit
    base_sid = args.base_sid + datetime.now().strftime("%Y%m%d%H%M%S")
    return {
        "country": args.country,
        "shop": args.shop,
        "type": "SALES_INVOICE",
        "sid": base_sid
        if args.n_invoices == 1
        else base_sid + "-" + str(idx),
        "flow": "RETAIL",
        "generatedBy": "JUMIA\\SWAT.INVOICE.GENERATE.SCRIPT",
        "referenceNumber": "*********",
        "issuedDate": "2021-01-28T00:00:00.000Z",
        "currency": "UGX",
        "receiver": {
            "type": "FOREIGNER",
            "legalName": "WYCLIFFE",
            "name": "WYCLIFFE CHEMONGES",
            "nationalIdentificationNumber": "",
            "taxIdentificationNumber": "",
            "businessRegistrationNumber": "",
            "address": {
                "region": "",
                "city": "",
                "street": "Plot 522 Naalya Estate Kampala,Naalya",
                "buildingNumber": "",
                "floor": "",
                "postalCode": "",
                "additionalInformation": "",
            },
            "email": args.receiver_email,
            "mobilePhone": args.receiver_mobile_phone,
            "linePhone": "+256787515990",
        },
        "issuer": {
            "type": "BUSINESS",
            "legalName": "JADE E SERVICES UGANDA LIMITED",
            "name": "Jumia",
            "taxIdentificationNumber": "1006964111",
            "businessRegistrationNumber": "/174418",
            "address": {
                "country": "UG",
                "region": "",
                "city": "",
                "street": "UG",
                "buildingNumber": "",
                "floor": "",
                "postalCode": "",
                "additionalInformation": "",
            },
            "email": "<EMAIL>",
            "mobilePhone": "2560778497936",
            "linePhone": "2560778497936",
        },
        "notes": "Fake generated document for test purposes!",
        "lines": [
            generate_invoice_lines(
                idx=i,
                quantity=line_quantity,
                unit_price=line_unit_price,
                net_amount_per_unit=line_net_amount_per_unit,
                tax_amount_per_unit=line_tax_amount_per_unit,
                tax_rate=line_tax_rate,
            )
            for i in range(args.n_lines)
        ],
        "lineCount": args.n_lines,
        "totalAmount": DECIMAL_FORMATTER.format(
            args.n_lines * line_quantity * line_unit_price
        ),
        "netAmount": DECIMAL_FORMATTER.format(
            args.n_lines * line_quantity * line_net_amount_per_unit
        ),
        "taxAmount": DECIMAL_FORMATTER.format(
            args.n_lines * line_quantity * line_tax_amount_per_unit
        ),
        "discountAmount": "0.0000",
        "taxCategoryTotals": [
            {
                "taxCategory": "VAT_GENERAL",
                "taxRate": DECIMAL_FORMATTER.format(line_tax_rate),
                "totalAmount": DECIMAL_FORMATTER.format(
                    args.n_lines * line_quantity * line_unit_price
                ),
                "netAmount": DECIMAL_FORMATTER.format(
                    args.n_lines * line_quantity * line_net_amount_per_unit
                ),
                "taxAmount": DECIMAL_FORMATTER.format(
                    args.n_lines * line_quantity * line_tax_amount_per_unit
                ),
            }
        ],
    }


if __name__ == "__main__":
    bill.setup_bill_arguments(parser)
    args = parser.parse_args()
    bill.init(args)

    payload = [generate_invoice(args, i) for i in range(args.n_invoices)]
    if not payload:
        logger.error("Could not generate invoice payload... Skipping...")
        sys.exit(1)

    bill.push_document(payload)

import os
import sys
import logging
import base64
import requests

HTTP_CREATED = 201

logger = logging.getLogger("bill")

_host = None
_headers = None


def setup_bill_arguments(parser):
    parser.add_argument(
        "--bill-host", default="http://localhost:8080", help="Bill BE host"
    )
    parser.add_argument(
        "--bill-token",
        help="Bill token to send in the Authorization header. "
        "If not provided, will try to use the environment variable BILL_TOKEN",
    )
    parser.add_argument(
        "--username",
        help="Username for the basic authentication. "
        "If not provided, will try to use the environment variable BILL_USERNAME",
    )
    parser.add_argument(
        "--password",
        help="Password for the basic authentication. "
        "If not provided, will try to use the environment variable BILL_PASSWORD",
    )


def init(args):
    global _host, _headers

    _host = args.bill_host

    args.bill_token = args.bill_token or os.environ.get("BILL_TOKEN", None)
    if not args.bill_token:
        if not args.username:
            logger.error("Authorization not provided. Exiting...")
            sys.exit(1)

        if not args.password:
            import getpass

            args.password = getpass.getpass("Password: ")

    auth = (
        args.bill_token
        or "Basic %s"
        % base64.encodestring(args.username + ":" + args.password)
        .replace("\n", "")
        .strip()
    )

    _headers = {"Authorization": auth, "Content-Type": "application/json"}


def log_api_error(response, message):
    logger.error(
        u"{message} -- {status} {content}".format(
            message=message, status=response.status_code, content=response.content
        )
    )


def push_document(document_payloads):
    host = u"""{host}/api/documents""".format(host=_host)

    for i in range(len(document_payloads)):
        response = requests.post(url=host, headers=_headers, json=document_payloads[i])
        if response.status_code != HTTP_CREATED:
            log_api_error(
                response,
                u"""Error pushing invoice with sid '{sid}'""".format(
                    sid=document_payloads[i]["sid"]
                )
            )

timestamps {
	node('finance-slaves') {
		stage('Checkout') {
			checkout scm
		}

		stage('Generate') {
			dir('bill-backend/tools/scripts/api') {
				withCredentials([usernamePassword(
					credentialsId: 'acl-stg-bill-machine-user',
					usernameVariable: 'BILL_USERNAME',
					passwordVariable: 'BILL_PASSWORD'
				)]) {
					sh "./invoices_mocker.py \
					--country $params.country --shop $params.shop \
					--n-invoices $params.nInvoices \
					--n-lines $params.nLines \
					--base-sid $params.baseSid \
					--receiver-email $params.receiverEmail \
					--receiver-mobile-phone $params.receiverMobilePhone \
					--line-quantity $params.lineQuantity \
					--line-unit-price $params.lineUnitPrice \
					--line-tax-rate $params.lineTaxRate \
					--bill-host 'https://api-bill-dev-services.jumia.com' \
					--username $BILL_USERNAME \
					--password $BILL_PASSWORD"
				}
			}	
		}
	}
}
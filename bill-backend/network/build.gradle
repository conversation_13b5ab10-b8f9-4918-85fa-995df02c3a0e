apply plugin: 'idea'
apply from: '../config/quality/quality.gradle'

dependencies {

    implementation project(':domain')

    // ACL Migrator
    implementation group: 'com.jumia.services', name: 'acl-migrator', version: "${aclMigratorVersion}"

    implementation "org.springframework.boot:spring-boot:${springBootVersion}"

    // spotbugs annotations, so that lombok can add suppress warnings in generated code
    implementation group: 'com.github.spotbugs', name: 'spotbugs-annotations', version: "${spotbugsVersion}"

    //basic utils, mainly for Base64
    implementation group: 'pt.aig.aigx', name: 'aigx-elasticsearch-components', version: '3.3.4-RELEASE'

    implementation "pt.aig.aigx:logging-context:0.3.0-RELEASE"

    //retrofit for network requests
    implementation group: 'com.squareup.retrofit2', name: 'retrofit', version: '2.9.0'
    implementation "com.squareup.okhttp3:logging-interceptor:4.9.1"
    implementation 'com.squareup.retrofit2:converter-jackson:2.9.0'
    testImplementation "com.squareup.retrofit2:retrofit-mock:2.9.0"

    implementation "com.squareup.retrofit2:retrofit-mock:2.2.0"

    // https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient
    implementation group: 'org.apache.httpcomponents', name: 'httpclient', version: '4.5'

    // https://mvnrepository.com/artifact/org.springframework.boot/spring-boot-autoconfigure
    implementation group: 'org.springframework.boot', name: 'spring-boot-autoconfigure', version: "${springBootVersion}"

    //kafka
    compile group: 'org.springframework.kafka', name: 'spring-kafka', version: "${kafkaVersion}"

    testCompile group: 'org.springframework.kafka', name: 'spring-kafka-test', version: "${kafkaVersion}"

    //new relic, for monitoring
    compile group: 'com.newrelic.agent.java', name: 'newrelic-api', version: '3.43.0'

}

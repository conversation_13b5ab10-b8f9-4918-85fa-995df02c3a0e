package pt.jumia.services.bill.network.afroms;

import okhttp3.ResponseBody;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.AfromsProductDetails;
import pt.jumia.services.bill.domain.entities.dtos.AfromsShopConfig;
import pt.jumia.services.bill.domain.exceptions.afroms.AfromsErrorException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.afroms.payloads.ProductRequestPayload;
import pt.jumia.services.bill.network.afroms.payloads.ProductResponsePayload;
import retrofit2.Response;
import retrofit2.mock.Calls;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AfromsNetworkRequesterTest {

    private final NetworkProperties networkProperties = new NetworkProperties();
    private AfromsNetworkRequester afromsNetworkRequester;

    @Mock
    private AfromsClient afromsClient;

    @Mock
    private  ClientFactory clientFactory;

    @BeforeEach
    public void setUp() {
        List<AfromsShopConfig> shops = new ArrayList<>(){{
            add(new AfromsShopConfig("eg", "jumia", "https://console-staging.jumia.com.eg/", "apiKey"));
        }};
        networkProperties.getAfroms().setShops(shops);

        afromsNetworkRequester = new AfromsNetworkRequester(networkProperties, clientFactory);
    }

    @Test
    public void getProductDetails_success() {
        String sku = "test";
        String apiKey = "apiKey";
        ProductRequestPayload requestPayload = ProductRequestPayload.builder().build();
        requestPayload.addMessage(ProductRequestPayload.Message.builder().sku(sku).build());
        Response<ProductResponsePayload[]> afromsResponse = Response.success(new ProductResponsePayload[] {ProductResponsePayload.builder().categorySid("id")
                .categoryString("path")
                .name("name").build()});
        AfromsProductDetails expected = AfromsProductDetails.builder()
                .ucrId("id")
                .ucrPath("path")
                .productName("name").build();

        when(clientFactory.createAfromsClient(any())).thenReturn(afromsClient);
        when(afromsClient.getProductDetails(apiKey, requestPayload)).thenReturn(Calls.response(afromsResponse));

        AfromsProductDetails actual = afromsNetworkRequester.getProductDetails("EG", "jumia", sku);

        verify(afromsClient).getProductDetails(apiKey, requestPayload);
        assertEquals(actual, expected);
    }

    @Test
    public void getProductDetails_unauthorized() {
        String sku = "test";
        String apiKey = "apiKey";
        ProductRequestPayload requestPayload = ProductRequestPayload.builder().build();
        requestPayload.addMessage(ProductRequestPayload.Message.builder().sku(sku).build());

        when(clientFactory.createAfromsClient(any())).thenReturn(afromsClient);
        when(afromsClient.getProductDetails(apiKey, requestPayload))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED, ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> afromsNetworkRequester.getProductDetails("EG", "jumia", sku))
                .isInstanceOf(AfromsErrorException.class);

        verify(afromsClient).getProductDetails(apiKey, requestPayload);
    }

    @Test
    public void getProductDetails_missingConfiguration() {
        String sku = "test";
        ProductRequestPayload requestPayload = ProductRequestPayload.builder().build();
        requestPayload.addMessage(ProductRequestPayload.Message.builder().sku(sku).build());

        assertThatThrownBy(() -> afromsNetworkRequester.getProductDetails("UG", "jumia", sku))
                .isInstanceOf(AfromsErrorException.class);
    }

    @Test
    public void getProductDetails_productNotFound() {
        String sku = "test";
        String apiKey = "apiKey";
        ProductRequestPayload requestPayload = ProductRequestPayload.builder().build();
        requestPayload.addMessage(ProductRequestPayload.Message.builder().sku(sku).build());

        when(clientFactory.createAfromsClient(any())).thenReturn(afromsClient);
        when(afromsClient.getProductDetails(apiKey, requestPayload))
                .thenReturn(Calls.response(new ProductResponsePayload[0]));

        assertThatThrownBy(() -> afromsNetworkRequester.getProductDetails("EG", "jumia", sku))
                .isInstanceOf(AfromsErrorException.class);

        verify(afromsClient).getProductDetails(apiKey, requestPayload);
    }

}

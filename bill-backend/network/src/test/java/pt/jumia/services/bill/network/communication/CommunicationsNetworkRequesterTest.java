package pt.jumia.services.bill.network.communication;

import com.neovisionaries.i18n.CountryCode;
import okhttp3.MediaType;
import okhttp3.ResponseBody;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.BusinessLine;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.DailyIssuedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DailyReceivedDocumentsReport;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.exceptions.communications.CommunicationsNetworkException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.domain.settings.CommunicationsSettings;
import pt.jumia.services.bill.domain.settings.DailyIssuedDocumentsReportSettings;
import pt.jumia.services.bill.domain.settings.DailyReceivedDocumentsReportSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.communications.CommunicationsClient;
import pt.jumia.services.bill.network.communications.CommunicationsNetworkRequester;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsDocumentRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsIssuedDocumentsReportRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsReceivedDocumentsReportRequestPayload;
import retrofit2.Response;
import retrofit2.mock.Calls;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CommunicationsNetworkRequesterTest {

    private static final DailyIssuedDocumentsReport DAILY_ISSUED_REPORT = DailyIssuedDocumentsReport.builder()
            .businessLine(BusinessLine.builder()
                    .countryCode(CountryCode.EG)
                    .shop("jumia-1")
                    .build())
            .detailedReport(DailyIssuedDocumentsReport.RangeReport.builder()
                    .reportRangeStartDate(LocalDateTime.now())
                    .reportRangeEndDate(LocalDateTime.now())
                    .statusesMap(Map.of(
                            DocumentStatus.TAX_SUCCESS, 10L,
                            DocumentStatus.TAX_SUBMITTED_INVALID, 5L
                    ))
                    .build())
            .overviewReport(DailyIssuedDocumentsReport.RangeReport.builder()
                    .reportRangeStartDate(LocalDateTime.now())
                    .reportRangeEndDate(LocalDateTime.now())
                    .statusesMap(Map.of(
                            DocumentStatus.TAX_SUCCESS, 11L,
                            DocumentStatus.TAX_SUBMITTED_INVALID, 15L
                    ))
                    .build())
            .build();

    private static final DailyReceivedDocumentsReport DAILY_RECEIVED_REPORT = DailyReceivedDocumentsReport.builder()
            .businessLine(BusinessLine.builder()
                    .countryCode(CountryCode.EG)
                    .shop("jumia-2")
                    .build())
            .statusesReport(DailyReceivedDocumentsReport.StatusesReport.builder()
                    .reportRangeStartDate(LocalDateTime.now())
                    .reportRangeEndDate(LocalDateTime.now())
                    .range(72)
                    .statusesMap(Map.of(
                            DocumentStatus.TAX_SUCCESS, 10L,
                            DocumentStatus.TAX_CANCELLED, 5L
                    ))
                    .build())
            .reviewReport(DailyReceivedDocumentsReport.ReviewReport.builder()
                    .reportFirstRangeStartDate(LocalDateTime.now())
                    .reportSecondRangeStartDate(LocalDateTime.now())
                    .reportThirdRangeStartDate(LocalDateTime.now())
                    .reportRangeEndDate(LocalDateTime.now())
                    .countOfUnreviewedDocumentsForTheThirdThird(100L)
                    .countOfUnreviewedDocumentsForTheSecondThird(250L)
                    .countOfUnreviewedDocumentsForTheFirstThird(500L)
                    .build())
            .build();

    private static final String COMMUNICATION_REPORT_EVENT_NAME = "billDailyReportTest";
    private static final String COMMUNICATION_REPORT_EMAIL = "<EMAIL>";

    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("even_more_dummy")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .generateId()
                    .category(Category.builder().sid("fake_category").build())
                    .appliedTaxes(List.of(DocumentLine.AppliedTax.builder().build()))
                    .build()))
            .taxCategoryTotals(List.of(TaxCategoryTotal.builder().generateId().build()))
            .build();

    private static final CommunicationsDocumentRequestPayload COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD =
            new CommunicationsDocumentRequestPayload(DOCUMENT_AGGREGATE);

    @Mock
    private CommunicationsClient communicationsClient;

    private CommunicationsNetworkRequester communicationsNetworkRequester;

    private final NetworkProperties networkProperties = new NetworkProperties();

    private String token;

    @Mock
    private OverallSettings overallSettings;

    @Mock
    private CommunicationsSettings communicationsSettings;

    @Mock
    private DailyIssuedDocumentsReportSettings dailyIssuedDocumentsReportSettings;

    @Mock
    private DailyReceivedDocumentsReportSettings dailyReceivedDocumentsReportSettings;

    @BeforeEach
    public void setUp() {
        ClientFactory clientFactory = mock(ClientFactory.class);
        when(clientFactory.createCommunicationsClient(any())).thenReturn(communicationsClient);

        communicationsNetworkRequester = new CommunicationsNetworkRequester(clientFactory, networkProperties, overallSettings);
        token = "Basic " + Base64.encodeBase64String(String.format("%s:%s",
                        networkProperties.getCommunications().getUsername(),
                        networkProperties.getCommunications().getPassword())
                .getBytes(StandardCharsets.UTF_8));
    }

    @Test
    void sendEmailInvoiceDocumentToReceiverTest() {
        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        when(communicationsSettings.getInvoiceEvent(anyString(), anyString())).thenReturn("sales_invoice_event");
        when(communicationsClient.publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "sales_invoice_event", COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD))
                .thenReturn(Calls.response(Response.success(202, null)));

        communicationsNetworkRequester.sendEmailReceivedDocument(DOCUMENT_AGGREGATE);

        verify(communicationsClient).publishEvent(token, "jumia-" +
                DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "sales_invoice_event", COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD);
    }

    @Test
    void sendEmailDocumentAggregateToReceiverTest() {
        String email = "<EMAIL>";
        when(communicationsClient.publishEmail(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD))
                .thenReturn(Calls.response(Response.success(202, null)));

        communicationsNetworkRequester.sendEmailReceivedDocument(DOCUMENT_AGGREGATE, email);

        verify(communicationsClient).publishEmail(token, "jumia-" +
                DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD);
    }

    @Test
    void sendEmailCreditMemoDocumentToReceiverTest() {
        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        Document document = DOCUMENT;
        document.setType(DocumentType.SALES_CREDIT_MEMO);
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE;
        documentAggregate.setDocument(document);
        CommunicationsDocumentRequestPayload communicationsDocumentRequestPayload =
                new CommunicationsDocumentRequestPayload(documentAggregate);
        when(communicationsSettings.getCreditMemoEvent(anyString(), anyString())).thenReturn("credit_memo_event");
        when(communicationsClient.publishEvent(token, document.getShop() + "-" +
                document.getCountry().getAlpha2(), "credit_memo_event", communicationsDocumentRequestPayload))
                .thenReturn(Calls.response(Response.success(202, null)));

        communicationsNetworkRequester.sendEmailReceivedDocument(documentAggregate);

        verify(communicationsClient).publishEvent(token, "jumia-" +
                document.getCountry().getAlpha2(), "credit_memo_event", communicationsDocumentRequestPayload);
    }

    @Test
    void sendEmailDocumentAggregateToReceiverExceptionTest() {
        String email = "<EMAIL>";
        Response<ResponseBody> errorResponse = Response.error(500, ResponseBody.create("some error", MediaType.parse("text/plain")));

        when(communicationsClient.publishEmail(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailReceivedDocument(DOCUMENT_AGGREGATE, email))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEmail(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                        DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(),
                COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD);
    }

    @Test
    void sendEmailDocumentToReceiverExceptionTest() {
        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        when(communicationsSettings.getInvoiceEvent(anyString(), anyString())).thenReturn("event_name");
        Response<ResponseBody> errorResponse = Response.error(500, ResponseBody.create("some error", MediaType.parse("text/plain")));

        when(communicationsClient.publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name", COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailReceivedDocument(DOCUMENT_AGGREGATE))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                        DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name",
                COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD);
    }

    @Test
    public void sendEmailDocumentToReceiverUnauthorizedTest() {
        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        when(communicationsSettings.getInvoiceEvent(anyString(), anyString())).thenReturn("event_name");
        Response<ResponseBody> errorResponse = Response.error(HttpStatus.SC_UNAUTHORIZED,
                ResponseBody.create("unauthorized", null));

        when(communicationsClient.publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name", COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailReceivedDocument(DOCUMENT_AGGREGATE))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                        DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name",
                COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD);
    }

    @Test
    public void sendEmailDocumentToReceiverForbiddenTest() {
        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        when(communicationsSettings.getInvoiceEvent(anyString(), anyString())).thenReturn("event_name");
        when(communicationsClient.publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                        DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name",
                COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_FORBIDDEN, ResponseBody.create("forbidden", null))));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailReceivedDocument(DOCUMENT_AGGREGATE))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                        DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name",
                COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD);
    }

    @Test
    public void sendEmailDocumentToReceiverIOException() {
        when(overallSettings.getCommunicationsSettings()).thenReturn(communicationsSettings);
        when(communicationsSettings.getInvoiceEvent(anyString(), anyString())).thenReturn("event_name");
        when(communicationsClient.publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                        DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name",
                COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD))
                .thenReturn(Calls.failure(new IOException("exception message xpto")));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailReceivedDocument(DOCUMENT_AGGREGATE))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEvent(token, DOCUMENT_AGGREGATE.getDocument().getShop() + "-" +
                        DOCUMENT_AGGREGATE.getDocument().getCountry().getAlpha2(), "event_name",
                COMMUNICATIONS_DOCUMENT_REQUEST_PAYLOAD);
    }

    @Test
    void sendEmailIssuedDocumentsReportSuccessTest() {
        when(overallSettings.getDailyIssuedDocumentsReportSettings()).thenReturn(dailyIssuedDocumentsReportSettings);

        when(dailyIssuedDocumentsReportSettings.getCommunicationEventName(
                DAILY_ISSUED_REPORT.getBusinessLine().getShop(),
                DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        when(communicationsClient.publishEmail(token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.response(Response.success(202, null)));

        communicationsNetworkRequester.sendEmailByIssuedDocumentsReportToReceiver(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }

    @Test
    public void sendEmailIssuedDocumentsReportUnauthorizedTest() {
        when(overallSettings.getDailyIssuedDocumentsReportSettings()).thenReturn(dailyIssuedDocumentsReportSettings);

        when(dailyIssuedDocumentsReportSettings.getCommunicationEventName(
                DAILY_ISSUED_REPORT.getBusinessLine().getShop(),
                DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        Response<ResponseBody> errorResponse = Response.error(HttpStatus.SC_UNAUTHORIZED,
                ResponseBody.create("unauthorized", null));

        when(communicationsClient.publishEmail(token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailByIssuedDocumentsReportToReceiver(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }

    @Test
    public void sendEmailIssuedDocumentsReportForbiddenTest() {

        when(overallSettings.getDailyIssuedDocumentsReportSettings()).thenReturn(dailyIssuedDocumentsReportSettings);

        when(dailyIssuedDocumentsReportSettings.getCommunicationEventName(
                DAILY_ISSUED_REPORT.getBusinessLine().getShop(),
                DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        Response<ResponseBody> errorResponse = Response
                .error(HttpStatus.SC_FORBIDDEN, ResponseBody.create("forbidden", null));

        when(communicationsClient.publishEmail(token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailByIssuedDocumentsReportToReceiver(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }

    @Test
    public void sendEmailIssuedDocumentsReportIOException() {
        when(overallSettings.getDailyIssuedDocumentsReportSettings()).thenReturn(dailyIssuedDocumentsReportSettings);

        when(dailyIssuedDocumentsReportSettings.getCommunicationEventName(
                DAILY_ISSUED_REPORT.getBusinessLine().getShop(),
                DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        when(communicationsClient.publishEmail(token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.failure(new IOException("exception message xpto")));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailByIssuedDocumentsReportToReceiver(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_ISSUED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_ISSUED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(DAILY_ISSUED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }

    @Test
    void sendEmailReceivedDocumentsReportSuccessTest() {
        when(overallSettings.getDailyReceivedDocumentsReportSettings()).thenReturn(dailyReceivedDocumentsReportSettings);

        when(dailyReceivedDocumentsReportSettings.getCommunicationEventName(
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop(),
                DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        when(communicationsClient.publishEmail(token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.response(Response.success(202, null)));

        communicationsNetworkRequester.sendEmailByReceivedDocumentsReportToReceiver(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }

    @Test
    public void sendEmailReceivedDocumentsReportUnauthorizedTest() {
        when(overallSettings.getDailyReceivedDocumentsReportSettings()).thenReturn(dailyReceivedDocumentsReportSettings);

        when(dailyReceivedDocumentsReportSettings.getCommunicationEventName(
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop(),
                DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        Response<ResponseBody> errorResponse = Response.error(HttpStatus.SC_UNAUTHORIZED,
                ResponseBody.create("unauthorized", null));

        when(communicationsClient.publishEmail(token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailByReceivedDocumentsReportToReceiver(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }

    @Test
    public void sendEmailReceivedDocumentsReportForbiddenTest() {

        when(overallSettings.getDailyReceivedDocumentsReportSettings()).thenReturn(dailyReceivedDocumentsReportSettings);

        when(dailyReceivedDocumentsReportSettings.getCommunicationEventName(
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop(),
                DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        Response<ResponseBody> errorResponse = Response
                .error(HttpStatus.SC_FORBIDDEN, ResponseBody.create("forbidden", null));

        when(communicationsClient.publishEmail(token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailByReceivedDocumentsReportToReceiver(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }

    @Test
    public void sendEmailReceivedDocumentsReportIOException() {
        when(overallSettings.getDailyReceivedDocumentsReportSettings()).thenReturn(dailyReceivedDocumentsReportSettings);

        when(dailyReceivedDocumentsReportSettings.getCommunicationEventName(
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop(),
                DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2()))
                .thenReturn(COMMUNICATION_REPORT_EVENT_NAME);

        when(communicationsClient.publishEmail(token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)))
                .thenReturn(Calls.failure(new IOException("exception message xpto")));

        assertThatThrownBy(() -> communicationsNetworkRequester.sendEmailByReceivedDocumentsReportToReceiver(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL))
                .isInstanceOf(CommunicationsNetworkException.class);

        verify(communicationsClient).publishEmail(
                token,
                DAILY_RECEIVED_REPORT.getBusinessLine().getShop() + "-" +
                        DAILY_RECEIVED_REPORT.getBusinessLine().getCountryCode().getAlpha2(),
                COMMUNICATION_REPORT_EVENT_NAME,
                CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(DAILY_RECEIVED_REPORT, COMMUNICATION_REPORT_EMAIL)
        );
    }
}

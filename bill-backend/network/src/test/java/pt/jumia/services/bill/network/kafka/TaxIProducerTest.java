package pt.jumia.services.bill.network.kafka;

import com.neovisionaries.i18n.CountryCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.util.concurrent.ListenableFuture;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentId;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.network.kafka.producers.TaxIProducer;
import pt.jumia.services.bill.network.taxi.payloads.StatementRequestPayload;

import java.math.BigDecimal;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TaxIProducerTest {

    private static final String[] TOPICS = {"jumiaEG", "jumiaUG"};

    private static final Document DOCUMENT = Document.builder()
            .id(UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb"))
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.EG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("dummy sid")
            .currency(Currency.getInstance("EGP"))
            .issuer(Issuer.builder().generateId().address(Address.builder()
                            .country(CountryCode.EG)
                            .build())
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .receiver(Receiver.builder().generateId()
                    .address(Address.builder()
                            .country(CountryCode.EG)
                            .build())
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("old-tax-document-number-un-updated")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .document(DOCUMENT)
            .lines(List.of(DocumentLine.builder()
                    .appliedTaxes(List.of(
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.VAT_GENERAL)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build(),
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.OTHER_AMOUNT)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build()
                    ))
                    .category(Category.builder()
                            .sid("sid-1")
                            .build())
                    .build(), DocumentLine.builder()
                    .appliedTaxes(List.of(
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.EXEMPT)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build(),
                            DocumentLine.AppliedTax.builder()
                                    .taxCategory(TaxCategory.ZERO_RATE)
                                    .taxRate(new BigDecimal("0.1"))
                                    .taxAmount(new BigDecimal("10.0"))
                                    .build()
                    ))
                    .category(Category.builder()
                            .sid("sid-2")
                            .build())
                    .build()))
            .build();
    @Mock
    private KafkaTemplate<String, StatementRequestPayload> kafkaTemplateDocumentRequest;
    @Mock
    private KafkaTemplate<String, BillPayload> kafkaTemplateBillPayload;
    @Mock
    private ListenableFuture<SendResult<String, StatementRequestPayload>> listenableDocumentRequestFuture;
    @Mock
    private ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    @InjectMocks
    private TaxIProducer taxIProducer;

    @BeforeEach
    public void setUp() {
        taxIProducer = new TaxIProducer(kafkaTemplateDocumentRequest, kafkaTemplateBillPayload, readDocumentAggregateUseCase);
        ReflectionTestUtils.setField(taxIProducer, "topicsName", TOPICS);
    }

    @Test
    public void publishMessage_invoice() {
        when(kafkaTemplateDocumentRequest.send(anyString(), any())).thenReturn(listenableDocumentRequestFuture);

        taxIProducer.pushDocument(DOCUMENT_AGGREGATE);

        verify(kafkaTemplateDocumentRequest).send(anyString(), any());
    }

    @Test
    public void publishMessage_creditNote() {
        DocumentAggregate documentAggregate = DOCUMENT_AGGREGATE.toBuilder().document(
                DOCUMENT.toBuilder()
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .originalDocument(DocumentId.builder().sid("sid").build())
                        .build()).build();
        when(kafkaTemplateDocumentRequest.send(anyString(), any())).thenReturn(listenableDocumentRequestFuture);

        taxIProducer.pushDocument(documentAggregate);

        verify(kafkaTemplateDocumentRequest).send(anyString(), any());
    }
}

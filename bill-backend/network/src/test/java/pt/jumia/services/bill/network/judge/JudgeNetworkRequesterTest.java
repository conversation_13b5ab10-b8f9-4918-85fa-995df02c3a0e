package pt.jumia.services.bill.network.judge;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.neovisionaries.i18n.CountryCode;
import okhttp3.MediaType;
import okhttp3.ResponseBody;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.exceptions.judge.JudgeNetworkException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.domain.settings.JudgeSettings;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.judge.response.JudgeDocumentResponse;
import retrofit2.Response;
import retrofit2.mock.Calls;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Currency;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class JudgeNetworkRequesterTest {

    @Mock
    private JudgeClient judgeClient;

    private JudgeNetworkRequester judgeNetworkRequester;

    private final NetworkProperties networkProperties = new NetworkProperties();

    @Mock
    private OverallSettings overallSettings;

    @Mock
    private JudgeSettings judgeSettings;

    private String token;
    private static final String SID = "SID-123";
    private final String INVOICE = "invoice";
    private final String CREDIT_NOTE = "creditNote";
    private final String CREDIT_MEMOS = "creditMemos";
    private static final String SHOP = "jumia";

    @BeforeEach
    public void setUp() {
        ClientFactory clientFactory = mock(ClientFactory.class);
        when(clientFactory.createJudgeClient(any())).thenReturn(judgeClient);

        token = "Basic " + Base64.encodeBase64String(String.format("%s:%s",
                networkProperties.getJudge().getUsername(),
                networkProperties.getJudge().getPassword())
                .getBytes(StandardCharsets.UTF_8));

        judgeNetworkRequester = new JudgeNetworkRequester(clientFactory, networkProperties, overallSettings);
    }

    @Test
    public void downloadDocumentBySid_success() {
        String testStr = "test";
        Response<ResponseBody> judgeResponse = Response.success(ResponseBody.create(testStr.getBytes(), MediaType.get("application/pdf")));
        when(judgeClient.downloadDocumentRawBySid(token, SID)).thenReturn(Calls.response(judgeResponse));

        DocumentFile attachmentDocumentFile = judgeNetworkRequester.downloadDocumentBySid(SID);

        verify(judgeClient).downloadDocumentRawBySid(token, SID);
        DocumentFile expectedDocumentFile = DocumentFile.builder()
                .content(testStr.getBytes())
                .mediaType("application/pdf")
                .build();
        assertEquals(expectedDocumentFile, attachmentDocumentFile);
    }

    @Test
    public void downloadDocumentBySid_exceptionConvertingToAttachmentFile() {
        Response<ResponseBody> judgeResponse = Response.success(null);
        when(judgeClient.downloadDocumentRawBySid(token, SID)).thenReturn(Calls.response(judgeResponse));

        assertThatThrownBy(() -> judgeNetworkRequester.downloadDocumentBySid(SID))
                .isInstanceOf(JudgeNetworkException.class);

        verify(judgeClient).downloadDocumentRawBySid(token, SID);
    }

    @Test
    public void downloadDocumentBySid_unauthorized() {
        when(judgeClient.downloadDocumentRawBySid(token, SID))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED, ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> judgeNetworkRequester.downloadDocumentBySid(SID))
                .isInstanceOf(JudgeNetworkException.class);

        verify(judgeClient).downloadDocumentRawBySid(token, SID);
    }

    @Test
    public void downloadDocumentBySid_notFound() {
        when(judgeClient.downloadDocumentRawBySid(token, SID))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_NOT_FOUND, ResponseBody.create("not_found", null))));

        assertThatThrownBy(() -> judgeNetworkRequester.downloadDocumentBySid(SID))
                .isInstanceOf(JudgeNetworkException.class);

        verify(judgeClient).downloadDocumentRawBySid(token, SID);
    }

    @Test
    public void downloadDocumentBySid_forbidden() {
        when(judgeClient.downloadDocumentRawBySid(token, SID))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_FORBIDDEN, ResponseBody.create("forbidden", null))));

        assertThatThrownBy(() -> judgeNetworkRequester.downloadDocumentBySid(SID))
                .isInstanceOf(JudgeNetworkException.class);

        verify(judgeClient).downloadDocumentRawBySid(token, SID);
    }

    @Test
    public void downloadDocumentBySid_IOException() {
        when(judgeClient.downloadDocumentRawBySid(token, SID))
                .thenReturn(Calls.failure(new IOException("exception message xpto")));

        assertThatThrownBy(() -> judgeNetworkRequester.downloadDocumentBySid(SID))
                .isInstanceOf(JudgeNetworkException.class)
                .hasMessage("exception message xpto");

        verify(judgeClient).downloadDocumentRawBySid(token, SID);
    }

    @Test
    public void generateDocument_invalidInvoicesDocumentTemplateCode() throws JsonProcessingException {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .shop("missing-shop")
                        .country(CountryCode.UG)
                        .type(DocumentType.SALES_INVOICE)
                        .build())
                .build();

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.getInvoiceCodes(anyString(), anyString())).thenReturn("");

        String pdfSid = judgeNetworkRequester.generateNewDocumentByCode(documentAggregate);

        assertNull(pdfSid);
        verifyNoInteractions(judgeClient);
    }

    @Test
    public void generateDocument_invalidCreditNoteDocumentTemplateCode() throws JsonProcessingException {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .shop("missing-shop")
                        .country(CountryCode.UG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .build())
                .build();

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.getCreditNoteCodes(anyString(), anyString())).thenReturn("");

        String pdfSid = judgeNetworkRequester.generateNewDocumentByCode(documentAggregate);

        assertNull(pdfSid);
        verifyNoInteractions(judgeClient);
    }

    @Test
    public void generateCreditNoteDocument_success() throws JsonProcessingException {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .shop(SHOP)
                        .flow(DocumentFlow.RETAIL)
                        .country(CountryCode.UG)
                        .type(DocumentType.SALES_CREDIT_NOTE)
                        .currency(Currency.getInstance("UGX"))
                        .receiver(Receiver.builder().build())
                        .issuer(Issuer.builder().build())
                        .build())
                .lines(List.of())
                .taxCategoryTotals(List.of())
                .build();

        JudgeDocumentResponse response = JudgeDocumentResponse.builder()
                .sid("judge-document-sid")
                .build();
        doReturn(Calls.response(response)).when(judgeClient)
                .generateNewDocumentByCode(anyString(), eq(CREDIT_NOTE), any());

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.getCreditNoteCodes(anyString(), anyString())).thenReturn(CREDIT_NOTE);

        String pdfSid = judgeNetworkRequester.generateNewDocumentByCode(documentAggregate);

        verify(judgeClient).generateNewDocumentByCode(anyString(), eq(CREDIT_NOTE), any());
        assertEquals(response.getSid(), pdfSid);
    }

    @Test
    public void generateInvoiceDocument_success() throws JsonProcessingException {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .shop(SHOP)
                        .flow(DocumentFlow.RETAIL)
                        .country(CountryCode.UG)
                        .type(DocumentType.SALES_INVOICE)
                        .currency(Currency.getInstance("UGX"))
                        .receiver(Receiver.builder().build())
                        .issuer(Issuer.builder().build())
                        .build())
                .lines(List.of())
                .taxCategoryTotals(List.of())
                .build();
        JudgeDocumentResponse response = JudgeDocumentResponse.builder()
                .sid("judge-document-sid")
                .build();
        doReturn(Calls.response(response)).when(judgeClient)
                .generateNewDocumentByCode(anyString(), eq(INVOICE), any());

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.getInvoiceCodes(anyString(), anyString())).thenReturn(INVOICE);

        String pdfSid = judgeNetworkRequester.generateNewDocumentByCode(documentAggregate);

        verify(judgeClient).generateNewDocumentByCode(anyString(), eq(INVOICE), any());
        assertEquals(response.getSid(), pdfSid);
    }

    @Test
    public void generateDocument_invalidCreditMemosDocumentTemplateCode() throws JsonProcessingException {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .shop("missing-shop")
                        .country(CountryCode.UG)
                        .type(DocumentType.SALES_CREDIT_MEMO)
                        .build())
                .build();

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.getCreditMemos(anyString(), anyString())).thenReturn("");

        String pdfSid = judgeNetworkRequester.generateNewDocumentByCode(documentAggregate);

        assertNull(pdfSid);
        verifyNoInteractions(judgeClient);
    }

    @Test
    public void generateCreditMemosDocument_success() throws JsonProcessingException {
        DocumentAggregate documentAggregate = DocumentAggregate.builder()
                .document(Document.builder()
                        .shop(SHOP)
                        .country(CountryCode.UG)
                        .flow(DocumentFlow.RETAIL)
                        .type(DocumentType.SALES_CREDIT_MEMO)
                        .currency(Currency.getInstance("UGX"))
                        .receiver(Receiver.builder().build())
                        .issuer(Issuer.builder().build())
                        .build())
                .lines(List.of())
                .taxCategoryTotals(List.of())
                .build();

        JudgeDocumentResponse response = JudgeDocumentResponse.builder()
                .sid("judge-document-sid")
                .build();
        doReturn(Calls.response(response)).when(judgeClient)
                .generateNewDocumentByCode(anyString(), eq(CREDIT_MEMOS), any());

        when(overallSettings.getJudgeSettings()).thenReturn(judgeSettings);
        when(judgeSettings.getCreditMemos(anyString(), anyString())).thenReturn(CREDIT_MEMOS);

        String pdfSid = judgeNetworkRequester.generateNewDocumentByCode(documentAggregate);

        verify(judgeClient).generateNewDocumentByCode(anyString(), eq(CREDIT_MEMOS), any());
        assertEquals(response.getSid(), pdfSid);
    }
}

package pt.jumia.services.bill.network.testutil;

import okhttp3.MediaType;
import okhttp3.ResponseBody;
import pt.jumia.services.bill.domain.utils.ResourceLoader;
import retrofit2.Response;

import java.io.IOException;

/**
 * Util test class with some server responses
 */
@SuppressWarnings("PMD.CloseResource")
public class MockResponse {

    public static final String ERROR_HTML = "error.html";

    private MockResponse() {
        //no instance
    }

    public static <T> Response<T> serverDown() throws IOException {
        String json = ResourceLoader.getStringFromFile(ERROR_HTML);
        ResponseBody body = ResponseBody.create(json, MediaType.parse("application/json"));
        return Response.error(503, body);
    }

    public static <T> Response<T> serverError() throws IOException {
        String json = ResourceLoader.getStringFromFile(ERROR_HTML);
        ResponseBody body = ResponseBody.create(json, MediaType.parse("application/json"));
        return Response.error(500, body);
    }

    public static <T> Response<T> badRequest() throws IOException {
        String json = ResourceLoader.getStringFromFile(ERROR_HTML);
        ResponseBody body = ResponseBody.create(json, MediaType.parse("application/json"));
        return Response.error(400, body);
    }

    public static <T> Response<T> unauthorized() throws IOException {
        String json = ResourceLoader.getStringFromFile(ERROR_HTML);
        ResponseBody body = ResponseBody.create(json, MediaType.parse("application/json"));
        return Response.error(401, body);
    }

}

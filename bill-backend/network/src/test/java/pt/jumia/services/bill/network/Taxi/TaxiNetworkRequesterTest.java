package pt.jumia.services.bill.network.Taxi;

import com.neovisionaries.i18n.CountryCode;
import okhttp3.MediaType;
import okhttp3.ResponseBody;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import pt.jumia.services.bill.domain.entities.CancelRequest;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.filters.DocumentRetryFilters;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.taxi.TaxiClient;
import pt.jumia.services.bill.network.taxi.TaxiNetworkRequester;
import pt.jumia.services.bill.network.taxi.payloads.CancelRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.DocumentRetryFiltersRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.ReceivedDocumentActionRequestPayload;
import retrofit2.Response;
import retrofit2.mock.Calls;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TaxiNetworkRequesterTest {

    private static final String DOCUMENT_ID = "70cd0084-6908-414f-953d-ef2423258ad1";
    private static final CancelRequest CANCEL_REQUEST = CancelRequest.builder()
            .documentId(UUID.fromString(DOCUMENT_ID))
            .cancellationReason("Cancellation Reason")
            .build();
    private static final Document DOCUMENT = Document.builder()
            .status(DocumentStatus.TAX_SUCCESS)
            .judgeSid("dummy_judge_sid")
            .country(CountryCode.NG)
            .shop("jumia")
            .type(DocumentType.SALES_INVOICE)
            .sid("abdsabdasbsasfasf")
            .currency(Currency.getInstance("NGN"))
            .issuer(Issuer.builder().generateId().build())
            .receiver(Receiver.builder().generateId()
                    .email("<EMAIL>")
                    .mobilePhone("+01011122222")
                    .build())
            .generateId()
            .build();

    private static final TaxAuthoritiesDetails TAX_AUTHORITIES_DETAILS = TaxAuthoritiesDetails.builder()
            .generateId()
            .document(DOCUMENT)
            .deviceNumber("*********")
            .verificationCode("dummy")
            .qrCode("very_dummy")
            .taxDocumentNumber("even_more_dummy")
            .submissionId("cant_be_more_dummy")
            .build();

    private static final DocumentAggregate DOCUMENT_AGGREGATE = DocumentAggregate.builder()
            .document(DOCUMENT)
            .taxAuthoritiesDetails(TAX_AUTHORITIES_DETAILS)
            .build();

    @Mock
    private TaxiClient taxiClient;

    @Mock
    private ReadDocumentAggregateUseCase readDocumentAggregateUseCase;

    private TaxiNetworkRequester taxiNetworkRequester;

    private final NetworkProperties networkProperties = new NetworkProperties();

    private String token;

    @BeforeEach
    public void setUp() {
        ClientFactory clientFactory = mock(ClientFactory.class);
        when(clientFactory.createTaxiClient(any())).thenReturn(taxiClient);

        taxiNetworkRequester = new TaxiNetworkRequester(readDocumentAggregateUseCase, clientFactory, networkProperties);
        token = "Basic " + Base64.encodeBase64String(String.format("%s:%s",
                        networkProperties.getTaxi().getUsername(),
                        networkProperties.getTaxi().getPassword())
                .getBytes(StandardCharsets.UTF_8));
    }

    @Test
    void ackErrorTest() {
        when(taxiClient.ackError(token, DOCUMENT_ID)).thenReturn(Calls.response(Response.success(null)));

        taxiNetworkRequester.ackError(DOCUMENT_ID);

        verify(taxiClient).ackError(token, DOCUMENT_ID);
    }

    @Test
    void ackErrorExceptionTest() {
        Response<Void> errorResponse = Response.error(500, ResponseBody.create(
                "some error", MediaType.parse("text/plain")));

        when(taxiClient.ackError(token, DOCUMENT_ID)).thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() ->
                taxiNetworkRequester.ackError(DOCUMENT_ID))
                .isInstanceOf(TaxiNetworkException.class);

        verify(taxiClient).ackError(token, DOCUMENT_ID);
    }

    @Test
    void ackErrorUnauthorizedTest() {
        Response<Void> errorResponse = Response.error(HttpStatus.SC_UNAUTHORIZED,
                ResponseBody.create("unauthorized", null));

        when(taxiClient.ackError(token, DOCUMENT_ID)).thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() ->
                taxiNetworkRequester.ackError(DOCUMENT_ID))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessage(String.format("Failed to acknowledge error in TaxI for entity with ID: %s ", DOCUMENT_ID));

        verify(taxiClient).ackError(token, DOCUMENT_ID);
    }

    @Test
    void ackErrorForbiddenTest() {
        Response<Void> errorResponse = Response.error(HttpStatus.SC_FORBIDDEN,
                ResponseBody.create("forbidden", null));

        when(taxiClient.ackError(token, DOCUMENT_ID)).thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() ->
                taxiNetworkRequester.ackError(DOCUMENT_ID))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessage(String.format("Failed to acknowledge error in TaxI for entity with ID: %s ", DOCUMENT_ID));

        verify(taxiClient).ackError(token, DOCUMENT_ID);
    }

    @Test
    public void downloadTaxAuthoritiesPdf_unauthorized() {
        when(taxiClient.getDocumentPdf(token, DOCUMENT_ID))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED,
                        ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> taxiNetworkRequester.getTaxAuthoritiesPdf(DOCUMENT_ID))
                .isInstanceOf(TaxiNetworkException.class);

        verify(taxiClient).getDocumentPdf(token, DOCUMENT_ID);
    }

    @Test
    public void downloadTaxAuthoritiesPdf_IOException() {
        when(taxiClient.getDocumentPdf(token, DOCUMENT_ID))
                .thenReturn(Calls.failure(new IOException("exception message xpto")));

        assertThatThrownBy(() -> taxiNetworkRequester.getTaxAuthoritiesPdf(DOCUMENT_ID))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessageContaining("Failed to get Tax Authorities Pdf for document with ID:")
                .hasMessageContaining(DOCUMENT_ID);

        verify(taxiClient).getDocumentPdf(token, DOCUMENT_ID);
    }

    @Test
    public void downloadDocumentBySid_success() {
        String testStr = "test";
        Response<ResponseBody> judgeResponse = Response.success(ResponseBody.create(testStr.getBytes(), MediaType.get("application/pdf")));
        when(taxiClient.getDocumentPdf(token, DOCUMENT_ID)).thenReturn(Calls.response(judgeResponse));

        DocumentFile attachmentDocumentFile = taxiNetworkRequester.getTaxAuthoritiesPdf(DOCUMENT_ID);

        verify(taxiClient).getDocumentPdf(token, DOCUMENT_ID);
        DocumentFile expectedDocumentFile = DocumentFile.builder()
                .content(testStr.getBytes())
                .mediaType("application/pdf")
                .build();
        assertEquals(expectedDocumentFile, attachmentDocumentFile);
    }

    @Test
    public void cancelDocumentById_success() {
        when(taxiClient.cancelDocument(token, DOCUMENT_ID, new CancelRequestPayload(CANCEL_REQUEST)))
                .thenReturn(Calls.response((Void) null));

        taxiNetworkRequester.cancelDocument(CANCEL_REQUEST);

        verify(taxiClient).cancelDocument(token, DOCUMENT_ID, new CancelRequestPayload(CANCEL_REQUEST));
    }

    @Test
    public void cancelDocumentById_ResponseNotOk() {
        Response<Void> errorResponse = Response.error(500, ResponseBody.create(
                "some error", MediaType.parse("text/plain")));

        when(taxiClient.cancelDocument(token, DOCUMENT_ID, new CancelRequestPayload(CANCEL_REQUEST)))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> taxiNetworkRequester.cancelDocument(CANCEL_REQUEST))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessageContaining(String
                        .format("Failed to cancel document, error in Taxi for entity with ID: %s ", DOCUMENT_ID));

        verify(taxiClient).cancelDocument(token, DOCUMENT_ID, new CancelRequestPayload(CANCEL_REQUEST));
    }

    @Test
    public void declineRejectionOfDocumentById_success() {
        when(taxiClient.declineDocumentRejection(token, DOCUMENT_ID)).thenReturn(Calls.response((Void) null));

        taxiNetworkRequester.declineDocumentRejection(DOCUMENT_ID);

        verify(taxiClient).declineDocumentRejection(token, DOCUMENT_ID);
    }

    @Test
    public void declineRejectionOfDocumentById_ResponseNotOk() {
        Response<Void> errorResponse = Response.error(500, ResponseBody.create(
                "some error", MediaType.parse("text/plain")));

        when(taxiClient.declineDocumentRejection(token, DOCUMENT_ID))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> taxiNetworkRequester.declineDocumentRejection(DOCUMENT_ID))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessageContaining(String
                        .format("Failed to decline document rejection, error in Taxi for entity with ID: %s ", DOCUMENT_ID));

        verify(taxiClient).declineDocumentRejection(token, DOCUMENT_ID);
    }

    @Test
    public void retryDocument_success() {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");
        DocumentRetryFilters documentRetryFilters = DocumentRetryFilters.builder()
                .relatedEntityId(documentId)
                .country(CountryCode.EG)
                .build();

        when(taxiClient.bulkRetry(token, new DocumentRetryFiltersRequestPayload(documentRetryFilters)))
                .thenReturn(Calls.response((Void) null));

        taxiNetworkRequester.bulkRetryDocuments(documentRetryFilters);

        verify(taxiClient).bulkRetry(token, new DocumentRetryFiltersRequestPayload(documentRetryFilters));
    }

    @Test
    public void retryDocument_unauthorized() {
        UUID documentId = UUID.fromString("4c4528a3-4b6f-4c9c-b49d-f8f3aed963cb");

        DocumentRetryFilters documentRetryFilters = DocumentRetryFilters.builder()
                .relatedEntityId(documentId)
                .country(CountryCode.EG)
                .build();

        when(taxiClient.bulkRetry(token, new DocumentRetryFiltersRequestPayload(documentRetryFilters)))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED,
                        ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> taxiNetworkRequester.bulkRetryDocuments(documentRetryFilters))
                .isInstanceOf(TaxiNetworkException.class);

        verify(taxiClient).bulkRetry(token, new DocumentRetryFiltersRequestPayload(documentRetryFilters));
    }

    @Test
    public void rejectReceivedDocument_success() {

        when(taxiClient.rejectReceivedDocument(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT,
                        "rejection-reason"
                )))
                .thenReturn(Calls.response((Void) null));

        taxiNetworkRequester.rejectReceivedDocument(DOCUMENT_AGGREGATE, "rejection-reason");

        verify(taxiClient).rejectReceivedDocument(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT,
                        "rejection-reason"
                ));
    }

    @Test
    public void rejectReceivedDocument_ResponseNotOk() {
        Response<Void> errorResponse = Response.error(500, ResponseBody.create(
                "some error", MediaType.parse("text/plain")));

        when(taxiClient.rejectReceivedDocument(token, DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT,
                        "rejection-reason"
                )))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> taxiNetworkRequester.rejectReceivedDocument(DOCUMENT_AGGREGATE, "rejection-reason"))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessageContaining(String
                        .format("Failed to reject received document,error in Taxi for document ID: %s ",
                                DOCUMENT.getId().toString()));

        verify(taxiClient).rejectReceivedDocument(token, DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT,
                        "rejection-reason"
                ));
    }

    @Test
    public void rejectReceivedDocument_unauthorized() {
        when(taxiClient.rejectReceivedDocument(token, DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument(),
                        "rejection-reason"
                )))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED,
                        ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> taxiNetworkRequester.rejectReceivedDocument(DOCUMENT_AGGREGATE, "rejection-reason"))
                .isInstanceOf(TaxiNetworkException.class);

        verify(taxiClient).rejectReceivedDocument(token, DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument(),
                        "rejection-reason"
                ));
    }


    @Test
    public void declineReceivedDocumentCancellation_success() {

        when(taxiClient.declineReceivedDocumentCancellation(token, DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                )))
                .thenReturn(Calls.response((Void) null));

        taxiNetworkRequester.declineReceivedDocumentCancellation(DOCUMENT_AGGREGATE);

        verify(taxiClient).declineReceivedDocumentCancellation(token, DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                ));
    }

    @Test
    public void declineReceivedDocumentCancellation_ResponseNotOk() {
        Response<Void> errorResponse = Response.error(500, ResponseBody.create(
                "some error", MediaType.parse("text/plain")));

        when(taxiClient.declineReceivedDocumentCancellation(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                )))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> taxiNetworkRequester.declineReceivedDocumentCancellation(DOCUMENT_AGGREGATE))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessageContaining(String
                        .format("Failed to decline received document cancellation,error in Taxi for document ID: %s ",
                                DOCUMENT_AGGREGATE.getDocument().getId()));

        verify(taxiClient).declineReceivedDocumentCancellation(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                ));
    }

    @Test
    public void declineReceivedDocumentCancellation_unauthorized() {
        when(taxiClient.declineReceivedDocumentCancellation(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                )))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED,
                        ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> taxiNetworkRequester.declineReceivedDocumentCancellation(DOCUMENT_AGGREGATE))
                .isInstanceOf(TaxiNetworkException.class);

        verify(taxiClient).declineReceivedDocumentCancellation(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                ));
    }

    @Test
    public void approveReceivedDocument_unauthorized() {
        when(taxiClient.approveReceivedDocument(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                )))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED,
                        ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> taxiNetworkRequester.approveReceivedDocument(DOCUMENT_AGGREGATE))
                .isInstanceOf(TaxiNetworkException.class);

        verify(taxiClient).approveReceivedDocument(token,
                DOCUMENT_AGGREGATE.getDocument().getId().toString(),
                new ReceivedDocumentActionRequestPayload(
                        DOCUMENT_AGGREGATE.getDocument()
                ));
    }

    @Test
    public void getReceivedDocumentsDetails_success() {
        List<String> ids = new ArrayList<>() {{
            add("first_document_id");
        }};

        when(taxiClient.getReceivedDocumentsDetails(token, ids))
                .thenReturn(Calls.response((Void) null));

        taxiNetworkRequester.getReceivedDocumentsDetails(ids);

        verify(taxiClient).getReceivedDocumentsDetails(token, ids);
    }

    @Test
    public void getReceivedDocumentsDetails_ResponseNotOk() {
        List<String> ids = new ArrayList<>() {{
            add("first_document_id");
        }};
        Response<Void> errorResponse = Response.error(500, ResponseBody.create(
                "some error", MediaType.parse("text/plain")));

        when(taxiClient.getReceivedDocumentsDetails(token, ids))
                .thenReturn(Calls.response(errorResponse));

        assertThatThrownBy(() -> taxiNetworkRequester.getReceivedDocumentsDetails(ids))
                .isInstanceOf(TaxiNetworkException.class)
                .hasMessageContaining("Failed to get received documents details, with message from Taxi " +
                        "500 - some error");

        verify(taxiClient).getReceivedDocumentsDetails(token, ids);
    }

    @Test
    public void getReceivedDocumentsDetails_unauthorized() {
        List<String> ids = new ArrayList<>() {{
            add("first_document_id");
        }};

        when(taxiClient.getReceivedDocumentsDetails(token, ids))
                .thenReturn(Calls.response(Response.error(HttpStatus.SC_UNAUTHORIZED,
                        ResponseBody.create("unauthorized", null))));

        assertThatThrownBy(() -> taxiNetworkRequester.getReceivedDocumentsDetails(ids))
                .isInstanceOf(TaxiNetworkException.class);

        verify(taxiClient).getReceivedDocumentsDetails(token, ids);
    }
}

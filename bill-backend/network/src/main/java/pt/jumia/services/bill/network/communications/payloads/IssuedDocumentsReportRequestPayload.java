package pt.jumia.services.bill.network.communications.payloads;

import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.DailyIssuedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DocumentStatus;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Builder(toBuilder = true)
public class IssuedDocumentsReportRequestPayload {
    private String countryCode;
    private String shop;
    private String reportGenerationDate;
    private RangePayloadReport detailedReport;
    private RangePayloadReport overviewReport;

    @Data
    @Builder(toBuilder = true)
    public static class RangePayloadReport {
        private String reportRangeStartDate;
        private String reportRangeEndDate;
        private Map<String, Long> statusesMap;
        private int range;
    }

    public static IssuedDocumentsReportRequestPayload generateReportPayload(DailyIssuedDocumentsReport report) {
        return IssuedDocumentsReportRequestPayload.builder()
                .reportGenerationDate(report.getDetailedReport().getReportRangeEndDate()
                        .atOffset(ZoneOffset.UTC)
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd hh:mm a")))
                .countryCode(report.getBusinessLine().getCountryCode().getAlpha2())
                .shop(report.getBusinessLine().getShop())
                .detailedReport(
                        RangePayloadReport.builder()
                                .reportRangeStartDate(
                                        convertDateTime(
                                                report.getDetailedReport().getReportRangeStartDate()
                                        )
                                )
                                .reportRangeEndDate(
                                        convertDateTime(
                                                report.getDetailedReport().getReportRangeEndDate()))
                                .statusesMap(
                                        convertStatusesMap(report.getDetailedReport().getStatusesMap())
                                )
                                .range(report.getDetailedReport().getRange())
                                .build()
                )
                .overviewReport(
                        RangePayloadReport.builder()
                                .reportRangeStartDate(
                                        convertDateTime(
                                                report.getOverviewReport().getReportRangeStartDate())
                                )
                                .reportRangeEndDate(
                                        convertDateTime(
                                                report.getOverviewReport().getReportRangeEndDate())
                                )
                                .statusesMap(
                                        convertStatusesMap(report.getOverviewReport().getStatusesMap())
                                )
                                .range(report.getOverviewReport().getRange())
                                .build()
                )
                .build();
    }

    private static Map<String, Long> convertStatusesMap(Map<DocumentStatus, Long> statusesMap) {
        return statusesMap.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().toString(),
                        entry -> entry.getValue()
                ));
    }

    private static String convertDateTime(LocalDateTime localDateTime) {
        return localDateTime
                .atOffset(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_ZONED_DATE_TIME);
    }
}

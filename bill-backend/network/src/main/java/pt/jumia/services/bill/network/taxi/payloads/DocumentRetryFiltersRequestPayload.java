package pt.jumia.services.bill.network.taxi.payloads;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import pt.jumia.services.bill.domain.entities.filters.DocumentRetryFilters;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DocumentRetryFiltersRequestPayload {

    private ApiLogFiltersRequestPayload apiLogFiltersRequestPayload;
    private TransformationsToApplyRequestPayload transformationsToApplyRequestPayload = new TransformationsToApplyRequestPayload();

    public DocumentRetryFiltersRequestPayload(DocumentRetryFilters documentRetryFilters) {
       apiLogFiltersRequestPayload = new ApiLogFiltersRequestPayload(documentRetryFilters);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class TransformationsToApplyRequestPayload {
        private LocalDateTime issuedDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public class ApiLogFiltersRequestPayload {
        private List<Long> ids;
        private Long rootApiLogId;
        private String relatedEntityId;
        private String relatedEntityCode;
        private String type;
        private String shop;
        private String countryCode;
        private String status;
        private List<String> statuses;
        private Integer statusCode;
        private String errorCode;
        private Integer nDays;
        private LocalDateTime createdAtFrom;
        private LocalDateTime createdAtTo;
        private LocalDateTime issuedDateFrom;
        private LocalDateTime issuedDateTo;
        private String creatorType;

        public ApiLogFiltersRequestPayload(DocumentRetryFilters documentRetryFilters) {
            this.relatedEntityId = documentRetryFilters.getRelatedEntityId().toString();
            this.relatedEntityCode = documentRetryFilters.getRelatedEntityCode();
            this.type = documentRetryFilters.getType() != null ? documentRetryFilters.getType().toString() : null;
            this.shop = documentRetryFilters.getShop();
            this.countryCode = documentRetryFilters.getCountry() != null ? documentRetryFilters.getCountry().toString() : null;
            this.statuses = documentRetryFilters.getStatuses() != null ?
                    documentRetryFilters.getStatuses().stream().map(Enum::toString).collect(Collectors.toList()) : null;
            this.statusCode = documentRetryFilters.getStatusCode();
            this.errorCode = documentRetryFilters.getErrorCode();
            this.nDays = documentRetryFilters.getNDays();
            this.createdAtFrom = documentRetryFilters.getCreatedAtFrom();
            this.createdAtTo = documentRetryFilters.getCreatedAtTo();
            this.issuedDateFrom = documentRetryFilters.getIssuedDateFrom();
            this.issuedDateTo = documentRetryFilters.getIssuedDateTo();
        }
    }

}

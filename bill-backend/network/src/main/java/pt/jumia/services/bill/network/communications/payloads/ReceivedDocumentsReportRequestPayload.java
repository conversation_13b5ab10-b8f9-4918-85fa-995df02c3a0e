package pt.jumia.services.bill.network.communications.payloads;

import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.DailyReceivedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DocumentStatus;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Builder(toBuilder = true)
public class ReceivedDocumentsReportRequestPayload {
    private String countryCode;
    private String shop;
    private String reportGenerationDate;
    private RangeStatusesReport rangeStatusesReport;
    private UnReviewedDocumentsReport unReviewedDocumentsReport;

    @Data
    @Builder(toBuilder = true)
    public static class RangeStatusesReport {
        private String reportRangeStartDate;
        private String reportRangeEndDate;
        private Map<String, Long> statusesMap;
        private int range;
    }

    @Data
    @Builder(toBuilder = true)
    public static class UnReviewedDocumentsReport {
        private String reportFirstRangeStartDate;
        private String reportSecondRangeStartDate;
        private String reportThirdRangeStartDate;
        private String reportRangeEndDate;
        private Long countOfUnreviewedDocumentsForTheFirstThird;
        private Long countOfUnreviewedDocumentsForTheSecondThird;
        private Long countOfUnreviewedDocumentsForTheThirdThird;
    }


    public static ReceivedDocumentsReportRequestPayload generateReportPayload(DailyReceivedDocumentsReport report) {
        return ReceivedDocumentsReportRequestPayload.builder()
                .countryCode(report.getBusinessLine().getCountryCode().getAlpha2())
                .shop(report.getBusinessLine().getShop())
                .reportGenerationDate(convertDateTime(
                        report.getStatusesReport().getReportRangeEndDate()))
                .rangeStatusesReport(
                        RangeStatusesReport.builder()
                                .reportRangeStartDate(
                                        convertDateTime(
                                                report.getStatusesReport().getReportRangeStartDate())
                                )
                                .reportRangeEndDate(
                                        convertDateTime(
                                                report.getStatusesReport().getReportRangeEndDate())
                                )
                                .statusesMap(
                                        convertStatusesMap(report.getStatusesReport().getStatusesMap())
                                )
                                .range(report.getStatusesReport().getRange())
                                .build()
                )
                .unReviewedDocumentsReport(UnReviewedDocumentsReport.builder()
                        .reportFirstRangeStartDate(
                                convertDateTime(
                                        report.getReviewReport().getReportFirstRangeStartDate())
                        )
                        .reportSecondRangeStartDate(
                                convertDateTime(
                                        report.getReviewReport().getReportSecondRangeStartDate())
                        )
                        .reportThirdRangeStartDate(
                                convertDateTime(
                                        report.getReviewReport().getReportThirdRangeStartDate())
                        )
                        .reportRangeEndDate(
                                convertDateTime(
                                        report.getReviewReport().getReportRangeEndDate())
                        )
                        .countOfUnreviewedDocumentsForTheFirstThird(report
                                .getReviewReport()
                                .getCountOfUnreviewedDocumentsForTheFirstThird())
                        .countOfUnreviewedDocumentsForTheSecondThird(report
                                .getReviewReport()
                                .getCountOfUnreviewedDocumentsForTheSecondThird())
                        .countOfUnreviewedDocumentsForTheThirdThird(report
                                .getReviewReport()
                                .getCountOfUnreviewedDocumentsForTheThirdThird())
                        .build())
                .build();
    }

    private static Map<String, Long> convertStatusesMap(Map<DocumentStatus, Long> statusesMap) {
        return statusesMap.entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().toString(),
                        entry -> entry.getValue()
                ));
    }

    private static String convertDateTime(LocalDateTime localDateTime) {
        return localDateTime
                .atOffset(ZoneOffset.UTC)
                .format(DateTimeFormatter.ISO_ZONED_DATE_TIME);
    }

}

package pt.jumia.services.bill.network.judge;


import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.JudgeRequester;
import pt.jumia.services.bill.domain.entities.*;
import pt.jumia.services.bill.domain.exceptions.judge.JudgeNetworkException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.domain.utils.JsonUtils;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.judge.payloads.JudgeDocumentRequestPayload;
import pt.jumia.services.bill.network.judge.payloads.JudgeReceivedDocumentContextPayload;
import pt.jumia.services.bill.network.judge.response.JudgeDocumentResponse;
import retrofit2.Response;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Locale;

@Slf4j
@Component
public class JudgeNetworkRequester implements JudgeRequester {
    private final JudgeClient judgeClient;
    private final String token;
    private final String username;
    private final OverallSettings overallSettings;

    @Autowired
    public JudgeNetworkRequester(ClientFactory clientFactory, NetworkProperties networkProperties, OverallSettings overallSettings) {
        this.judgeClient = clientFactory.createJudgeClient(networkProperties.getJudge().getUrl());

        this.username = networkProperties.getJudge().getUsername();
        this.token = "Basic " + Base64.encodeBase64String(String.format("%s:%s",
                        this.username,
                        networkProperties.getJudge().getPassword())
                .getBytes(StandardCharsets.UTF_8));
        this.overallSettings = overallSettings;
    }

    @Override
    public DocumentFile downloadDocumentBySid(String sid) throws JudgeNetworkException {

        Response<ResponseBody> response;
        try {
            log.info("Downloading JuDGe document with SID '{}'", sid);
            response = judgeClient.downloadDocumentRawBySid(token, sid).execute();
        } catch (IOException e) {
            throw JudgeNetworkException.buildFailedRequest(e.getMessage(), e);
        }

        if (!response.isSuccessful()) {
            throw responseNotSuccess(response);
        }

        try {
            return DocumentFile.builder()
                    .content(response.body().byteStream().readAllBytes())
                    .mediaType(response.body().contentType().toString())
                    .build();
        } catch (Exception e) {
            throw JudgeNetworkException.buildFailedRequest(e.getMessage(), e);
        }
    }

    @Override
    public String generateNewDocumentByCode(DocumentAggregate documentAggregate) throws JudgeNetworkException{
        log.info("Sending JuDGe print request document ");
        return processJudgeDocumentResponse(documentAggregate, null);
    }

    @Override
    public String generateNewDocumentByCodeWithAllDocFields(DocumentAggregate documentAggregate, GhDocPdfRequestPayloadInfo docPdfFields)
            throws JudgeNetworkException{
        return processJudgeDocumentResponse(documentAggregate, docPdfFields);
    }

    private String processJudgeDocumentResponse(DocumentAggregate documentAggregate, GhDocPdfRequestPayloadInfo docPdfFields)
            throws JudgeNetworkException {
        Response<JudgeDocumentResponse> response;

        JudgeReceivedDocumentContextPayload judgeReceivedDocumentContext =
                new JudgeReceivedDocumentContextPayload();

        String judgeDocumentTemplateCode = getJudgeDocumentTemplateCode(documentAggregate);
        if (StringUtils.isBlank(judgeDocumentTemplateCode)) {
            log.warn("No Judge template configured for {}", judgeDocumentTemplateCode);
            return null;
        }
        log.info("Sending JuDGe print request for document with ID '{}' to judge template '{}'",
                documentAggregate.getDocument().getId(),
                judgeDocumentTemplateCode);

        Object context = docPdfFields != null ? docPdfFields : documentAggregate;

        if (documentAggregate.getDocument().getFlow().equals(DocumentFlow.RECEIVED)){
            context = judgeReceivedDocumentContext.fromDocumentAggregate(documentAggregate);
            log.info("Setting context for received document. Context: '{}'", context);
        }

        try {
            response = judgeClient.generateNewDocumentByCode(token, judgeDocumentTemplateCode,
                            JudgeDocumentRequestPayload.builder()
                                    .application(username)
                                    .context(context)
                                    .country(documentAggregate.getDocument().getCountry().getAlpha2())
                                    .build())
                    .execute();
        } catch (IOException e) {
            log.error("Error printing JuDGe template '{}' for document with ID '{}': {}",
                    judgeDocumentTemplateCode,
                    documentAggregate.getDocument().getId(),
                    ExceptionUtils.getStackTrace(e));
            throw JudgeNetworkException.buildFailedRequest(e.getMessage(), e);
        }

        if (!response.isSuccessful()) {
            throw responseNotSuccess(response);
        }

        return response.body().getSid();
    }

    private String getJudgeDocumentTemplateCode(DocumentAggregate documentAggregate) {
        String code;
        if (DocumentType.SALES_INVOICE.equals(documentAggregate.getDocument().getType())
                && DocumentFlow.RECEIVED.equals(documentAggregate.getDocument().getFlow())){
            code = this.overallSettings.getJudgeSettings().getInvoiceCodes().getDefault();
        } else if (DocumentType.SALES_INVOICE.equals(documentAggregate.getDocument().getType())
                || DocumentType.PURCHASE_INVOICE.equals(documentAggregate.getDocument().getType())) {
            code = this.overallSettings.getJudgeSettings().getInvoiceCodes(
                    documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                    documentAggregate.getDocument().getCountry().getAlpha2());
        } else if (DocumentType.SALES_CREDIT_NOTE.equals(documentAggregate.getDocument().getType())
                || DocumentType.PURCHASE_CREDIT_NOTE.equals(documentAggregate.getDocument().getType())) {
            code = this.overallSettings.getJudgeSettings().getCreditNoteCodes(
                    documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                    documentAggregate.getDocument().getCountry().getAlpha2());
        } else {
            code = this.overallSettings.getJudgeSettings().getCreditMemos(
                    documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                    documentAggregate.getDocument().getCountry().getAlpha2());
        }

        return code;
    }

    private JudgeNetworkException responseNotSuccess(Response<?> response) {
        try {
            String errorMessage = response.errorBody() == null ? null : response.errorBody().string();
            log.error("Error on JuDGe request: {}", errorMessage);
            return JudgeNetworkException.buildResponseNotOk(response.code(), errorMessage);
        } catch (IOException e) {
            return JudgeNetworkException.buildResponseNotOk(response.code(), "Failed reading response body", e);
        }
    }
}

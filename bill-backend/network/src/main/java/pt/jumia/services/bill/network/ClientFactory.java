package pt.jumia.services.bill.network;

import pt.jumia.services.bill.network.afroms.AfromsClient;
import pt.jumia.services.bill.network.communications.CommunicationsClient;
import pt.jumia.services.bill.network.judge.JudgeClient;
import pt.jumia.services.bill.network.taxi.TaxiClient;

public interface ClientFactory {

    JudgeClient createJudgeClient(String endpoint);

    CommunicationsClient createCommunicationsClient(String endpoint);

    TaxiClient createTaxiClient(String endpoint);

    AfromsClient createAfromsClient(String endpoint);
}

package pt.jumia.services.bill.network.kafka.config;

import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;
import pt.jumia.services.bill.network.taxi.payloads.StatementRequestPayload;

/**
 * <AUTHOR>
 * at 05/17/2022
 */
@Configuration
public class KafkaProducerConfiguration {

    @Bean
    public KafkaTemplate<String, BillPayload> billKafkaTestTemplate(KafkaProperties properties) {
        return new KafkaTemplate<>(billTestProducerFactory(properties));
    }
    @Bean
    public ProducerFactory<String, BillPayload> billTestProducerFactory(KafkaProperties properties) {
        return new DefaultKafkaProducerFactory<>(properties.buildProducerProperties());
    }
    @Bean
    public KafkaTemplate<String, StatementRequestPayload> billKafkaTemplate(KafkaProperties properties) {
        return new KafkaTemplate<>(billProducerFactory(properties));
    }

    @Bean
    public ProducerFactory<String, StatementRequestPayload> billProducerFactory(KafkaProperties properties) {
        return new DefaultKafkaProducerFactory<>(properties.buildProducerProperties());
    }
}

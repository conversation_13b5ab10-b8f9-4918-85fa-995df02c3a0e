package pt.jumia.services.bill.network;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.Profiles;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.network.afroms.AfromsClient;
import pt.jumia.services.bill.network.communications.CommunicationsClient;
import pt.jumia.services.bill.network.judge.JudgeClient;
import pt.jumia.services.bill.network.taxi.TaxiClient;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

/**
 * Factory that creates our Retrofit instances, so that we can make HTTP requests
 */
@Component
@RequiredArgsConstructor
@Profile({"!" + Profiles.FAKE_CLIENTS})
public class NetworkClientFactory implements ClientFactory {

    private final NetworkProperties networkProperties;
    private final ObjectMapper objectMapper;

    @Override
    public JudgeClient createJudgeClient(String endpoint) {
        return createRetrofitInstance(endpoint, createOkHttpInstance())
                .create(JudgeClient.class);
    }

    @Override
    public CommunicationsClient createCommunicationsClient(String endpoint) {
        return createRetrofitInstance(endpoint, createOkHttpInstance())
                .create(CommunicationsClient.class);
    }

    @Override
    public TaxiClient createTaxiClient(String endpoint) {
        return createRetrofitInstance(endpoint, createOkHttpInstance())
                .create(TaxiClient.class);
    }

    @Override
    public AfromsClient createAfromsClient(String endpoint) {
        return createRetrofitInstance(endpoint, createOkHttpInstance())
                .create(AfromsClient.class);
    }

    @NotNull
    private OkHttpClient.Builder createOkHttpInstance() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .readTimeout(networkProperties.getReadTimeout(), TimeUnit.SECONDS);

        if (networkProperties.isLogging()) {
            HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor();
            interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

            builder.addInterceptor(interceptor);
        }

        return builder;
    }

    @NotNull
    private Retrofit createRetrofitInstance(String endpoint, OkHttpClient.Builder okHttpBuilder) {
        return new Retrofit.Builder()
            .baseUrl(endpoint)
            .addConverterFactory(JacksonConverterFactory.create(objectMapper))
            .client(okHttpBuilder.build()).build();
    }
}

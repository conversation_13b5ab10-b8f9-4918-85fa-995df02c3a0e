package pt.jumia.services.bill.network.kafka.producers;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.KafkaProducer;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.kafka.BillPayload;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.network.taxi.payloads.StatementRequestPayload;

@Slf4j
@Component
public class TaxIProducer implements KafkaProducer {

    private final KafkaTemplate<String, StatementRequestPayload> kafkaTemplate;
    private final KafkaTemplate<String, BillPayload> kafkaTestTemplate;
    private final ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    @Value(value = "#{'${kafka.topics.bill-documents.name}'.split(' ')}")
    private String[] topicsName;

    @Autowired
    public TaxIProducer(
            KafkaTemplate<String, StatementRequestPayload> kafkaTemplate,
            KafkaTemplate<String, BillPayload> kafkaTestTemplate,
            ReadDocumentAggregateUseCase readDocumentAggregateUseCase) {
        this.kafkaTemplate = kafkaTemplate;
        this.kafkaTestTemplate = kafkaTestTemplate;
        this.readDocumentAggregateUseCase = readDocumentAggregateUseCase;
    }

    @Override
    public void sendMessage(BillPayload payload) {
        log.debug(String.format("Test producing Bill message --> %s", payload));
        this.kafkaTestTemplate.send(topicsName[0], payload);
    }

    @Override
    public void pushDocument(DocumentAggregate documentAggregate) {
        String topic = "";
        DocumentAggregate originalDocument = null;
        for (String localTopic : this.topicsName) {
            if (localTopic.toLowerCase().contains(
                    documentAggregate.getDocument().getCountry().getAlpha2().toLowerCase()
            )) {
                topic = localTopic;
            }
        }
        log.info("Producing Bill message on topic [{}]. Document sid: [{}]", topic, documentAggregate.getDocument().getSid());
        log.debug(String.format("Producing Bill message --> %s", documentAggregate));
        if (documentAggregate.getDocument().getType().equals(DocumentType.SALES_CREDIT_NOTE)
                || documentAggregate.getDocument().getType().equals(DocumentType.PURCHASE_CREDIT_NOTE)) {
            originalDocument = readDocumentAggregateUseCase
                    .executeBySidAndSuccessStatus(documentAggregate.getDocument()
                            .getOriginalDocument().getSid());
        }
        this.kafkaTemplate.send(topic, new StatementRequestPayload(documentAggregate, originalDocument));
    }
}

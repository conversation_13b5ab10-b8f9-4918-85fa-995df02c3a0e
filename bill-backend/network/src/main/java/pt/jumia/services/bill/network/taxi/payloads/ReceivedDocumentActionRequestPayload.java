package pt.jumia.services.bill.network.taxi.payloads;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder(toBuilder = true)
public class ReceivedDocumentActionRequestPayload {
    private String country;
    private String shop;
    private String entityTaxAuthoritiesId;
    private String changeReason;

    public ReceivedDocumentActionRequestPayload(Document receivedDocument) {
        this.country = receivedDocument.getCountry().getAlpha2();
        this.shop = receivedDocument.getShop();
    }

    public ReceivedDocumentActionRequestPayload(Document receivedDocument, String changeReason) {
        this.country = receivedDocument.getCountry().getAlpha2();
        this.shop = receivedDocument.getShop();
        this.changeReason = changeReason;
    }
}

package pt.jumia.services.bill.network.acl;

import org.slf4j.LoggerFactory;
import pt.jumia.services.acl.lib.logging.Logger;

/**
 * Implementation of the Acl lib logger to enabled the ability for us to log the lib logs in our own logs
 */
public class AclLogger implements Logger {

    private static final org.slf4j.Logger LOGGER = LoggerFactory.getLogger(AclLogger.class);

    @Override
    public void debug(String debugMessage) {

    }

    @Override
    public void info(String infoMessage) {

    }

    @Override
    public void warning(String warningMessage) {
        LOGGER.warn(warningMessage);
    }

    @Override
    public void error(String errorMessage) {
        LOGGER.error(errorMessage);
    }
}

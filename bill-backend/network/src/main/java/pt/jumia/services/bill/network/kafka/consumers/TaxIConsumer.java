package pt.jumia.services.bill.network.kafka.consumers;

import com.newrelic.api.agent.Trace;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.bill.domain.RequestContext;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;
import pt.jumia.services.bill.domain.entities.dtos.TaxAuthoritiesDetailsUpdateDto;
import pt.jumia.services.bill.domain.usecases.documenettransformation.UpsertDocumentTransformationUseCase;
import pt.jumia.services.bill.domain.usecases.documents.UpdateTaxAuthoritiesDetailsUseCase;
import pt.jumia.services.bill.network.response.TaxAuthoritiesDetailsResponsePayload;

import java.util.List;
import java.util.UUID;

@Slf4j
@RequiredArgsConstructor
@Component
public class TaxIConsumer {

    public static final String BILL_ACL_USERNAME = "taxi";

    private final UpdateTaxAuthoritiesDetailsUseCase updateTaxAuthoritiesDetailsUseCase;
    private final UpsertDocumentTransformationUseCase upsertDocumentTransformationUseCase;

    @Trace(dispatcher = true)
    @KafkaListener(
            topics = "#{'${kafka.topics.taxi-documents-status-updates.name}'.split(' ')}",
            autoStartup = "${kafka.topics.taxi-documents-status-updates.auto-start}",
            groupId = "${kafka.topics.taxi-documents-status-updates.group-id}",
            containerFactory = "billListenerContainerFactory",
            concurrency = "${kafka.topics.taxi-documents-status-updates.partitions}"
    )
    public void consumeTaxAuthoritiesDetails(
            @Payload TaxAuthoritiesDetailsResponsePayload taxAuthoritiesDetailsResponsePayload
    ) {
        log.debug(String.format("Consuming TaxI message --> %s", taxAuthoritiesDetailsResponsePayload));
        RequestContext.setUser(RequestUser.builder().username(BILL_ACL_USERNAME).build());
        try {
            TaxAuthoritiesDetailsUpdateDto taxAuthoritiesDetailsUpdateDto = taxAuthoritiesDetailsResponsePayload.toEntity();
            upsertDocumnetTranformationInTaxAuthoritiesPayload(taxAuthoritiesDetailsUpdateDto.getDocumentId(),
                    taxAuthoritiesDetailsUpdateDto.getTaxAuthoritiesDetails().getDocumentTransformations());
            updateTaxAuthoritiesDetailsUseCase.execute(taxAuthoritiesDetailsUpdateDto);
        } catch (Exception e) {
            log.error(String.format("Error while consuming TaxI message " +
                    " with  Error response: %s", ExceptionUtils.getStackTrace(e)));
        }
    }
    private void upsertDocumnetTranformationInTaxAuthoritiesPayload(UUID documentId,
                                                                    List<DocumentTransformation> documneDocumentTransformationList) {
        if (documneDocumentTransformationList != null && !(documneDocumentTransformationList.isEmpty())) {
            upsertDocumentTransformationUseCase.execute(documentId, documneDocumentTransformationList);
        }
    }
}

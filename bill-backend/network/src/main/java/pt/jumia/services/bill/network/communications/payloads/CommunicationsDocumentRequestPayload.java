package pt.jumia.services.bill.network.communications.payloads;

import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentType;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@NoArgsConstructor
public class CommunicationsDocumentRequestPayload {
    private String id;
    private String email;
    private String phoneNumber;
    private List<AttachmentDocumentRequestPayload> attachments;
    private DocumentRequestPayload context;
    private List<Entity> entities;

    public CommunicationsDocumentRequestPayload(DocumentAggregate documentAggregate) {
        Document document = documentAggregate.getDocument();
        this.id = document.getId().toString();
        this.email = document.getReceiver().getEmail();
        this.phoneNumber = document.getReceiver().getMobilePhone();
        this.attachments = List.of(new AttachmentDocumentRequestPayload(AttachmentDocumentNameRequestPayload.builder()
                .sid(document.getSid())
                .judgeSid(document.getJudgeSid())
                .type(document.getType())
                .taxDocumentNumber(documentAggregate.getTaxAuthoritiesDetails().getTaxDocumentNumber())
                .build()));
        this.context = new DocumentRequestPayload(documentAggregate);

        // setup entities list
        this.entities = new ArrayList<>();
        this.entities.add(Entity.builder()
                .sid(document.getId().toString())
                .systemIdentifier(document.getSid())
                .type(document.getType())
                .build());

        if (StringUtils.isNotBlank(documentAggregate.getTaxAuthoritiesDetails().getTaxDocumentNumber())) {
            this.entities.add(Entity.builder()
                    .systemIdentifier(documentAggregate.getTaxAuthoritiesDetails().getTaxDocumentNumber())
                    .sid(StringUtils.isEmpty(documentAggregate.getTaxAuthoritiesDetails().getSubmissionId())
                            ? documentAggregate.getTaxAuthoritiesDetails().getTaxDocumentNumber()
                            : documentAggregate.getTaxAuthoritiesDetails().getSubmissionId())
                    .type(document.getType())
                    .build());
        }
    }

    @Data
    @Builder(toBuilder = true)
    public static class Entity {
        private String sid;
        private String systemIdentifier;
        private DocumentType type;
    }
}

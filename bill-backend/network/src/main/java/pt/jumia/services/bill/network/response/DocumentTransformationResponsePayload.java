package pt.jumia.services.bill.network.response;


import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;

@Data
@NoArgsConstructor
public class DocumentTransformationResponsePayload {

    private String oldValue;

    private String newValue;

    private String type;

    private String originalValue;

    public DocumentTransformationResponsePayload(DocumentTransformation documentTransformation) {
        this.type = documentTransformation.getType() != null ? documentTransformation.getType().name() : null;
        this.newValue = documentTransformation.getNewValue();
        this.originalValue = documentTransformation.getOriginalValue();
    }

    public DocumentTransformation toEntity(Document document) {
        return DocumentTransformation.builder()
                .document(document)
                .type(type == null ? null : DocumentTransformation.EntityType.valueOf(type))
                .originalValue(originalValue)
                .newValue(newValue)
                .build();
    }
    public DocumentTransformation toEntity() {
        return DocumentTransformation.builder()
                .type(type == null ? null : DocumentTransformation.EntityType.valueOf(type))
                .originalValue(originalValue)
                .newValue(newValue)
                .build();
    }
}

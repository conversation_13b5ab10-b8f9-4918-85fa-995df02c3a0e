package pt.jumia.services.bill.network.taxi.payloads;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.CancelRequest;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class CancelRequestPayload {
    private String cancellationReason;

    public CancelRequestPayload(CancelRequest cancelRequest) {
        this.cancellationReason = cancelRequest.getCancellationReason();
    }
}

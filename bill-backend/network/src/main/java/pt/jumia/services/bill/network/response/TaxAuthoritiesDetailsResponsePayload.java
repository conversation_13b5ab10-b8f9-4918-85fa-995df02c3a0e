package pt.jumia.services.bill.network.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.GhDocPdfRequestPayloadInfo;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;
import pt.jumia.services.bill.domain.entities.dtos.OverrideFields;
import pt.jumia.services.bill.domain.entities.dtos.TaxAuthoritiesDetailsUpdateDto;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class TaxAuthoritiesDetailsResponsePayload {

    private UUID documentId;
    private String submissionId;
    private String taxDocumentNumber;
    private String qrCode;
    private String verificationCode;
    private String internalData;
    private String deviceNumber;
    private String exception;
    private String statusCode;
    private String errorCode;
    private String apiLogStatus;
    private OverrideFieldsResponsePayload overrideFields;
    private GhDocPdfRequestPayloadInfo docPdfFields;
    private List<DocumentTransformationResponsePayload> documentTransformations;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class OverrideFieldsResponsePayload {
        private String buyerLegalName;
        private String invoiceNumberInsideTaxAuthorities;
        private Map<String, String> taxCategoriesMap;

        public OverrideFields toEntity() {
            return OverrideFields
                    .builder()
                    .invoiceNumberInsideTaxAuthorities(invoiceNumberInsideTaxAuthorities)
                    .buyerLegalName(buyerLegalName)
                    .taxCategoriesMap(taxCategoriesMap)
                    .build();
        }
    }

    public enum ApiLogStatus {
        ACCEPTED(DocumentStatus.TAX_PENDING),
        PENDING(DocumentStatus.TAX_PENDING),
        SKIPPED(DocumentStatus.TAX_SKIPPED),
        SUCCESS(DocumentStatus.TAX_SUCCESS),
        FAILED_MAPPING(DocumentStatus.TAXI_FAILED_MAPPING),
        FAILED_REQUEST(DocumentStatus.TAX_FAILED_REQUEST),
        FAILED_RESPONSE(DocumentStatus.TAX_FAILED_RESPONSE),
        SUBMITTED_INVALID(DocumentStatus.TAX_SUBMITTED_INVALID),
        REJECTED(DocumentStatus.TAX_REJECTED),
        CANCELLED(DocumentStatus.TAX_CANCELLED),
        RETRIED(DocumentStatus.TAX_ERROR_RETRIED),
        ERROR_ACKED(DocumentStatus.TAX_ERROR_ACKED);

        private final DocumentStatus documentStatus;

        ApiLogStatus(DocumentStatus documentStatus) {
            this.documentStatus = documentStatus;
        }

        public DocumentStatus getDocumentStatus() {
            return this.documentStatus;
        }
    }

    public TaxAuthoritiesDetailsUpdateDto toEntity() {
        return TaxAuthoritiesDetailsUpdateDto.builder()
                .documentId(documentId)
                .documentStatus(ApiLogStatus.valueOf(apiLogStatus).getDocumentStatus())
                .taxAuthoritiesDetails(TaxAuthoritiesDetails
                        .builder()
                        .submissionId(submissionId)
                        .taxDocumentNumber(taxDocumentNumber)
                        .qrCode(qrCode)
                        .verificationCode(verificationCode)
                        .internalData(internalData)
                        .deviceNumber(deviceNumber)
                        .statusCode(statusCode)
                        .errorCode(errorCode)
                        .exception(exception)
                        .generateId()
                        .documentTransformations(CollectionUtils.isEmpty(documentTransformations) ? null :
                                documentTransformations.stream()
                                .map(DocumentTransformationResponsePayload::toEntity).collect(Collectors.toList()))
                        .docPdfFields(docPdfFields == null ? null : docPdfFields.toEntity())
                        .build())
                .overrideFields(overrideFields == null ? null : overrideFields.toEntity())
                .build();
    }
}

package pt.jumia.services.bill.network.afroms;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.AfromsRequester;
import pt.jumia.services.bill.domain.entities.AfromsProductDetails;
import pt.jumia.services.bill.domain.entities.dtos.AfromsShopConfig;
import pt.jumia.services.bill.domain.exceptions.afroms.AfromsErrorException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.afroms.payloads.ProductRequestPayload;
import pt.jumia.services.bill.network.afroms.payloads.ProductResponsePayload;
import retrofit2.Response;

import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@Slf4j
@Component
public class AfromsNetworkRequester implements AfromsRequester {

    private final ClientFactory clientFactory;
    private final NetworkProperties.Afroms afroms;
    private ConcurrentMap<String, AfromsClient> connections = new ConcurrentHashMap<>();

    @Autowired
    public AfromsNetworkRequester(NetworkProperties networkProperties,
                                  ClientFactory clientFactory
    ) {
        this.afroms = networkProperties.getAfroms();
        this.clientFactory = clientFactory;
    }

    private AfromsClient getConnection(AfromsShopConfig afromsShopConfig) {
        return this.connections.computeIfAbsent(afromsShopConfig.getUrl(),
                connection -> this.createConnection(afromsShopConfig)
        );
    }

    private AfromsClient createConnection(AfromsShopConfig afromsShopConfig) {
        return this.clientFactory.createAfromsClient(afromsShopConfig.getUrl());

    }

    @Override
    public AfromsProductDetails getProductDetails(String country, String shop, String sku) {
        AfromsShopConfig config = this.getShopConfig(country, shop);
        AfromsClient client = this.getConnection(config);
        Response<ProductResponsePayload[]> response;

        try {
            ProductRequestPayload productRequestPayload = ProductRequestPayload.builder().build();
            productRequestPayload.addMessage(ProductRequestPayload.Message.builder().sku(sku).build());

            response = client.getProductDetails(config.getApikey(), productRequestPayload).execute();
        } catch (IOException e) {
            throw AfromsErrorException.buildFailedRequest(e.getMessage(), e);
        }

        if (!response.isSuccessful()) {
            throw responseNotSuccess(response);
        }

        if (response.body() != null && response.body().length > 0) {
            return  AfromsProductDetails.builder()
                .ucrId(response.body()[0].getCategorySid())
                .ucrPath(response.body()[0].getCategoryString())
                .productName(response.body()[0].getName())
                .build();
        } else {
            throw AfromsErrorException.productNotFound(String
                    .format("Product UCR not found for country [%s], shop [%s], with sku [%s]",
                            country, shop, sku));
        }
    }

    private AfromsShopConfig getShopConfig(String country, String shop) throws AfromsErrorException {
        Optional<AfromsShopConfig> optionalConfig = this.afroms
                .getAfromsShopConfig(country, shop);

        return optionalConfig.orElseThrow(() ->
                AfromsErrorException.missingConfiguration(
                        String.format("Afroms connection configuration not found for country [%s], shop [%s]",
                        country, shop)));
    }

    private AfromsErrorException responseNotSuccess(Response<?> response) {
        try {
            return AfromsErrorException.buildResponseNotOk(response.code(),
                    response.errorBody() == null ? null : response.errorBody().string());
        } catch (IOException e) {
            return AfromsErrorException.buildResponseNotOk(response.code(),
                    "Failed reading response body", e);
        }
    }

}

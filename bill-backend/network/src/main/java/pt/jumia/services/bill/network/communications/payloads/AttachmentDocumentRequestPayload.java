package pt.jumia.services.bill.network.communications.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class AttachmentDocumentRequestPayload {

    private String judgeDocumentSid;
    private String name;

    public AttachmentDocumentRequestPayload(AttachmentDocumentNameRequestPayload attachmentDocumentNameRequestPayload) {
        this.judgeDocumentSid = attachmentDocumentNameRequestPayload.getJudgeSid();
        this.name = String.format("%s_%s.pdf",
                attachmentDocumentNameRequestPayload.getType(),
                attachmentDocumentNameRequestPayload.getTaxDocumentNumber() != null
                        ? attachmentDocumentNameRequestPayload.getTaxDocumentNumber()
                        : attachmentDocumentNameRequestPayload.getSid()
        );
    }
}

package pt.jumia.services.bill.network.judge.payloads;

import com.neovisionaries.i18n.CountryCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Address;

@Data
@NoArgsConstructor
public class AddressRequestPayload {

    private CountryCode country;
    private String region;
    private String city;
    private String street;
    private String buildingNumber;
    private String floor;
    private String postalCode;
    private String additionalInformation;

    public AddressRequestPayload(Address address) {
        this.country = address.getCountry();
        this.region = address.getRegion();
        this.city = address.getCity();
        this.street = address.getStreet();
        this.buildingNumber = address.getBuildingNumber();
        this.floor = address.getFloor();
        this.postalCode = address.getPostalCode();
        this.additionalInformation = address.getAdditionalInformation();
    }

    public Address toEntity() {
        return Address.builder()
                .country(country)
                .region(region)
                .city(city)
                .street(street)
                .buildingNumber(buildingNumber)
                .floor(floor)
                .postalCode(postalCode)
                .additionalInformation(additionalInformation)
                .build();
    }
}

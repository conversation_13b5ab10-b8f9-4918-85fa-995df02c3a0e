package pt.jumia.services.bill.network.judge;

import okhttp3.ResponseBody;
import pt.jumia.services.bill.network.judge.response.JudgeDocumentResponse;
import pt.jumia.services.bill.network.judge.payloads.JudgeDocumentRequestPayload;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Path;

public interface JudgeClient {

    @GET("/api/documents/{sid}/raw")
    Call<ResponseBody> downloadDocumentRawBySid(
            @Header("Authorization") String authorization,
            @Path("sid") String sid);

    @POST("/api/print/{code}")
    Call<JudgeDocumentResponse> generateNewDocumentByCode(
            @Header("Authorization") String authorization,
            @Path("code") String code,
            @Body JudgeDocumentRequestPayload judgeDocumentRequestPayload);


}

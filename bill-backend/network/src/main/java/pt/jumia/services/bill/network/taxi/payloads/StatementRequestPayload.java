package pt.jumia.services.bill.network.taxi.payloads;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;
import pt.jumia.services.bill.network.communications.payloads.IssuerRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.ReceiverRequestPayload;
import pt.jumia.services.bill.network.request.DocumentRequestPayload;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class StatementRequestPayload {

    private UUID id;
    private DocumentStatus status;
    private String country;
    private String shop;
    private DocumentType type;
    private String sid;
    private DocumentFlow flow;
    private String generatedBy;
    private OriginalDocument originalDocument;
    private IssuedReason issuedReason;
    private String referenceNumber;
    private String purchaseReferenceNumber;
    private LocalDateTime issuedDate;
    private String currency;
    private BigDecimal issuedToLocalCurrencyExchangeRate;
    private ReceiverRequestPayload receiver;
    private IssuerRequestPayload issuer;
    private String notes;
    private List<DocumentRequestPayload.DocumentLineRequestPayload> lines;
    private Integer lineCount;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalItemsDiscountAmount;
    private BigDecimal extraDiscountAmount;
    private List<DocumentRequestPayload.TaxCategoryTotalRequestPayload> taxCategoryTotals;
    private DocumentRequestPayload.PaymentDetailsRequestPayload payment;
    private DocumentRequestPayload.DeliveryDetailsRequestPayload delivery;
    private String transactionType;
    private String transactionProgress;

    public StatementRequestPayload(DocumentAggregate documentAggregate, DocumentAggregate originalDocument) {
        Document document = documentAggregate.getDocument();

        this.id = document.getId();
        this.status = document.getStatus();
        this.country = document.getCountry().getAlpha2();
        this.shop = document.getShop();
        this.type = document.getType();
        this.sid = document.getSid();
        this.flow = document.getFlow();
        this.generatedBy = document.getGeneratedBy();
        this.originalDocument = originalDocument == null ? null : OriginalDocument.builder()
                .submissionId(originalDocument.getTaxAuthoritiesDetails().getSubmissionId())
                .taxDocumentNumber(originalDocument.getTaxAuthoritiesDetails().getTaxDocumentNumber())
                .build();
        this.issuedReason = document.getIssuedReason() == null ? null : IssuedReason.builder()
                .code(document.getIssuedReason().getCode())
                .notes(document.getIssuedReason().getNotes())
                .build();
        this.referenceNumber = document.getReferenceNumber();
        this.purchaseReferenceNumber = document.getPurchaseReferenceNumber();
        this.issuedDate = document.getIssuedDate();
        this.currency = document.getCurrency().getCurrencyCode();
        this.issuedToLocalCurrencyExchangeRate = document.getIssuedToLocalCurrencyExchangeRate();
        this.receiver = new ReceiverRequestPayload(document.getReceiver());
        this.issuer = new IssuerRequestPayload(document.getIssuer());
        this.notes = document.getNotes();
        this.lines = Objects.isNull(documentAggregate.getLines()) ? null :
                documentAggregate.getLines().stream().map(DocumentRequestPayload.DocumentLineRequestPayload::new)
                        .collect(Collectors.toList());
        this.lineCount = document.getLineCount();
        this.totalAmount = document.getTotalAmount();
        this.netAmount = document.getNetAmount();
        this.discountAmount = document.getDiscountAmount();
        this.totalItemsDiscountAmount = document.getTotalItemsDiscountAmount();
        this.extraDiscountAmount = document.getExtraDiscountAmount();
        this.taxAmount = document.getTaxAmount();
        this.taxCategoryTotals = Objects.isNull(documentAggregate.getTaxCategoryTotals()) ? null :
                documentAggregate.getTaxCategoryTotals().stream()
                        .map(DocumentRequestPayload.TaxCategoryTotalRequestPayload::new).collect(Collectors.toList());
        this.payment = document.getPayment() == null ?
                null : new DocumentRequestPayload.PaymentDetailsRequestPayload(document.getPayment());
        this.delivery = document.getDelivery() == null ?
                null : new DocumentRequestPayload.DeliveryDetailsRequestPayload(document.getDelivery());
        this.transactionType = document.getTransactionType();
        this.transactionProgress = document.getTransactionProgress();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Builder(toBuilder = true)
    public static class OriginalDocument {
        private String submissionId;
        private String taxDocumentNumber;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Builder(toBuilder = true)
    public static class IssuedReason {
        private IssuedReasonCode code;
        private String notes;
    }

}

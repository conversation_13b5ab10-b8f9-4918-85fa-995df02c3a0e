package pt.jumia.services.bill.network;

import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.network.acl.AclMigratorService;

@Component
@AllArgsConstructor
public class ApplicationStartupListener implements ApplicationListener<ContextRefreshedEvent> {

    private final AclMigratorService aclMigratorService;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // Uncomment the code bellow to enable acl migrations
        aclMigratorService.migrate();
    }
}

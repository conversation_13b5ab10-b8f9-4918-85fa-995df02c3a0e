package pt.jumia.services.bill.network.afroms;

import lombok.extern.slf4j.Slf4j;
import pt.jumia.services.bill.network.afroms.payloads.ProductRequestPayload;
import pt.jumia.services.bill.network.afroms.payloads.ProductResponsePayload;
import retrofit2.Call;
import retrofit2.mock.Calls;

@Slf4j
public class FakeAfromsClient implements AfromsClient {

    public FakeAfromsClient() {
        log.warn("Using FakeAfromsClient!");
    }

    @Override
    public Call<ProductResponsePayload[]> getProductDetails(String apiKey, ProductRequestPayload productRequestPayload) {
        return Calls.response((ProductResponsePayload[]) null);
    }

}

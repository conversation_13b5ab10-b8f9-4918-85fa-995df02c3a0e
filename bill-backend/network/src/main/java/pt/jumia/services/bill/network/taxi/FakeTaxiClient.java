package pt.jumia.services.bill.network.taxi;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import pt.jumia.services.bill.network.taxi.payloads.CancelRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.CreditNoteRequestPayload;
import pt.jumia.services.bill.network.request.DocumentRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.DocumentRetryFiltersRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.ReceivedDocumentActionRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.StatementRequestPayload;
import retrofit2.Call;
import retrofit2.mock.Calls;

import java.util.List;

@Slf4j
public class FakeTaxiClient implements TaxiClient {

    public FakeTaxiClient() {
        log.warn("Using FakeTaxiClient!");
    }

    @Override
    public Call<ResponseBody> pushInvoice(String authorization, DocumentRequestPayload document) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<ResponseBody> pushCreditNote(String authorization, CreditNoteRequestPayload document) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<Void> ackError(String authorization, String entityId) {
        return Calls.response((Void) null);
    }

    @Override
    public Call<Void> cancelDocument(String authorization, String entityId, CancelRequestPayload cancelRequestPayload) {
        return Calls.response((Void) null);
    }

    @Override
    public Call<Void> declineDocumentRejection(String authorization, String entityId) {
        return Calls.response((Void) null);
    }

    @Override
    public Call<ResponseBody> getDocumentPdf(String authorization, String entityId) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<Void> bulkRetry(
            String authorization, DocumentRetryFiltersRequestPayload documentRetryFiltersRequestPayload) {

        return Calls.response((Void) null);
    }

    @Override
    public Call<Void> rejectReceivedDocument(
            String authorization,
            String entityTaxAuthoritiesId,
            ReceivedDocumentActionRequestPayload receivedDocumentActionRequestPayload) {
        return Calls.response((Void) null);
    }

    @Override
    public Call<Void> declineReceivedDocumentCancellation(
            String authorization,
            String entityTaxAuthoritiesId,
            ReceivedDocumentActionRequestPayload receivedDocumentActionRequestPayload) {
        return Calls.response((Void) null);
    }

    @Override
    public Call<Void> approveReceivedDocument(
            String authorization,
            String entityTaxAuthoritiesId,
            ReceivedDocumentActionRequestPayload receivedDocumentActionRequestPayload) {
        return Calls.response((Void) null);
    }

    @Override
    public Call<ResponseBody> pushStatement(String authorization, List<StatementRequestPayload> statementRequestPayloadList) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<Void> getReceivedDocumentsDetails(String authorization, List<String> receivedDocumentsIds) {
        return Calls.response((Void) null);
    }

}

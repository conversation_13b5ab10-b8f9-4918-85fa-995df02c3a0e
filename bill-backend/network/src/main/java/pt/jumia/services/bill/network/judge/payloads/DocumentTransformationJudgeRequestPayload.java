package pt.jumia.services.bill.network.judge.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentTransformation;

@Data
@NoArgsConstructor
public class DocumentTransformationJudgeRequestPayload {

    private DocumentTransformation.EntityType type;
    private String originalValue;
    private String newValue;

    public DocumentTransformationJudgeRequestPayload(DocumentTransformation documentTransformation) {
        this.type = documentTransformation.getType();
        this.originalValue = documentTransformation.getOriginalValue();
        this.newValue = documentTransformation.getNewValue();
    }

    public DocumentTransformation toEntity() {
        return DocumentTransformation.builder()
                .type(this.type)
                .originalValue(this.originalValue)
                .newValue(this.newValue)
                .build();
    }
}

package pt.jumia.services.bill.network.judge.payloads;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class JudgeReceivedDocumentContextPayload {

    private String id;
    private String sid;
    private String flow;
    private String shop;
    private String type;
    private List<DocumentLine> lines;
    private String notes;
    private Issuer issuer;
    private String status;
    private String country;
    private String currency;
    private Receiver receiver;
    private int lineCount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private LocalDateTime issuedDate;
    private String generatedBy;
    private BigDecimal totalAmount;
    private BigDecimal discountAmount;
    private String referenceNumber;
    private List<TaxCategoryTotal> taxCategoryTotals;
    private TaxAuthoritiesDetails taxAuthoritiesDetails;

    public JudgeReceivedDocumentContextPayload fromDocumentAggregate(DocumentAggregate documentAggregate){
        Document document = documentAggregate.getDocument();
        Issuer issuer = document.getIssuer();
        Receiver receiver = document.getReceiver();

         return JudgeReceivedDocumentContextPayload.builder()
                 .id(document.getId().toString())
                 .sid(document.getSid())
                 .flow(document.getFlow().name())
                 .shop(document.getShop())
                 .type(document.getSourceType())
                 .notes(document.getNotes())
                 .status(document.getStatus().name())
                 .country(document.getCountry().getAlpha2())
                 .currency(document.getCurrency().getCurrencyCode())
                 .issuedDate(document.getIssuedDate())
                 .generatedBy(document.getGeneratedBy())
                 .referenceNumber(document.getReferenceNumber())
                 .lineCount(document.getLineCount())
                 .netAmount(document.getNetAmount())
                 .taxAmount(document.getTaxAmount())
                 .totalAmount(document.getTotalAmount())
                 .discountAmount(document.getDiscountAmount())
                 .issuer(Issuer.builder()
                         .id(document.getIssuer().getId())
                         .name(document.getIssuer().getName() == null ? "N/A" : document.getIssuer().getName())
                         .type(document.getIssuer().getType())
                         .address(Address.builder()
                                 .city(issuer.getAddress().getCity() == null ? "N/A" : issuer.getAddress().getCity())
                                 .floor(issuer.getAddress().getFloor() == null ? "N/A" : issuer.getAddress().getFloor())
                                 .region(issuer.getAddress().getRegion() == null ? "N/A" : issuer.getAddress().getRegion())
                                 .street(issuer.getAddress().getStreet() == null ? "N/A" : issuer.getAddress().getStreet())
                                 .country(document.getIssuer().getAddress().getCountry())
                                 .postalCode(issuer.getAddress().getPostalCode() == null ?
                                         "N/A" : issuer.getAddress().getPostalCode())
                                 .buildingNumber(issuer.getAddress().getFloor() == null ?
                                         "N/A" : issuer.getAddress().getFloor())
                                 .additionalInformation(issuer.getAddress().getAdditionalInformation() == null
                                         ? "N/A" : issuer.getAddress().getAdditionalInformation())
                                 .build())
                         .createdAt(document.getIssuer().getCreatedAt())
                         .createdBy(document.getIssuer().getCreatedBy())
                         .legalName(document.getIssuer().getLegalName())
                         .linePhone(document.getIssuer().getLinePhone())
                         .mobilePhone(document.getIssuer().getMobilePhone())
                         .taxIdentificationNumber(document.getIssuer().getTaxIdentificationNumber())
                         .businessRegistrationNumber(document.getIssuer().getBusinessRegistrationNumber())
                         .build())
                 .receiver(Receiver.builder()
                         .id(document.getReceiver().getId())
                         .name(document.getReceiver().getName() == null ? "N/A" : document.getReceiver().getName())
                         .type(document.getReceiver().getType())
                         .address(Address.builder()
                                 .city(receiver.getAddress().getCity() == null ?
                                         "N/A" : receiver.getAddress().getCity())
                                 .floor(receiver.getAddress().getFloor() == null ?
                                         "N/A" : receiver.getAddress().getFloor())
                                 .region(receiver.getAddress().getRegion() == null ?
                                         "N/A" : receiver.getAddress().getRegion())
                                 .street(receiver.getAddress().getStreet() == null ?
                                         "N/A" : receiver.getAddress().getStreet())
                                 .country(document.getReceiver().getAddress().getCountry())
                                 .postalCode(receiver.getAddress().getPostalCode() == null ?
                                         "N/A" : receiver.getAddress().getPostalCode())
                                 .buildingNumber(receiver.getAddress().getFloor() == null ?
                                         "N/A" : receiver.getAddress().getFloor())
                                 .additionalInformation(receiver.getAddress().getAdditionalInformation() == null
                                         ? "N/A" : receiver.getAddress().getAdditionalInformation())
                                 .build())
                         .createdAt(document.getReceiver().getCreatedAt())
                         .createdBy(document.getReceiver().getCreatedBy())
                         .legalName(document.getReceiver().getLegalName())
                         .linePhone(document.getReceiver().getLinePhone())
                         .mobilePhone(document.getReceiver().getMobilePhone())
                         .taxIdentificationNumber(document.getReceiver().getTaxIdentificationNumber())
                         .businessRegistrationNumber(receiver.getBusinessRegistrationNumber())
                         .nationalIdentificationNumber(receiver.getNationalIdentificationNumber() == null ?
                                 "N/A" : receiver.getNationalIdentificationNumber())
                         .build())
                 .lines(documentAggregate.getLines())
                 .taxCategoryTotals(documentAggregate.getTaxCategoryTotals())
                 .taxAuthoritiesDetails(documentAggregate.getTaxAuthoritiesDetails())
                .build();
    }
}

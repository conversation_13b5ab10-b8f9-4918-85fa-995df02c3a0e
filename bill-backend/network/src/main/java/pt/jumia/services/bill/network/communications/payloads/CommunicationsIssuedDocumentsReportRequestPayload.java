package pt.jumia.services.bill.network.communications.payloads;

import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.DailyIssuedDocumentsReport;

@Data
@Builder(toBuilder = true)
public class CommunicationsIssuedDocumentsReportRequestPayload {
    private String email;
    private IssuedDocumentsReportRequestPayload context;

    public static CommunicationsIssuedDocumentsReportRequestPayload createReportPayload(
            DailyIssuedDocumentsReport dailyIssuedDocumentsReport,
            String email
    ) {
        return CommunicationsIssuedDocumentsReportRequestPayload.builder()
                .email(email)
                .context(IssuedDocumentsReportRequestPayload.generateReportPayload(dailyIssuedDocumentsReport))
                .build();
    }
}

package pt.jumia.services.bill.network.communications.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentLineWithNewDecimalFields;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DocumentLineRequestPayload {

    //
    // Internal fields
    //

    private UUID id;

    //
    // General information
    //

    private int position;
    private BigDecimal quantity;
    private UnitOfMeasure unitOfMeasure;

    //
    // Product information
    //

    private String itemCode;
    private String itemName;
    private CategoryRequestPayload category;

    //
    // Price information
    //

    private BigDecimal unitPrice;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal totalTaxAmount;
    private List<AppliedTaxRequestPayload> appliedTaxes;
    private DiscountRequestPayload discount;
    private BigDecimal discountValue;
    private BigDecimal discountRate;

    public DocumentLineRequestPayload(DocumentLine documentLine) {
        this.id = documentLine.getId();
        this.position = documentLine.getPosition();
        
        // Use new decimal fields if available, otherwise fall back to old fields
        if (documentLine instanceof DocumentLineWithNewDecimalFields) {
            DocumentLineWithNewDecimalFields lineWithNewFields = (DocumentLineWithNewDecimalFields) documentLine;
            this.quantity = lineWithNewFields.getQuantity_new() != null ? 
                    lineWithNewFields.getQuantity_new() : documentLine.getQuantity();
        } else {
            this.quantity = documentLine.getQuantity();
        }
        
        this.unitOfMeasure = documentLine.getUnitOfMeasure();
        this.itemCode = documentLine.getItemCode();
        this.itemName = documentLine.getItemName();
        this.category = new CategoryRequestPayload(documentLine.getCategory());
        
        // Use new decimal fields if available, otherwise fall back to old fields
        if (documentLine instanceof DocumentLineWithNewDecimalFields) {
            DocumentLineWithNewDecimalFields lineWithNewFields = (DocumentLineWithNewDecimalFields) documentLine;
            this.unitPrice = lineWithNewFields.getUnitPrice_new() != null ? 
                    lineWithNewFields.getUnitPrice_new() : documentLine.getUnitPrice();
            this.totalAmount = lineWithNewFields.getTotalAmount_new() != null ? 
                    lineWithNewFields.getTotalAmount_new() : documentLine.getTotalAmount();
            this.netAmount = lineWithNewFields.getNetAmount_new() != null ? 
                    lineWithNewFields.getNetAmount_new() : documentLine.getNetAmount();
            this.totalTaxAmount = lineWithNewFields.getTotalTaxAmount_new() != null ? 
                    lineWithNewFields.getTotalTaxAmount_new() : documentLine.getTotalTaxAmount();
        } else {
            this.unitPrice = documentLine.getUnitPrice();
            this.totalAmount = documentLine.getTotalAmount();
            this.netAmount = documentLine.getNetAmount();
            this.totalTaxAmount = documentLine.getTotalTaxAmount();
        }
        
        this.appliedTaxes = documentLine.getAppliedTaxes().stream().map(AppliedTaxRequestPayload::new).collect(Collectors.toList());
        this.discount = documentLine.getDiscount() == null ? null : new DiscountRequestPayload(documentLine.getDiscount());
        
        // Set discount value and rate using new fields if available
        if (documentLine instanceof DocumentLineWithNewDecimalFields && documentLine.getDiscount() != null) {
            DocumentLineWithNewDecimalFields lineWithNewFields = (DocumentLineWithNewDecimalFields) documentLine;
            this.discountValue = lineWithNewFields.getDiscountAmount_new() != null ? 
                    lineWithNewFields.getDiscountAmount_new() : documentLine.getDiscount().getAmount();
            this.discountRate = lineWithNewFields.getDiscountRate_new() != null ? 
                    lineWithNewFields.getDiscountRate_new() : documentLine.getDiscount().getRate();
        } else if (documentLine.getDiscount() != null) {
            this.discountValue = documentLine.getDiscount().getAmount();
            this.discountRate = documentLine.getDiscount().getRate();
        }
    }

    public DocumentLine toEntity(Document document) {
        return DocumentLine.builder()
                .id(id)
                .document(document)
                .position(position)
                .quantity(quantity)
                .unitOfMeasure(unitOfMeasure)
                .itemCode(itemCode)
                .itemName(itemName)
                .category(category.toEntity())
                .unitPrice(unitPrice)
                .totalAmount(totalAmount)
                .netAmount(netAmount)
                .totalTaxAmount(totalTaxAmount)
                .appliedTaxes(appliedTaxes.stream().map(AppliedTaxRequestPayload::toEntity).collect(Collectors.toList()))
                .discount(discount == null ? null : DocumentLine.Discount.builder()
                        .rate(discount.getRate())
                        .amount(discount.getAmount())
                        .build())
                .build();
    }

    @Data
    @NoArgsConstructor
    public static class DiscountRequestPayload {
        BigDecimal amount;
        BigDecimal rate;

        public DiscountRequestPayload(DocumentLine.Discount discount) {
            this.amount = discount.getAmount();
            this.rate = discount.getRate();
        }
    }
}

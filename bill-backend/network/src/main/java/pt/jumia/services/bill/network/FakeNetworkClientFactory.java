package pt.jumia.services.bill.network;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.Profiles;
import pt.jumia.services.bill.network.afroms.AfromsClient;
import pt.jumia.services.bill.network.communications.CommunicationsClient;
import pt.jumia.services.bill.network.communications.FakeCommunicationsClient;
import pt.jumia.services.bill.network.judge.FakeJudgeClient;
import pt.jumia.services.bill.network.judge.JudgeClient;
import pt.jumia.services.bill.network.afroms.FakeAfromsClient;
import pt.jumia.services.bill.network.taxi.FakeTaxiClient;
import pt.jumia.services.bill.network.taxi.TaxiClient;

@Component
@RequiredArgsConstructor
@Profile({Profiles.FAKE_CLIENTS})
public class FakeNetworkClientFactory implements ClientFactory {
    @Override
    public JudgeClient createJudgeClient(String endpoint) {
        return new FakeJudgeClient();
    }

    @Override
    public CommunicationsClient createCommunicationsClient(String endpoint) {
        return new FakeCommunicationsClient();
    }

    @Override
    public TaxiClient createTaxiClient(String endpoint) {
        return new FakeTaxiClient();
    }

    @Override
    public AfromsClient createAfromsClient(String endpoint) {
        return new FakeAfromsClient();
    }
}

package pt.jumia.services.bill.network.communications.payloads;

import com.neovisionaries.i18n.CountryCode;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Currency;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DocumentRequestPayload {

    //
    // Internal fields
    //

    private UUID id;
    private DocumentStatus status;
    private String judgeSid;

    //
    // General information
    //

    private CountryCode country;
    private String shop;
    private DocumentType type;
    private String sid;
    private DocumentFlow flow;
    private String generatedBy;
    private String referenceNumber;
    private LocalDateTime issuedDate;
    private String currency;
    private ReceiverRequestPayload receiver;
    private IssuerRequestPayload issuer;
    private DocumentIdRequestPayload originalDocument;
    private String notes;

    private List<DocumentLineRequestPayload> lines;

    //
    // Summary
    //

    private int lineCount;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;

    private List<TaxCategoryTotalRequestPayload> taxCategoryTotals;
    private TaxAuthoritiesDetailsRequestPayload taxAuthoritiesDetails;

    //
    // Audit information
    //

    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    private String sourceType;
    private String postingGroup;

    public DocumentRequestPayload(DocumentAggregate aggregate) {
        Document document = aggregate.getDocument();
        this.id = document.getId();
        this.status = document.getStatus();
        this.judgeSid = document.getJudgeSid();
        this.country = document.getCountry();
        this.shop = document.getShop();
        this.type = document.getType();
        this.sid = document.getSid();
        this.flow = document.getFlow();
        this.generatedBy = document.getGeneratedBy();
        this.referenceNumber = document.getReferenceNumber();
        this.issuedDate = document.getIssuedDate();
        this.currency = document.getCurrency().getCurrencyCode();
        this.receiver = new ReceiverRequestPayload(document.getReceiver());
        this.issuer = new IssuerRequestPayload(document.getIssuer());
        this.originalDocument = document.getOriginalDocument() != null
                ? new DocumentIdRequestPayload(document.getOriginalDocument())
                : null;
        this.notes = document.getNotes();
        this.lineCount = document.getLineCount();
        this.totalAmount = document.getTotalAmount();
        this.netAmount = document.getNetAmount();
        this.taxAmount = document.getTaxAmount();
        this.discountAmount = document.getDiscountAmount();
        this.createdAt = document.getCreatedAt();
        this.createdBy = document.getCreatedBy();
        this.updatedAt = document.getUpdatedAt();
        this.updatedBy = document.getUpdatedBy();

        this.lines = aggregate.getLines().stream().map(DocumentLineRequestPayload::new).collect(Collectors.toList());
        this.taxCategoryTotals = aggregate.getTaxCategoryTotals().stream().map(TaxCategoryTotalRequestPayload::new).collect(Collectors.toList());
        this.taxAuthoritiesDetails = aggregate.getTaxAuthoritiesDetails() != null
                ? new TaxAuthoritiesDetailsRequestPayload(aggregate.getTaxAuthoritiesDetails())
                : null;

        this.sourceType = document.getSourceType();
        this.postingGroup = document.getPostingGroup();
    }

    public DocumentAggregate toEntity() {
        Document document = Document
                .builder()
                .id(id)
                .status(status)
                .country(this.country)
                .shop(this.shop)
                .type(this.type)
                .sid(this.sid)
                .flow(this.flow)
                .generatedBy(this.generatedBy)
                .referenceNumber(this.referenceNumber)
                .issuedDate(this.issuedDate)
                .currency(Currency.getInstance(this.currency))
                .receiver(this.receiver.toEntity())
                .issuer(this.issuer.toEntity())
                .originalDocument(originalDocument != null ? originalDocument.toEntity() : null)
                .notes(this.notes)
                .lineCount(this.lineCount)
                .totalAmount(this.totalAmount)
                .netAmount(this.netAmount)
                .taxAmount(this.taxAmount)
                .discountAmount(this.discountAmount)
                .sourceType(this.sourceType)
                .postingGroup(this.postingGroup)
                .build();

        List<DocumentLine> lines = this.lines
                .stream()
                .map(documentLine -> documentLine.toEntity(document))
                .collect(Collectors.toList());

        List<TaxCategoryTotal> taxCategoryTotals = this.taxCategoryTotals
                .stream()
                .map(taxCategoryTotal -> taxCategoryTotal.toEntity(document))
                .collect(Collectors.toList());

        return DocumentAggregate
                .builder()
                .document(document)
                .lines(lines)
                .taxCategoryTotals(taxCategoryTotals)
                .taxAuthoritiesDetails(taxAuthoritiesDetails != null ? taxAuthoritiesDetails.toEntity(document) : null)
                .build();
    }
}

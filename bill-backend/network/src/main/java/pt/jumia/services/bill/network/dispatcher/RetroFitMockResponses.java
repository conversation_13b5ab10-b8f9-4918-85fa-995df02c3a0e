package pt.jumia.services.bill.network.dispatcher;

import okhttp3.MediaType;
import okhttp3.ResponseBody;
import org.apache.http.HttpStatus;
import retrofit2.Response;

@SuppressWarnings("PMD")
public class RetroFitMockResponses {
    public static Response serviceUnavailable() {
        MediaType mediaType = MediaType.parse("application/json");
        String content = "{ \"reason\": \"the server cannot be reach\" }";
        ResponseBody body = ResponseBody.create(content, mediaType);
        return Response.error(HttpStatus.SC_SERVICE_UNAVAILABLE, body);
    }
}

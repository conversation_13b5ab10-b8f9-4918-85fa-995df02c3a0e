package pt.jumia.services.bill.network.taxi.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class EntityMappingResponsePayload {

    private Long id;
    private String shop;
    private String country;
    private String type;
    private String jumiaValue;
    private String jumiaDescription;
    private String taxValue;
    private String taxDescription;
    private String status;
    private boolean addedBefore = false;
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;
}

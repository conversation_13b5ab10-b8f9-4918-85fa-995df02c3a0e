package pt.jumia.services.bill.network.communications;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsDocumentRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsIssuedDocumentsReportRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsReceivedDocumentsReportRequestPayload;
import retrofit2.Call;
import retrofit2.mock.Calls;

@Slf4j
public class FakeCommunicationsClient implements CommunicationsClient {

    public FakeCommunicationsClient() {
        log.warn("Using FakeCommunicationsClient!");
    }

    @Override
    public Call<ResponseBody> publishEvent(
            String authorization,
            String companyId,
            String eventName,
            CommunicationsDocumentRequestPayload communicationsDocumentRequestPayload
    ) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<ResponseBody> publishEmail(
            String authorization,
            String companyId,
            String eventName,
            CommunicationsIssuedDocumentsReportRequestPayload reportRequestPayload) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<ResponseBody> publishEmail(
            String authorization,
            String companyId,
            String eventName,
            CommunicationsReceivedDocumentsReportRequestPayload reportRequestPayload) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<ResponseBody> publishEmail(
            String authorization,
            String companyId,
            CommunicationsDocumentRequestPayload communicationsDocumentRequestPayload) {
        return Calls.response((ResponseBody) null);
    }
}

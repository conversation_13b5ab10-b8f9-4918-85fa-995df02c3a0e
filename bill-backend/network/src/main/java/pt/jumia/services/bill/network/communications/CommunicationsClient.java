package pt.jumia.services.bill.network.communications;

import okhttp3.ResponseBody;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsDocumentRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsIssuedDocumentsReportRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsReceivedDocumentsReportRequestPayload;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.Path;

public interface CommunicationsClient {

    @POST("/b2b/api/company/{companyId}/generic-events/{eventName}")
    Call<ResponseBody> publishEvent(
            @Header("Authorization") String authorization,
            @Path("companyId") String companyId,
            @Path("eventName") String eventName,
            @Body CommunicationsDocumentRequestPayload communicationsDocumentRequestPayload);

    @POST("/b2b/api/company/{companyId}/generic-events/{eventName}")
    Call<ResponseBody> publishEmail(
            @Header("Authorization") String authorization,
            @Path("companyId") String companyId,
            @Path("eventName") String eventName,
            @Body CommunicationsIssuedDocumentsReportRequestPayload reportRequestPayload);

    @POST("/b2b/api/company/{companyId}/generic-events/{eventName}")
    Call<ResponseBody> publishEmail(
            @Header("Authorization") String authorization,
            @Path("companyId") String companyId,
            @Path("eventName") String eventName,
            @Body CommunicationsReceivedDocumentsReportRequestPayload reportRequestPayload);

    @POST("/b2b/api/company/{companyId}/generic-events/bill_generic")
    Call<ResponseBody> publishEmail(
            @Header("Authorization") String authorization,
            @Path("companyId") String companyId,
            @Body CommunicationsDocumentRequestPayload communicationsDocumentRequestPayload);
}

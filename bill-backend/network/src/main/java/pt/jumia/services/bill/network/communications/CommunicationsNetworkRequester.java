package pt.jumia.services.bill.network.communications;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.CommunicationsRequester;
import pt.jumia.services.bill.domain.entities.DailyIssuedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DailyReceivedDocumentsReport;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.exceptions.communications.CommunicationsNetworkException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.domain.settings.OverallSettings;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsDocumentRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsIssuedDocumentsReportRequestPayload;
import pt.jumia.services.bill.network.communications.payloads.CommunicationsReceivedDocumentsReportRequestPayload;
import retrofit2.Response;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Locale;

@Component
@Slf4j
public class CommunicationsNetworkRequester implements CommunicationsRequester {
    private final CommunicationsClient communicationsClient;
    private final String token;
    private final OverallSettings overallSettings;

    @Autowired
    public CommunicationsNetworkRequester(ClientFactory clientFactory, NetworkProperties networkProperties,
                                          OverallSettings overallSettings) {
        this.communicationsClient = clientFactory.createCommunicationsClient(networkProperties.getCommunications().getUrl());
        this.token = "Basic " + Base64.encodeBase64String(String.format("%s:%s",
                        networkProperties.getCommunications().getUsername(),
                        networkProperties.getCommunications().getPassword())
                .getBytes(StandardCharsets.UTF_8));
        this.overallSettings = overallSettings;
    }

    @Override
    public void sendEmailReceivedDocument(DocumentAggregate documentAggregate) throws CommunicationsNetworkException {
        String event = getCommunicationModuleEventByDocumentType(documentAggregate);
        Response<ResponseBody> response;
        try {
            response = communicationsClient.publishEvent(token,
                            documentAggregate.getDocument().getShop() + "-" +
                                    documentAggregate.getDocument().getCountry().getAlpha2(),
                            event,
                            new CommunicationsDocumentRequestPayload(documentAggregate))
                    .execute();
        } catch (IOException e) {
            throw CommunicationsNetworkException.buildFailedRequest(
                    String.format("Failed to send event to communication module for document %s", documentAggregate),
                    e);
        }

        if (!response.isSuccessful()) {
            throw responseNotSuccess(response);
        }
    }

    @Override
    public void sendEmailReceivedDocument(DocumentAggregate documentAggregate, String email) throws CommunicationsNetworkException {

        CommunicationsDocumentRequestPayload communicationsDocumentRequestPayload =
                new CommunicationsDocumentRequestPayload(documentAggregate);
        communicationsDocumentRequestPayload.setEmail(email);

        Response<ResponseBody> response;
        try {
            log.info("Sending document with ID '{}' to communication module for email '{}'",
                    documentAggregate.getDocument().getSid(), email);

            response = communicationsClient.publishEmail(token,
                            documentAggregate.getDocument().getShop() + "-" +
                                    documentAggregate.getDocument().getCountry().getAlpha2(),
                            communicationsDocumentRequestPayload)
                    .execute();

        } catch (IOException e) {
            throw CommunicationsNetworkException.buildFailedRequest(
                    String.format("Failed to send document %s to communication module for email %s",
                            documentAggregate, email), e);
        }
        if (!response.isSuccessful()) {
            throw responseNotSuccess(response);
        }

        log.info("Document with ID'{}' Successfully sent to communication module for email '{}'",
                documentAggregate.getDocument().getSid(), email);
    }

    @Override
    public void sendEmailByIssuedDocumentsReportToReceiver(DailyIssuedDocumentsReport report, String email) throws CommunicationsNetworkException {

        String event = overallSettings.getDailyIssuedDocumentsReportSettings()
                .getCommunicationEventName(report.getBusinessLine().getShop(),
                        report.getBusinessLine().getCountryCode().getAlpha2());
        String businessLine = report.getBusinessLine().getShop() + "-" +
                report.getBusinessLine().getCountryCode().getAlpha2();
        Response<ResponseBody> response;
        try {
            response = communicationsClient.publishEmail(token,
                            businessLine,
                            event,
                            CommunicationsIssuedDocumentsReportRequestPayload.createReportPayload(report, email))
                    .execute();
        } catch (IOException e) {
            throw CommunicationsNetworkException.buildFailedRequest(
                    String.format("Failed to send report to communication module for businessLine: %s", businessLine),
                    e);
        }

        if (!response.isSuccessful()) {
            throw responseNotSuccess(response);
        }

    }

    @Override
    public void sendEmailByReceivedDocumentsReportToReceiver(DailyReceivedDocumentsReport report, String email) {
        String event = overallSettings.getDailyReceivedDocumentsReportSettings()
                .getCommunicationEventName(report.getBusinessLine().getShop(),
                        report.getBusinessLine().getCountryCode().getAlpha2());
        String businessLine = report.getBusinessLine().getShop() + "-" +
                report.getBusinessLine().getCountryCode().getAlpha2();
        Response<ResponseBody> response;
        try {
            response = communicationsClient.publishEmail(token,
                            businessLine,
                            event,
                            CommunicationsReceivedDocumentsReportRequestPayload.createReportPayload(report, email))
                    .execute();
        } catch (IOException e) {
            throw CommunicationsNetworkException.buildFailedRequest(
                    String.format("Failed to send report to communication module for businessLine: %s", businessLine),
                    e);
        }

        if (!response.isSuccessful()) {
            throw responseNotSuccess(response);
        }

    }

    private String getCommunicationModuleEventByDocumentType(DocumentAggregate documentAggregate) {
        if (DocumentType.SALES_INVOICE.equals(documentAggregate.getDocument().getType())
                || DocumentType.PURCHASE_INVOICE.equals(documentAggregate.getDocument().getType())) {
            return this.overallSettings.getCommunicationsSettings().getInvoiceEvent(
                    documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                    documentAggregate.getDocument().getCountry().getAlpha2());
        } else if (DocumentType.SALES_CREDIT_NOTE.equals(documentAggregate.getDocument().getType())
                || DocumentType.PURCHASE_CREDIT_NOTE.equals(documentAggregate.getDocument().getType())) {
            return this.overallSettings.getCommunicationsSettings().getCreditNoteEvent(
                    documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                    documentAggregate.getDocument().getCountry().getAlpha2());
        } else if (DocumentType.SALES_CREDIT_MEMO.equals(documentAggregate.getDocument().getType())) {
            return this.overallSettings.getCommunicationsSettings().getCreditMemoEvent(
                    documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                    documentAggregate.getDocument().getCountry().getAlpha2());
        } else {
            throw CommunicationsNetworkException.buildFailedRequest(
                    String.format("Cannot find proper event code for '%s' of shop: '%s' and country: '%s'",
                            documentAggregate.getDocument().getType(),
                            documentAggregate.getDocument().getShop().toLowerCase(Locale.US),
                            documentAggregate.getDocument().getCountry().getAlpha2())
            );
        }
    }

    private CommunicationsNetworkException responseNotSuccess(Response<?> response) {
        try {
            return CommunicationsNetworkException.buildResponseNotOk(response.code(),
                    response.errorBody() == null ? null : response.errorBody().string());
        } catch (IOException e) {
            return CommunicationsNetworkException.buildResponseNotOk(response.code(), "Failed reading response body", e);
        }
    }
}

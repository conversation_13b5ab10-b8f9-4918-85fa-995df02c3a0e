package pt.jumia.services.bill.network.communications.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentId;

import java.util.UUID;

@Data
@NoArgsConstructor
public class DocumentIdRequestPayload {

    private UUID id;
    private String sid;

    public DocumentIdRequestPayload(DocumentId documentId) {
        id = documentId.getId();
        sid = documentId.getSid();
    }

    public DocumentId toEntity() {
        return DocumentId.builder()
                .id(id)
                .sid(sid)
                .build();
    }
}

package pt.jumia.services.bill.network.communications.payloads;

import lombok.Builder;
import lombok.Data;
import pt.jumia.services.bill.domain.entities.DailyReceivedDocumentsReport;

@Data
@Builder(toBuilder = true)
public class CommunicationsReceivedDocumentsReportRequestPayload {
    private String email;
    private ReceivedDocumentsReportRequestPayload context;

    public static CommunicationsReceivedDocumentsReportRequestPayload createReportPayload(
            DailyReceivedDocumentsReport report,
            String email
    ) {
        return CommunicationsReceivedDocumentsReportRequestPayload.builder()
                .email(email)
                .context(ReceivedDocumentsReportRequestPayload.generateReportPayload(report))
                .build();
    }
}

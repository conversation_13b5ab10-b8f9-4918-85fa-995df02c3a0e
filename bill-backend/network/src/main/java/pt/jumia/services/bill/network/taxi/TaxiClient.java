package pt.jumia.services.bill.network.taxi;

import okhttp3.ResponseBody;
import pt.jumia.services.bill.network.taxi.payloads.CancelRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.CreditNoteRequestPayload;
import pt.jumia.services.bill.network.request.DocumentRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.DocumentRetryFiltersRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.ReceivedDocumentActionRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.StatementRequestPayload;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;

import java.util.List;

public interface TaxiClient {

    @POST("/api/invoices")
    Call<ResponseBody> pushInvoice(
            @Header("Authorization") String authorization,
            @Body DocumentRequestPayload document);

    @POST("/api/credit-notes")
    Call<ResponseBody> pushCreditNote(
            @Header("Authorization") String authorization,
            @Body CreditNoteRequestPayload document);

    @POST("/api/api-logs/entity/{entityId}/acknowledge")
    Call<Void> ackError(
            @Header("Authorization") String authorization,
            @Path("entityId") String entityId);

    @POST("/api/api-logs/entity/{entityId}/cancel")
    Call<Void> cancelDocument(
            @Header("Authorization") String authorization,
            @Path("entityId") String entityId,
            @Body CancelRequestPayload cancelRequestPayload);

    @POST("/api/api-logs/entity/{entityId}/decline-rejection")
    Call<Void> declineDocumentRejection(
            @Header("Authorization") String authorization,
            @Path("entityId") String entityId);

    @GET("/api/api-logs/{entityId}/pdf")
    Call<ResponseBody> getDocumentPdf(
            @Header("Authorization") String authorization,
            @Path("entityId") String entityId);

    @POST("/api/api-logs/retry")
    Call<Void> bulkRetry(
            @Header("Authorization") String authorization,
            @Body DocumentRetryFiltersRequestPayload documentRetryFiltersRequestPayload);

    @PUT("/api/received-documents/{relatedEntityId}/reject")
    Call<Void> rejectReceivedDocument(
            @Header("Authorization") String authorization,
            @Path("relatedEntityId") String relatedEntityId,
            @Body ReceivedDocumentActionRequestPayload receivedDocumentActionRequestPayload);

    @PUT("/api/received-documents/{relatedEntityId}/decline-cancellation")
    Call<Void> declineReceivedDocumentCancellation(
            @Header("Authorization") String authorization,
            @Path("relatedEntityId") String relatedEntityId,
            @Body ReceivedDocumentActionRequestPayload receivedDocumentActionRequestPayload);

    @PUT("/api/received-documents/{relatedEntityId}/approve")
    Call<Void> approveReceivedDocument(
            @Header("Authorization") String authorization,
            @Path("relatedEntityId") String relatedEntityId,
            @Body ReceivedDocumentActionRequestPayload receivedDocumentActionRequestPayload);

    @POST("/api/statement")
    Call<ResponseBody> pushStatement(
            @Header("Authorization") String authorization,
            @Body List<StatementRequestPayload> statementRequestPayloadList);

    @GET("/api/received-documents/details")
    Call<Void> getReceivedDocumentsDetails(
            @Header("Authorization") String authorization,
            @Query("receivedDocumentsIds") List<String> receivedDocumentsIds);
}

package pt.jumia.services.bill.network.judge.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder(toBuilder = true)
public class JudgeDocumentResponse {
    private long id;
    private String sid;
    private String origin;
    private String filename;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder(toBuilder = true)
    public static class DocumentTemplate {
        private long id;
        private String code;
    }
}

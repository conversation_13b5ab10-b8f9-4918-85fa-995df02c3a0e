package pt.jumia.services.bill.network.acl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import pt.jumia.services.acl.lib.AclErrorException;
import pt.jumia.services.acl.lib.RequestUser;
import pt.jumia.services.acl.lib.payloads.ApplicationResponsePayload;
import pt.jumia.services.acl.lib.payloads.PermissionRequestPayload;
import pt.jumia.services.acl.lib.payloads.PermissionResponsePayload;
import pt.jumia.services.acl.migrator.AclMigrator;
import pt.jumia.services.acl.migrator.callback.MigrationCallback;
import pt.jumia.services.bill.domain.Permissions;
import pt.jumia.services.bill.domain.properties.AclProperties;
import pt.jumia.services.bill.domain.properties.InfoProperties;

import java.net.HttpURLConnection;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class AclMigratorService {

    private static final String APPLICATION_TARGET_TYPE = "APPLICATION";
    private static final String COUNTRY_TARGET_TYPE = "COUNTRY";

    private final InfoProperties infoProperties;
    private final AclProperties aclProperties;
    private final AclNetworkRequester aclNetworkRequester;

    public void migrate() {
        try {
            AclMigrator aclMigrator = new AclMigrator(aclNetworkRequester.getAclConnectApiClient());
            RequestUser requestUser = aclNetworkRequester.authorize(
                    aclProperties.getMigratorUser().getUsername(),
                    aclProperties.getMigratorUser().getPassword());

            aclMigrator.run(
                    requestUser,
                    aclProperties.getAppName(),
                    infoProperties.getBuild().getVersion(),
                    createMigrationCallbacks()
            );
        } catch (AclErrorException e) {
            if (e.getCode() == HttpURLConnection.HTTP_FORBIDDEN) {
                log.error("Application {} does not have the necessary permissions to create new permissions. Migrations were not run," +
                        " fix the application profile before trying again", aclProperties.getAppName());
            } else if (e.getCode() == HttpURLConnection.HTTP_CONFLICT) {
                log.error("Application {} has no current version stored. Migrations were not run, set the application version before" +
                        " trying again", aclProperties.getAppName());
            } else {
                log.error("Unable to authenticate user {}. Migrations were not run.", aclProperties.getMigratorUser().getUsername());
            }
        } catch (Exception e) {
            log.error("Something unexpected happened. Migrations were not run. {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private Map<String, MigrationCallback> createMigrationCallbacks() {
        Map<String, MigrationCallback> migrationMap = new HashMap<>();

        migrationMap.put("0.1.0", (app, user) -> {
            log.info("Running migrations for version 0.1.0");
            createPermission(app, user, Permissions.CAN_ACCESS, "Allows user to access the Bill application",
                    APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.CAN_MANAGE_DOCUMENTS, "Allows user to create documents by country in Bill",
                    COUNTRY_TARGET_TYPE);
            createPermission(app, user, Permissions.CAN_VIEW_DOCUMENTS, "Allows user to view documents in Bill",
                    COUNTRY_TARGET_TYPE);
            createPermission(app, user, Permissions.MANAGE_SETTINGS, "Allows the user to manage settings in Bill",
                    APPLICATION_TARGET_TYPE);
            createPermission(app, user, Permissions.RESUBMIT_DOCUMENT, "Allows the user to resubmit document by country in Bill",
                    COUNTRY_TARGET_TYPE);
            createPermission(app, user, Permissions.ACK_DOCUMENT, "Allows the user to acknowledge document by country in Bill",
                    COUNTRY_TARGET_TYPE);
            createPermission(app, user, Permissions.CANCEL_DOCUMENT, "Allows the user to cancel document by country in Bill",
                    COUNTRY_TARGET_TYPE);
            createPermission(app, user, Permissions.DECLINE_REJECTED_DOCUMENT, "Allows the user to cancel document by country in Bill",
                    COUNTRY_TARGET_TYPE);
            return true;
        });

        migrationMap.put("0.18.0", (app, user) -> {
            log.info("Running migrations for version 0.18.0");
            createPermission(app, user, Permissions.CAN_RETRY_DOCUMENTS, "Allows user to retry documents in TaxI from Bill.",
                    COUNTRY_TARGET_TYPE);
            return true;
        });

        migrationMap.put("0.25.0", (app, user) -> {
            log.info("Running migrations for version 0.25.0");
            createPermission(app, user, Permissions.CAN_LIST_RECEIVED_DOCUMENTS,
                    "Allows user to list all received documents by country in Bill.",
                    COUNTRY_TARGET_TYPE);
            createPermission(app, user, Permissions.CAN_CREATE_RECEIVED_DOCUMENTS,
                    "Allows user to create new received documents by country in Bill.",
                    COUNTRY_TARGET_TYPE);
            return true;
        });

        migrationMap.put("0.27.0", (app, user) -> {
            log.info("Running migrations for version 0.27.0");
            createPermission(app, user, Permissions.REVIEW_RECEIVED_DOCUMENTS,
                    "Allows user to review received documents by country in Bill.",
                    COUNTRY_TARGET_TYPE);
            return true;
        });

        migrationMap.put("0.28.0", (app, user) -> {
            log.info("Running migrations for version 0.28.0");
            createPermission(app, user, Permissions.REJECT_RECEIVED_DOCUMENTS,
                    "Allows user to reject received documents by country in Bill.",
                    COUNTRY_TARGET_TYPE);
            return true;
        });

        migrationMap.put("0.31.0", (app, user) -> {
            log.info("Running migrations for version 0.31.0");
            createPermission(app, user, Permissions.DECLINE_RECEIVED_DOCUMENTS_CANCELLATION,
                    "Allows user to decline received documents cancellation by country in Bill.",
                    COUNTRY_TARGET_TYPE);
            return true;
        });

        migrationMap.put("0.62.0", (app, user) -> {
            log.info("Running migrations for version 0.62.0");
            createPermission(app, user, Permissions.APPROVED_RECEIVED_DOCUMENTS,
                    "Allows user to approve received documents by country in Bill.",
                    COUNTRY_TARGET_TYPE);
            return true;
        });

        return migrationMap;
    }

    private void createPermission(ApplicationResponsePayload application,
                                  RequestUser requestUser,
                                  String permissionCode,
                                  String description,
                                  String targetType) {

        PermissionRequestPayload permissionRequest = new PermissionRequestPayload();

        permissionRequest.setApplication(application.getId());
        permissionRequest.setCode(permissionCode);
        permissionRequest.setTargetType(targetType);
        permissionRequest.setDescription(description);

        try {
            PermissionResponsePayload permission =
                    aclNetworkRequester.getAclConnectApiClient().management().permissions().create(requestUser, permissionRequest);
            log.info("Created permission: {}", permission.toString());
        } catch (AclErrorException e) {

            if (Integer.valueOf(HttpURLConnection.HTTP_CONFLICT).equals(e.getCode())) {
                log.info("Permission {} already exists, skipping.", permissionRequest.getCode());
            } else {
                log.error("Permission creation failed: {}", e.getMessage());
                throw (e);
            }
        }
    }
}

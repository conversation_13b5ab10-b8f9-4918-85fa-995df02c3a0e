package pt.jumia.services.bill.network.afroms.payloads;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder(toBuilder = true)
public class ProductRequestPayload {

    @Builder.Default
    private String module = "omscatalog";
    @Builder.Default
    private String service = "Product";
    @Builder.Default
    private String method = "index";
    @Builder.Default
    private List<Message> message = new ArrayList<>();

    public void addMessage(Message newMessage) {
        this.message.add(newMessage);
    }

    @Data
    @Builder(toBuilder = true)
    public static class Message {
        private String sku;
    }

}

package pt.jumia.services.bill.network.kafka.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import pt.jumia.services.bill.network.response.TaxAuthoritiesDetailsResponsePayload;

/**
 * <AUTHOR>
 * at 05/17/2022
 */
@Slf4j
@Configuration
public class KafkaConsumerConfiguration {

    private <T> ConsumerFactory<String, T> getConsumerFactory(Class<T> payloadClass, KafkaProperties properties) {
        return new DefaultKafkaConsumerFactory<>(
                properties.buildConsumerProperties(),
                new StringDeserializer(),
                new JsonDeserializer<>(payloadClass, false)
        );
    }

    private <T> ConcurrentKafkaListenerContainerFactory<String, T> getListenerContainerFactory(ConsumerFactory<String, T> consumerFactory) {
        ConcurrentKafkaListenerContainerFactory<String, T> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory);
        return factory;
    }

    /**
     * Default setup. Needed because autoconfiguration
     */
    @Bean
    public ConsumerFactory<String, Object> consumerFactory(
            KafkaProperties properties
    ) {
        return this.getConsumerFactory(Object.class, properties);
    }

    /**
     * Default setup. Needed because autoconfiguration
     */
    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, Object> kafkaListenerContainerFactory(KafkaProperties properties) {
        return this.getListenerContainerFactory(consumerFactory(properties));
    }

    /**
     * Bill consumer
     */
    @Bean
    public ConsumerFactory<String, TaxAuthoritiesDetailsResponsePayload> billFactory(KafkaProperties properties) {
        return this.getConsumerFactory(TaxAuthoritiesDetailsResponsePayload.class, properties);
    }


    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, TaxAuthoritiesDetailsResponsePayload> billListenerContainerFactory(
            ConsumerFactory<String, TaxAuthoritiesDetailsResponsePayload> consumerFactory
    ) {
        return this.getListenerContainerFactory(consumerFactory);
    }

    @Bean
    public ConcurrentKafkaListenerContainerFactory<String, TaxAuthoritiesDetailsResponsePayload> genericListenerContainerFactory(
            ConsumerFactory<String, TaxAuthoritiesDetailsResponsePayload> consumerFactory
    ) {
        return this.getListenerContainerFactory(consumerFactory);
    }

    @Bean
    @ConfigurationProperties("spring.kafka.cluster-bill")
    @Qualifier("clusterBILL")
    @Primary
    public KafkaProperties clusterBILL() {
        return new KafkaProperties();
    }
}

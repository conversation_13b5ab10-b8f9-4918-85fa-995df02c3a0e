package pt.jumia.services.bill.network.judge;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import pt.jumia.services.bill.network.judge.payloads.JudgeDocumentRequestPayload;
import pt.jumia.services.bill.network.judge.response.JudgeDocumentResponse;
import retrofit2.Call;
import retrofit2.mock.Calls;

@Slf4j
public class FakeJudgeClient implements JudgeClient {

    public FakeJudgeClient() {
        log.warn("Using FakeJudgeClient!");
    }

    @Override
    public Call<ResponseBody> downloadDocumentRawBySid(String authorization, String sid) {
        return Calls.response((ResponseBody) null);
    }

    @Override
    public Call<JudgeDocumentResponse> generateNewDocumentByCode(
            String authorization,
            String code,
            JudgeDocumentRequestPayload judgeDocumentRequestPayload
    ) {
        return Calls.response(JudgeDocumentResponse.builder()
                .id(123L)
                .filename("readme")
                .origin("universe")
                .sid("sid-xpto")
                .build());
    }
}

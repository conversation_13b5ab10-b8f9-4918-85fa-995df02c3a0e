package pt.jumia.services.bill.network.taxi;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pt.jumia.services.bill.domain.TaxiRequester;
import pt.jumia.services.bill.domain.entities.CancelRequest;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFile;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.filters.DocumentRetryFilters;
import pt.jumia.services.bill.domain.exceptions.taxi.TaxiNetworkException;
import pt.jumia.services.bill.domain.properties.NetworkProperties;
import pt.jumia.services.bill.domain.usecases.documents.ReadDocumentAggregateUseCase;
import pt.jumia.services.bill.network.ClientFactory;
import pt.jumia.services.bill.network.request.DocumentRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.CancelRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.CreditNoteRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.DocumentRetryFiltersRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.ReceivedDocumentActionRequestPayload;
import pt.jumia.services.bill.network.taxi.payloads.StatementRequestPayload;
import retrofit2.Response;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TaxiNetworkRequester implements TaxiRequester {

    private final ReadDocumentAggregateUseCase readDocumentAggregateUseCase;
    private final TaxiClient taxiClient;
    private final String token;

    @Autowired
    public TaxiNetworkRequester(ReadDocumentAggregateUseCase readDocumentAggregateUseCase,
                                ClientFactory clientFactory,
                                NetworkProperties networkProperties) {
        this.readDocumentAggregateUseCase = readDocumentAggregateUseCase;
        this.taxiClient = clientFactory.createTaxiClient(networkProperties.getTaxi().getUrl());
        String username = networkProperties.getTaxi().getUsername();
        this.token = "Basic " + Base64.encodeBase64String(String.format("%s:%s",
                        username,
                        networkProperties.getTaxi().getPassword())
                .getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public void pushInvoice(DocumentAggregate documentAggregate) {
        log.info("Pushing invoice to TaxI. [{}]", documentAggregate.getDocument().getSid());

        try {
            Response<ResponseBody> response = taxiClient.pushInvoice(token, new DocumentRequestPayload(documentAggregate)).execute();
            if (!response.isSuccessful()) {
                log.error("Error pushing document [{}] to TaxI. Error code: [{}], Error Message: [{}]",
                        documentAggregate.getDocument().getSid(), response.code(), response.message());
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        response.errorBody() == null ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            log.error("An exception occurred while pushing document [{}] to TaxI. {}",
                    documentAggregate.getDocument().getSid(), ExceptionUtils.getStackTrace(e));
            throw TaxiNetworkException.buildFailedRequest(String.format("Failed to push document %s to taxi", documentAggregate), e);
        }

    }

    @Override
    public void pushCreditNote(String originalDocumentSid, DocumentAggregate documentAggregate) {
        log.info("Pushing Credit Note to TaxI. [{}]", documentAggregate.getDocument().getSid());

        try {
            DocumentAggregate originalDocument = readDocumentAggregateUseCase.executeBySidAndSuccessStatus(originalDocumentSid);

            Response<ResponseBody> response =
                    taxiClient.pushCreditNote(token, new CreditNoteRequestPayload(originalDocument, documentAggregate)).execute();
            if (!response.isSuccessful()) {
                log.error("Error pushing document [{}] to TaxI. Error code: [{}], Error Message: [{}]",
                        documentAggregate.getDocument().getSid(), response.code(), response.message());
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        response.errorBody() == null ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            log.error("An exception occurred while pushing document [{}] to TaxI. {}",
                    documentAggregate.getDocument().getSid(), ExceptionUtils.getStackTrace(e));
            throw TaxiNetworkException.buildFailedRequest(String.format("Failed to push document %s to taxi", documentAggregate), e);
        }

    }

    @Override
    public void ackError(String entityId) {
        try {
            Response<Void> response = taxiClient.ackError(token, entityId).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        response.errorBody() == null ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            String message = String.format("Failed to acknowledge error in TaxI for entity with ID: %s ", entityId);
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public void cancelDocument(CancelRequest cancelRequest) {
        try {
            Response<Void> response = taxiClient.cancelDocument(token,
                            cancelRequest.getDocumentId().toString(),
                            new CancelRequestPayload(cancelRequest))
                    .execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        response.errorBody() == null ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            String message = String.format("Failed to cancel document, error in Taxi for entity with ID: %s ",
                    cancelRequest.getDocumentId().toString());
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public void declineDocumentRejection(String entityId) {
        try {
            Response<Void> response = taxiClient.declineDocumentRejection(token, entityId).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        Objects.isNull(response.errorBody()) ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            String message = String.format("Failed to decline document rejection, error in Taxi for entity with ID: %s ", entityId);
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public void rejectReceivedDocument(DocumentAggregate receivedDocument, String rejectionReason) {
        try {
            Response<Void> response = taxiClient.rejectReceivedDocument(
                            token,
                            receivedDocument.getDocument().getId().toString(),
                            new ReceivedDocumentActionRequestPayload(
                                    receivedDocument.getDocument(),
                                    rejectionReason
                            )
                    )
                    .execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        Objects.isNull(response.errorBody()) ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            String message = String.format("Failed to reject received document," +
                            "error in Taxi for document ID: %s ",
                    receivedDocument.getDocument().getId());
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public void approveReceivedDocument(DocumentAggregate receivedDocument) {
        try {
            Response<Void> response = taxiClient.approveReceivedDocument(
                    token,
                    receivedDocument.getDocument().getId().toString(),
                    new ReceivedDocumentActionRequestPayload(receivedDocument.getDocument())).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        Objects.isNull(response.errorBody()) ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            String message = String.format("Failed to approve received document," +
                            "error in Taxi for document ID: %s ",
                    receivedDocument.getDocument().getId());
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public void declineReceivedDocumentCancellation(DocumentAggregate receivedDocument) {
        try {
            Response<Void> response = taxiClient.declineReceivedDocumentCancellation(
                    token,
                    receivedDocument.getDocument().getId().toString(),
                    new ReceivedDocumentActionRequestPayload(receivedDocument.getDocument())).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        Objects.isNull(response.errorBody()) ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            String message = String.format("Failed to decline received document cancellation," +
                            "error in Taxi for document ID: %s ",
                    receivedDocument.getDocument().getId());
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public DocumentFile getTaxAuthoritiesPdf(String entityId) {
        try {
            Response<ResponseBody> response = taxiClient.getDocumentPdf(token, entityId).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        response.errorBody() == null ? null : response.errorBody().string());
            }
            return DocumentFile.builder()
                    .content(response.body().byteStream().readAllBytes())
                    .mediaType(response.body().contentType().toString())
                    .build();
        } catch (TaxiNetworkException taxiNetworkException) {
            String message = String.format("Failed to get Tax Authorities Pdf for document with ID: %s," +
                    " with message from Taxi %s", entityId, taxiNetworkException.getMessage());
            log.warn(message, taxiNetworkException);
            throw TaxiNetworkException.buildFailedRequest(message, taxiNetworkException);
        } catch (Exception e) {
            String message = String.format("Failed to get Tax Authorities Pdf for document with ID: %s", entityId);
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public void bulkRetryDocuments(DocumentRetryFilters documentRetryFilters) {
        try {
            Response<Void> response = taxiClient.bulkRetry(
                    token, new DocumentRetryFiltersRequestPayload(documentRetryFilters)
            ).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(
                        response.code(),
                        response.errorBody() == null ? null : response.errorBody().string()
                );
            }
        } catch (TaxiNetworkException taxiNetworkException) {
            String message = String.format("Failed to get Tax Authorities Retry document," +
                    " with message from Taxi %s", taxiNetworkException.getMessage());
            log.warn(message, taxiNetworkException);
            throw TaxiNetworkException.buildFailedRequest(message, taxiNetworkException);
        } catch (Exception e) {
            String message = "Failed to get Tax Authorities Retry document";
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

    @Override
    public void pushStatement(List<DocumentAggregate> documentAggregateList) {
        try {
            List<StatementRequestPayload> statementRequestPayloadList =
                    documentAggregateList.stream()
                            .map(documentAggregate -> new StatementRequestPayload(documentAggregate,
                                    (documentAggregate.getDocument().getType().equals(DocumentType.SALES_CREDIT_NOTE) ||
                                            documentAggregate.getDocument().getType().equals(DocumentType.PURCHASE_CREDIT_NOTE)) &&
                                            documentAggregate.getDocument().getOriginalDocument() != null ?
                                            readDocumentAggregateUseCase
                                                    .execute(documentAggregate.getDocument().getOriginalDocument().getId())
                                            : null))
                            .collect(Collectors.toList());
            Response<ResponseBody> response =
                    taxiClient.pushStatement(token, statementRequestPayloadList).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(response.code(),
                        response.errorBody() == null ? null : response.errorBody().string());
            }
        } catch (Exception e) {
            throw TaxiNetworkException.buildFailedRequest(String.format("Failed to push statement %s to taxi",
                    documentAggregateList), e);
        }
    }

    @Override
    public void getReceivedDocumentsDetails(List<String> receivedDocumentsIds) {
        try {
            Response<Void> response = taxiClient.getReceivedDocumentsDetails(
                    token, receivedDocumentsIds
            ).execute();
            if (!response.isSuccessful()) {
                throw TaxiNetworkException.buildResponseNotOk(
                        response.code(),
                        response.errorBody() == null ? null : response.errorBody().string()
                );
            }
        } catch (TaxiNetworkException taxiNetworkException) {
            String message = String.format("Failed to get received documents details," +
                    " with message from Taxi %s", taxiNetworkException.getMessage());
            log.warn(message, taxiNetworkException);
            throw TaxiNetworkException.buildFailedRequest(message, taxiNetworkException);
        } catch (Exception e) {
            String message = "Failed to get received documents details";
            log.warn(message, e);
            throw TaxiNetworkException.buildFailedRequest(message, e);
        }
    }

}

package pt.jumia.services.bill.network.judge.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.TaxAuthoritiesDetails;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class TaxAuthoritiesDetailsRequestPayload {

    private UUID id;

    private String submissionId;
    private String taxDocumentNumber;
    private String qrCode;
    private String verificationCode;
    private String deviceNumber;
    private String internalData;
    private String statusCode;
    private String errorCode;
    private String exception;
    private LocalDateTime createdAt;
    private String createdBy;
    private LocalDateTime updatedAt;
    private String updatedBy;

    public TaxAuthoritiesDetailsRequestPayload(TaxAuthoritiesDetails taxAuthoritiesDetails) {
        this.id = taxAuthoritiesDetails.getId();
        this.submissionId = taxAuthoritiesDetails.getSubmissionId();
        this.taxDocumentNumber = taxAuthoritiesDetails.getTaxDocumentNumber();
        this.qrCode = taxAuthoritiesDetails.getQrCode();
        this.verificationCode = taxAuthoritiesDetails.getVerificationCode();
        this.internalData = taxAuthoritiesDetails.getInternalData();
        this.deviceNumber = taxAuthoritiesDetails.getDeviceNumber();
        this.statusCode = taxAuthoritiesDetails.getStatusCode();
        this.errorCode = taxAuthoritiesDetails.getErrorCode();
        this.exception = taxAuthoritiesDetails.getException();
        this.createdAt = taxAuthoritiesDetails.getCreatedAt();
        this.createdBy = taxAuthoritiesDetails.getCreatedBy();
        this.updatedAt = taxAuthoritiesDetails.getUpdatedAt();
        this.updatedBy = taxAuthoritiesDetails.getUpdatedBy();
    }

    public TaxAuthoritiesDetails toEntity(Document document) {
        return TaxAuthoritiesDetails.builder()
                .id(id)
                .document(document)
                .submissionId(submissionId)
                .taxDocumentNumber(taxDocumentNumber)
                .qrCode(qrCode)
                .verificationCode(verificationCode)
                .deviceNumber(deviceNumber)
                .internalData(internalData)
                .statusCode(statusCode)
                .errorCode(errorCode)
                .exception(exception)
                .createdAt(createdAt)
                .createdBy(createdBy)
                .updatedAt(updatedAt)
                .updatedBy(updatedBy)
                .build();
    }
}

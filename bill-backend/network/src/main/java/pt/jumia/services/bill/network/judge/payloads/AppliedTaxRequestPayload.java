package pt.jumia.services.bill.network.judge.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.TaxCategory;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class AppliedTaxRequestPayload {

    private TaxCategory category;
    private BigDecimal rate;
    private BigDecimal fixedAmount;
    private BigDecimal appliedAmount;

    public AppliedTaxRequestPayload(DocumentLine.AppliedTax document) {
        this.category = document.getTaxCategory();
        this.rate = document.getTaxRate();
        this.fixedAmount = document.getTaxFixedAmount();
        this.appliedAmount = document.getTaxAmount();
    }

    public DocumentLine.AppliedTax toEntity() {
        return DocumentLine.AppliedTax.builder()
                .taxCategory(category)
                .taxRate(rate)
                .taxFixedAmount(fixedAmount)
                .taxAmount(appliedAmount)
                .build();
    }
}

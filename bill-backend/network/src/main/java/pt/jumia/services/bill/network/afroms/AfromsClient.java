package pt.jumia.services.bill.network.afroms;

import okhttp3.ResponseBody;
import pt.jumia.services.bill.network.afroms.payloads.ProductRequestPayload;
import pt.jumia.services.bill.network.afroms.payloads.ProductResponsePayload;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

public interface AfromsClient {

    @POST("/services")
    Call<ProductResponsePayload[]> getProductDetails(
            @Header("Console-Api-Key") String apiKey,
            @Body ProductRequestPayload productRequestPayload);

}

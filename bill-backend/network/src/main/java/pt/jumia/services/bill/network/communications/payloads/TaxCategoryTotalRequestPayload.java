package pt.jumia.services.bill.network.communications.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotalWithNewDecimalFields;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@NoArgsConstructor
public class TaxCategoryTotalRequestPayload {

    //
    // Internal fields
    //

    private UUID id;

    //
    // Tax information
    //

    private TaxCategory taxCategory;
    private BigDecimal taxRate;
    private BigDecimal taxFixedAmount;

    //
    // Summary
    //

    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;

    public TaxCategoryTotalRequestPayload(TaxCategoryTotal taxCategoryTotal) {
        this.id = taxCategoryTotal.getId();
        this.taxCategory = taxCategoryTotal.getTaxCategory();
        
        // Use new decimal fields if available, otherwise fall back to old fields
        if (taxCategoryTotal instanceof TaxCategoryTotalWithNewDecimalFields) {
            TaxCategoryTotalWithNewDecimalFields totalWithNewFields = (TaxCategoryTotalWithNewDecimalFields) taxCategoryTotal;
            this.taxRate = totalWithNewFields.getTaxRate_new() != null ? 
                    totalWithNewFields.getTaxRate_new() : taxCategoryTotal.getTaxRate();
            this.taxFixedAmount = totalWithNewFields.getTaxFixedAmount_new() != null ? 
                    totalWithNewFields.getTaxFixedAmount_new() : taxCategoryTotal.getTaxFixedAmount();
            this.totalAmount = totalWithNewFields.getTotalAmount_new() != null ? 
                    totalWithNewFields.getTotalAmount_new() : taxCategoryTotal.getTotalAmount();
            this.netAmount = totalWithNewFields.getNetAmount_new() != null ? 
                    totalWithNewFields.getNetAmount_new() : taxCategoryTotal.getNetAmount();
            this.taxAmount = totalWithNewFields.getTaxAmount_new() != null ? 
                    totalWithNewFields.getTaxAmount_new() : taxCategoryTotal.getTaxAmount();
        } else {
            this.taxRate = taxCategoryTotal.getTaxRate();
            this.taxFixedAmount = taxCategoryTotal.getTaxFixedAmount();
            this.totalAmount = taxCategoryTotal.getTotalAmount();
            this.netAmount = taxCategoryTotal.getNetAmount();
            this.taxAmount = taxCategoryTotal.getTaxAmount();
        }
    }

    public TaxCategoryTotal toEntity(Document document) {
        return TaxCategoryTotal.builder()
                .id(id)
                .document(document)
                .taxCategory(taxCategory)
                .taxRate(taxRate)
                .taxFixedAmount(taxFixedAmount)
                .totalAmount(totalAmount)
                .netAmount(netAmount)
                .taxAmount(taxAmount)
                .build();
    }
}

package pt.jumia.services.bill.network.request;

import com.neovisionaries.i18n.CountryCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Address;
import pt.jumia.services.bill.domain.entities.Category;
import pt.jumia.services.bill.domain.entities.DeliveryDetails;
import pt.jumia.services.bill.domain.entities.Document;
import pt.jumia.services.bill.domain.entities.DocumentAggregate;
import pt.jumia.services.bill.domain.entities.DocumentFlow;
import pt.jumia.services.bill.domain.entities.DocumentLine;
import pt.jumia.services.bill.domain.entities.DocumentStatus;
import pt.jumia.services.bill.domain.entities.DocumentType;
import pt.jumia.services.bill.domain.entities.DocumentWithNewDecimalFields;
import pt.jumia.services.bill.domain.entities.IssuedReason;
import pt.jumia.services.bill.domain.entities.IssuedReasonCode;
import pt.jumia.services.bill.domain.entities.Issuer;
import pt.jumia.services.bill.domain.entities.IssuerType;
import pt.jumia.services.bill.domain.entities.ItemType;
import pt.jumia.services.bill.domain.entities.PaymentDetails;
import pt.jumia.services.bill.domain.entities.Receiver;
import pt.jumia.services.bill.domain.entities.ReceiverType;
import pt.jumia.services.bill.domain.entities.TaxCategory;
import pt.jumia.services.bill.domain.entities.TaxCategoryTotal;
import pt.jumia.services.bill.domain.entities.UnitOfMeasure;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DocumentRequestPayload {

    private UUID id;
    private DocumentStatus status;
    private String country;
    private String shop;
    private DocumentType type;
    private String sid;
    private DocumentFlow flow;
    private String generatedBy;
    private String referenceNumber;
    private String purchaseReferenceNumber;
    private LocalDateTime issuedDate;
    private String currency;
    private BigDecimal issuedToLocalCurrencyExchangeRate;
    private ReceiverRequestPayload receiver;
    private IssuerRequestPayload issuer;
    private String notes;
    private IssuedReasonRequestPaload issuedReason;
    private List<DocumentLineRequestPayload> lines;
    private Integer lineCount;
    private BigDecimal totalAmount;
    private BigDecimal netAmount;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalItemsDiscountAmount;
    private BigDecimal extraDiscountAmount;
    private List<TaxCategoryTotalRequestPayload> taxCategoryTotals;
    private PaymentDetailsRequestPayload payment;
    private DeliveryDetailsRequestPayload delivery;
    private String sourceType;
    private String postingGroup;
    private String transactionType;
    private String transactionProgress;

    public DocumentRequestPayload(DocumentAggregate documentAggregate) {

        Document document = documentAggregate.getDocument();

        this.id = document.getId();
        this.status = document.getStatus();
        this.country = document.getCountry().getAlpha2();
        this.shop = document.getShop();
        this.type = document.getType();
        this.sid = document.getSid();
        this.flow = document.getFlow();
        this.generatedBy = document.getGeneratedBy();
        this.referenceNumber = document.getReferenceNumber();
        this.purchaseReferenceNumber = document.getPurchaseReferenceNumber();
        this.issuedDate = document.getIssuedDate();
        this.currency = document.getCurrency().getCurrencyCode();

        // Use new decimal fields if available, otherwise fall back to old fields
        if (document instanceof DocumentWithNewDecimalFields) {
            DocumentWithNewDecimalFields docWithNewFields = (DocumentWithNewDecimalFields) document;
            this.issuedToLocalCurrencyExchangeRate = docWithNewFields.getIssuedToLocalCurrencyExchangeRate_new() != null ?
                    docWithNewFields.getIssuedToLocalCurrencyExchangeRate_new() : document.getIssuedToLocalCurrencyExchangeRate();
        } else {
            this.issuedToLocalCurrencyExchangeRate = document.getIssuedToLocalCurrencyExchangeRate();
        }

        this.receiver = new ReceiverRequestPayload(document.getReceiver());
        this.issuer = new IssuerRequestPayload(document.getIssuer());
        this.notes = document.getNotes();
        this.issuedReason = document.getIssuedReason() == null ? null : new IssuedReasonRequestPaload(document.getIssuedReason());
        this.lines = Objects.isNull(documentAggregate.getLines()) ? null :
                documentAggregate.getLines().stream().map(DocumentLineRequestPayload::new)
                        .collect(Collectors.toList());
        this.lineCount = document.getLineCount();

        // Use new decimal fields if available, otherwise fall back to old fields
        if (document instanceof DocumentWithNewDecimalFields) {
            DocumentWithNewDecimalFields docWithNewFields = (DocumentWithNewDecimalFields) document;
            this.totalAmount = docWithNewFields.getTotalAmount_new() != null ?
                    docWithNewFields.getTotalAmount_new() : document.getTotalAmount();
            this.netAmount = docWithNewFields.getNetAmount_new() != null ?
                    docWithNewFields.getNetAmount_new() : document.getNetAmount();
            this.taxAmount = docWithNewFields.getTaxAmount_new() != null ?
                    docWithNewFields.getTaxAmount_new() : document.getTaxAmount();
            this.discountAmount = docWithNewFields.getDiscountAmount_new() != null ?
                    docWithNewFields.getDiscountAmount_new() : document.getDiscountAmount();
            this.totalItemsDiscountAmount = docWithNewFields.getTotalItemsDiscountAmount_new() != null ?
                    docWithNewFields.getTotalItemsDiscountAmount_new() : document.getTotalItemsDiscountAmount();
            this.extraDiscountAmount = docWithNewFields.getExtraDiscountAmount_new() != null ?
                    docWithNewFields.getExtraDiscountAmount_new() : document.getExtraDiscountAmount();
        } else {
            this.totalAmount = document.getTotalAmount();
            this.netAmount = document.getNetAmount();
            this.taxAmount = document.getTaxAmount();
            this.discountAmount = document.getDiscountAmount();
            this.totalItemsDiscountAmount = document.getTotalItemsDiscountAmount();
            this.extraDiscountAmount = document.getExtraDiscountAmount();
        }

        this.taxCategoryTotals = Objects.isNull(documentAggregate.getTaxCategoryTotals()) ? null :
                documentAggregate.getTaxCategoryTotals().stream().map(TaxCategoryTotalRequestPayload::new)
                        .collect(Collectors.toList());
        this.payment = document.getPayment() == null ? null : new PaymentDetailsRequestPayload(document.getPayment());
        this.delivery = document.getDelivery() == null ? null : new DeliveryDetailsRequestPayload(document.getDelivery());
        this.sourceType = document.getSourceType();
        this.postingGroup = document.getPostingGroup();
        this.transactionType = document.getTransactionType();
        this.transactionProgress = document.getTransactionProgress();
    }

    @Data
    @NoArgsConstructor
    public static class ReceiverRequestPayload {

        UUID id;
        ReceiverType type;
        String legalName;
        String name;
        String nationalIdentificationNumber;
        String taxIdentificationNumber;
        String businessRegistrationNumber;
        AddressRequestPayload address;
        String email;
        String mobilePhone;
        String linePhone;

        public ReceiverRequestPayload(Receiver receiver) {

            this.id = receiver.getId();
            this.type = receiver.getType();
            this.legalName = receiver.getLegalName();
            this.name = receiver.getName();
            this.nationalIdentificationNumber = receiver.getNationalIdentificationNumber();
            this.taxIdentificationNumber = receiver.getTaxIdentificationNumber();
            this.businessRegistrationNumber = receiver.getBusinessRegistrationNumber();
            this.address = new AddressRequestPayload(receiver.getAddress());
            this.email = receiver.getEmail();
            this.mobilePhone = receiver.getMobilePhone();
            this.linePhone = receiver.getLinePhone();
        }
    }

    @Data
    @NoArgsConstructor
    public static class IssuerRequestPayload {

        UUID id;
        IssuerType type;
        String legalName;
        String name;
        String taxIdentificationNumber;
        String businessRegistrationNumber;
        String branch;
        AddressRequestPayload address;
        String email;
        String mobilePhone;
        String linePhone;

        public IssuerRequestPayload(Issuer issuer) {

            this.id = issuer.getId();
            this.type = issuer.getType();
            this.legalName = issuer.getLegalName();
            this.name = issuer.getName();
            this.taxIdentificationNumber = issuer.getTaxIdentificationNumber();
            this.businessRegistrationNumber = issuer.getBusinessRegistrationNumber();
            this.branch = issuer.getBranch();
            this.address = new AddressRequestPayload(issuer.getAddress());
            this.email = issuer.getEmail();
            this.mobilePhone = issuer.getMobilePhone();
            this.linePhone = issuer.getLinePhone();
        }
    }

    @Data
    @NoArgsConstructor
    public static class AddressRequestPayload {

        String country;
        String region;
        String city;
        String street;
        String buildingNumber;
        String floor;
        String postalCode;
        String additionalInformation;

        public AddressRequestPayload(Address address) {

            this.country = address.getCountry() == null ? null : address.getCountry().getAlpha2();
            this.region = address.getRegion();
            this.city = address.getCity();
            this.street = address.getStreet() == null ? "-" : address.getStreet();
            this.buildingNumber = address.getBuildingNumber() == null ? "-" : address.getBuildingNumber();
            this.floor = address.getFloor() == null ? "-" : address.getFloor();
            this.postalCode = address.getPostalCode() == null ? "-" : address.getPostalCode();
            this.additionalInformation = address.getAdditionalInformation() == null ?
                    "-" : address.getAdditionalInformation();
        }
    }

    @Data
    @NoArgsConstructor
    public static class DocumentLineRequestPayload {

        UUID id;
        Integer position;
        BigDecimal quantity;
        UnitOfMeasure unitOfMeasure;
        String itemCode;
        String itemName;
        ItemType itemType;
        CategoryRequestPayload category;
        BigDecimal unitPrice;
        BigDecimal totalAmount;
        BigDecimal netAmount;
        BigDecimal totalTaxAmount;
        List<AppliedTaxRequestPayload> appliedTaxes;
        DiscountRequestPayload discount;
        String unitOfPackage;

        public DocumentLineRequestPayload(DocumentLine documentLine) {

            this.id = documentLine.getId();
            this.position = documentLine.getPosition();
            this.quantity = documentLine.getQuantity();
            this.unitOfMeasure = documentLine.getUnitOfMeasure();
            this.itemCode = documentLine.getItemCode();
            this.itemName = documentLine.getItemName();
            this.itemType = documentLine.getItemType();
            this.category = new CategoryRequestPayload(documentLine.getCategory());
            this.unitPrice = documentLine.getUnitPrice();
            this.totalAmount = documentLine.getTotalAmount();
            this.netAmount = documentLine.getNetAmount();
            this.totalTaxAmount = documentLine.getTotalTaxAmount();
            this.appliedTaxes = documentLine.getAppliedTaxes().stream().map(AppliedTaxRequestPayload::new)
                    .collect(Collectors.toList());
            this.discount = documentLine.getDiscount() == null ? null : new DiscountRequestPayload(documentLine.getDiscount());
            this.unitOfPackage = documentLine.getUnitOfPackage();
        }

        @Data
        @NoArgsConstructor
        public static class DiscountRequestPayload {
            BigDecimal amount;
            BigDecimal rate;

            public DiscountRequestPayload(DocumentLine.Discount discount) {
                this.amount = discount.getAmount();
                this.rate = discount.getRate();
            }
        }

        @Data
        @NoArgsConstructor
        public static class CategoryRequestPayload {

            String sid;
            String name;
            String taxAuthorityCode;

            public CategoryRequestPayload(Category category) {

                this.sid = category.getSid();
                this.name = category.getName();
                this.taxAuthorityCode = category.getTaxAuthorityCode();
            }
        }

        @Data
        @NoArgsConstructor
        public static class AppliedTaxRequestPayload {

            TaxCategory taxCategory;
            BigDecimal taxRate;
            BigDecimal taxFixedAmount;
            BigDecimal taxAmount;

            public AppliedTaxRequestPayload(DocumentLine.AppliedTax appliedTax) {

                this.taxCategory = appliedTax.getTaxCategory();
                this.taxRate = appliedTax.getTaxRate();
                this.taxFixedAmount = appliedTax.getTaxFixedAmount();
                this.taxAmount = appliedTax.getTaxAmount();
            }
        }
    }

    @Data
    @NoArgsConstructor
    public static class TaxCategoryTotalRequestPayload {

        UUID id;
        TaxCategory taxCategory;
        BigDecimal taxRate;
        BigDecimal taxFixedAmount;
        BigDecimal totalAmount;
        BigDecimal netAmount;
        BigDecimal taxAmount;

        public TaxCategoryTotalRequestPayload(TaxCategoryTotal taxCategoryTotal) {

            this.id = taxCategoryTotal.getId();
            this.taxCategory = taxCategoryTotal.getTaxCategory();
            this.taxRate = taxCategoryTotal.getTaxRate();
            this.taxFixedAmount = taxCategoryTotal.getTaxFixedAmount();
            this.totalAmount = taxCategoryTotal.getTotalAmount();
            this.netAmount = taxCategoryTotal.getNetAmount();
            this.taxAmount = taxCategoryTotal.getTaxAmount();
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder(toBuilder = true)
    public static class IssuedReasonRequestPaload {
        private String code;
        private String notes;

        public IssuedReasonRequestPaload(IssuedReason entity) {
            this.code = entity.getCode() != null ? entity.getCode().name() : null;
            this.notes = entity.getNotes();
        }

        public IssuedReason toEntity() {
            return IssuedReason.builder()
                    .code(IssuedReasonCode.valueOf(code))
                    .notes(notes)
                    .build();
        }
    }

    @Data
    @NoArgsConstructor
    public static class PaymentDetailsRequestPayload {

        private UUID id;
        private String bankName;
        private String bankAddress;
        private String bankAccountNo;
        private String bankAccountIBAN;
        private String swiftCode;
        private String terms;
        private String createdBy;
        private LocalDateTime createdAt;

        public PaymentDetailsRequestPayload(PaymentDetails entity) {
            this.id = entity.getId();
            this.bankName = entity.getBankName();
            this.bankAddress = entity.getBankAddress();
            this.bankAccountNo = entity.getBankAccountNo();
            this.bankAccountIBAN = entity.getBankAccountIBAN();
            this.swiftCode = entity.getSwiftCode();
            this.terms = entity.getTerms();
        }
    }

    @Data
    @NoArgsConstructor
    public static class DeliveryDetailsRequestPayload {

        private UUID id;
        private String approach;
        private String packaging;
        private LocalDateTime dateValidity;
        private String exportPort;
        private BigDecimal grossWeight;
        private BigDecimal netWeight;
        private CountryCode countryOfOrigin;
        private String terms;

        public DeliveryDetailsRequestPayload(DeliveryDetails entity) {
            this.id = entity.getId();
            this.approach = entity.getApproach();
            this.packaging = entity.getPackaging();
            this.dateValidity = entity.getDateValidity();
            this.exportPort = entity.getExportPort();
            this.grossWeight = entity.getGrossWeight();
            this.netWeight = entity.getNetWeight();
            this.countryOfOrigin = entity.getCountryOfOrigin();
            this.terms = entity.getTerms();
        }
    }
}

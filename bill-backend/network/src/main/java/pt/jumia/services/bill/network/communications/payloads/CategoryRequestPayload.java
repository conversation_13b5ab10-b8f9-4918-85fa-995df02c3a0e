package pt.jumia.services.bill.network.communications.payloads;

import lombok.Data;
import lombok.NoArgsConstructor;
import pt.jumia.services.bill.domain.entities.Category;

@Data
@NoArgsConstructor
public class CategoryRequestPayload {

    private String sid;
    private String name;
    private String taxAuthorityCode;

    public CategoryRequestPayload(Category category) {
        this.sid = category.getSid();
        this.name = category.getName();
        this.taxAuthorityCode = category.getTaxAuthorityCode();
    }

    public Category toEntity() {
        return Category.builder()
                .sid(sid)
                .name(name)
                .taxAuthorityCode(taxAuthorityCode)
                .build();
    }
}

buildscript {
    ext {
        springBootVersion = '2.4.5'
        springVersion = '5.3.6'
        junitVersion = '5.7.1'
        mockitoVersion = '3.9.0'
        lombokVersion = '1.18.20'
        spotbugsVersion = '4.2.3'
        spotbugsGradlePluginVersion = '4.7.0'
        cpdVersion = '3.2'
        aclLibVersion = '2.1.0-RELEASE'
        aclMigratorVersion = '0.5.0-RELEASE'
        log4jVersion = '2.16.0'
        kafkaVersion = '2.7.6'
    }
    repositories {
        //Apply build type
        if (!project.hasProperty("environment") || project.environment == "local") {
            mavenCentral()
            maven {
                url "https://plugins.gradle.org/m2/"
            }
            // workaround since class/methods not accessible at this point
            println "\u001B[34m[INFO]\u001B[0m Build script set to local repositories"
        } else {
            maven {
                credentials {
                    username "${nexusUsername}"
                    password "${nexusPassword}"
                }
                url "${nexusUrl}${nexusPath}"
            }
            maven {
                url "https://plugins.gradle.org/m2/"
            }
            println "\u001B[34m[INFO]\u001B[0m Build script set to nexus repositories"
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath "de.aaschmid:gradle-cpd-plugin:${cpdVersion}"
        classpath 'com.palantir.gradle.docker:gradle-docker:0.24.0'
        classpath "gradle.plugin.com.github.spotbugs.snom:spotbugs-gradle-plugin:${spotbugsGradlePluginVersion}"
    }
}

plugins {
    id "com.avast.gradle.docker-compose" version "0.7.1"
}

allprojects {
    apply plugin: 'java'
    apply plugin: 'java-library'

    repositories {
        maven {
            credentials {
                username "${nexusUsername}"
                password "${nexusPassword}"
            }
            url "${nexusUrl}${nexusPath}"
        }
    }

    dependencies {
        // Apache utils
        implementation "org.apache.commons:commons-lang3:3.12.0"

        //lombok for payload POJOs
        compileOnly "org.projectlombok:lombok:${lombokVersion}"
        testCompileOnly "org.projectlombok:lombok:${lombokVersion}"
        annotationProcessor "org.projectlombok:lombok:${lombokVersion}"
        testAnnotationProcessor "org.projectlombok:lombok:${lombokVersion}"

//        testImplementation "org.mockito:mockito-core:${mockitoVersion}"
        testImplementation "org.mockito:mockito-junit-jupiter:${mockitoVersion}"
        testImplementation "org.junit.jupiter:junit-jupiter-api:${junitVersion}"
        testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:${junitVersion}"
        testImplementation "org.assertj:assertj-core:3.19.0"
        //enable a logging framework in the tests
        testImplementation group: 'org.slf4j', name: 'slf4j-simple', version: '1.7.30'
    }

    test {
        useJUnitPlatform()
    }
}

apply plugin: 'org.springframework.boot'
apply from: 'config/quality/quality.gradle'
apply plugin: "com.palantir.docker"

defaultTasks 'bootRun'

bootRun.ext.activeProfiles = 'default'
bootRun {
    doFirst {
        jvmArgs = ["-Dspring.profiles.active=" + ext.activeProfiles]
    }
}

sourceCompatibility = 11

def majorVersion = 0
def minorVersion = 78
def patchVersion = 0
version sprintf("%s.%s.%s", majorVersion, minorVersion, patchVersion)
println "Building Bill Backend v" + version
updateApplicationProperties()

//defaults
ext {
    defaultProfile = "local"
    defaultBuildType = "RELEASE"
}

//Check for input build type, if not provided assume snapshot build type
if (!project.hasProperty("buildType")) {
    ext.buildType = ext.defaultBuildType
    logger.info(sprintf("No build type provided usind default value %s", ext.defaultBuildType))
}

//Validate build types
version sprintf("%s.%s.%s-%s", majorVersion, minorVersion, patchVersion, ext.buildType)
logger.info(sprintf("Building artifact version %s", version))

//Check for input environment, if not provided assume local build
if (!project.hasProperty("environment")) {
    ext.environment = ext.defaultProfile
    logger.info(sprintf("No enviroment build input provided usind default value %s", ext.defaultProfile))
} else {
    logger.info(sprintf("Using  enviroment '%s' profile", ext.environment))
}

//load file name
def buildScript = sprintf("config/build-%s.gradle", environment)

//Throw exception in case of invalid option
if (!file(buildScript).exists()) {
    throw new GradleException(sprintf("Unknown environment: %s", environment))
}

logger.info(sprintf("Loading custom build '%s'", buildScript))

//setup profile script file
apply from: buildScript


dependencies {
    implementation project(':domain')
    implementation project(':api')
    implementation project(':data')
    implementation project(':network')

    implementation(platform("org.apache.logging.log4j:log4j-bom:${log4jVersion}"))
    implementation "org.springframework.boot:spring-boot-starter-log4j2:${springBootVersion}"
    implementation("org.springframework.boot:spring-boot-starter-actuator:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}") {
        exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
    }

    //rest assured for endpoint testing
    testImplementation "io.rest-assured:rest-assured:3.0.2"

    testCompile group: 'org.springframework.kafka', name: 'spring-kafka-test', version: "${kafkaVersion}"
    testCompile group: 'com.fasterxml.jackson.module', name: 'jackson-module-scala_3', version: '2.13.0-rc1'
}

dockerCompose.isRequiredBy(test)
dockerCompose {
    useComposeFiles = ['./../dockers/docker-compose.test.yml']
    startedServices = ['bill-database-test']
}

def updateApplicationProperties() {
    def configFile = file('src/main/resources/application.yml')
    println "updating version to '${version}' in ${configFile}"
    String configContent = configFile.getText('UTF-8')
    configContent = configContent.replaceAll(/info\.build\.version: .*/, "info.build.version: ${version}")
    configFile.write(configContent, 'UTF-8')
}

// Process resources in order to turn gradle properties available in the application.properties file
processResources {
    filesMatching('**/*.properties') { expand(project.properties) }
}

def getGitBranchForDockerTag() {
    def branchName = System.getenv('CHANGE_BRANCH')
    if (!branchName) {
        return "latest"
    }
    return branchName.replaceAll(".+/", "") + "-" + ext.buildType
}

def branchName = getGitBranchForDockerTag()
def dockerRegistry = System.getenv('DOCKER_REGISTRY') ? System.getenv('DOCKER_REGISTRY') : 'nexus.dev.js:8084';

docker {
    name "${dockerRegistry}/bill"
    tags "${version}", "latest", "${branchName}"
    copySpec.from("build/libs").into("app")
    files 'rev.txt'
    buildArgs([NEXUS: "${dockerRegistry}"])
    dockerfile file('../dockers/bill-backend')
}

int readFileLineByLine(String filePath) {
    File file = new File(filePath)
    def line, noOfLines = 0;
    file.withReader { reader ->
        while ((line = reader.readLine()) != null) {
            println "${line}"
            noOfLines++
        }
    }
    return noOfLines
}


